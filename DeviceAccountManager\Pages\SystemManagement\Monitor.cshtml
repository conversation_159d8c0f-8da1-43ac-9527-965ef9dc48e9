@page
@model DeviceAccountManager.Pages.SystemManagement.MonitorModel
@{
    ViewData["Title"] = "系统监控";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-desktop me-2"></i>系统监控</h2>
                <div>
                    <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>刷新数据
                    </button>
                    <div class="form-check form-switch d-inline-block ms-3">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label" for="autoRefresh">自动刷新</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted">系统状态</h6>
                            <h4 class="text-success" id="systemStatus">运行中</h4>
                        </div>
                        <i class="fas fa-server fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted">运行时间</h6>
                            <h4 class="text-info" id="uptime">@Model.SystemUptime</h4>
                        </div>
                        <i class="fas fa-clock fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted">在线用户</h6>
                            <h4 class="text-warning" id="onlineUsers">@Model.OnlineUsers</h4>
                        </div>
                        <i class="fas fa-users fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted">今日请求</h6>
                            <h4 class="text-success" id="todayRequests">@Model.TodayRequests</h4>
                        </div>
                        <i class="fas fa-chart-line fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务状态 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>服务状态</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="serviceStatus">
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">●</span>
                                <span>数据库服务</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">●</span>
                                <span>邮件服务</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">●</span>
                                <span>备份服务</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">●</span>
                                <span>后台任务</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-hdd me-2"></i>系统资源</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>内存使用率</span>
                            <span id="memoryUsage">65%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 65%" id="memoryBar"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>CPU使用率</span>
                            <span id="cpuUsage">25%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" id="cpuBar"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>磁盘使用率</span>
                            <span id="diskUsage">45%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 45%" id="diskBar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时活动日志 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>实时活动日志</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>对象</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="realtimeActivities">
                                @foreach (var activity in Model.RecentActivities)
                                {
                                    <tr>
                                        <td>@activity.CreatedAt.ToString("HH:mm:ss")</td>
                                        <td>@(activity.User?.Username ?? "系统")</td>
                                        <td>@activity.Action</td>
                                        <td>@activity.EntityName</td>
                                        <td>
                                            <span class="badge bg-success">成功</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能图表 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>请求量趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="requestChart" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>用户活动分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let autoRefreshInterval;
        let requestChart, activityChart;

        $(document).ready(function() {
            initializeCharts();
            startAutoRefresh();
            
            $('#autoRefresh').change(function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
        });

        function initializeCharts() {
            // 请求量趋势图
            const requestCtx = document.getElementById('requestChart').getContext('2d');
            requestChart = new Chart(requestCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: '请求数',
                        data: [12, 19, 25, 35, 28, 22],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 用户活动分布图
            const activityCtx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(activityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['登录', '查看', '创建', '更新', '删除'],
                    datasets: [{
                        data: [30, 25, 20, 15, 10],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        function refreshData() {
            $.get('/api/system/monitor', function(data) {
                if (data.success) {
                    updateSystemStatus(data.data);
                }
            }).fail(function() {
                console.error('获取监控数据失败');
            });
        }

        function updateSystemStatus(data) {
            $('#uptime').text(data.uptime || '@Model.SystemUptime');
            $('#onlineUsers').text(data.onlineUsers || '@Model.OnlineUsers');
            $('#todayRequests').text(data.todayRequests || '@Model.TodayRequests');
            
            // 更新资源使用率
            if (data.resources) {
                updateResourceBar('memory', data.resources.memory || 65);
                updateResourceBar('cpu', data.resources.cpu || 25);
                updateResourceBar('disk', data.resources.disk || 45);
            }
        }

        function updateResourceBar(type, percentage) {
            $(`#${type}Usage`).text(percentage + '%');
            $(`#${type}Bar`).css('width', percentage + '%');
        }

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(refreshData, 30000); // 每30秒刷新
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }
    </script>
}
