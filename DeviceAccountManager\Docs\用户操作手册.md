# 生产设备账号权限管理系统 - 用户操作手册

## 📋 手册信息

- **手册版本**: v1.0
- **适用系统**: 生产设备账号权限管理系统 v1.0
- **目标用户**: 系统管理员、工艺员、维护者、操作者
- **最后更新**: 2025-01-01

## 🚀 快速入门

### 系统访问
1. 打开浏览器，访问系统地址：`http://localhost:5000`
2. 在登录页面输入用户名和密码
3. 点击"登录"按钮进入系统

### 默认管理员账户
- **用户名**: admin
- **密码**: Admin123!
- **权限**: 超级管理员

⚠️ **重要提醒**: 首次登录后请立即修改默认密码！

## 👥 用户权限说明

### 权限级别
系统采用四级权限体系：

| 权限级别 | 中文名称 | 权限范围 |
|----------|----------|----------|
| SuperAdmin | 超级管理员 | 所有功能的完全访问权限 |
| Technician | 工艺员 | 账户管理、密码管理、员工管理 |
| Maintainer | 维护者 | 查看账户信息、修改自己的密码 |
| Operator | 操作者 | 仅查看自己的账户信息 |

### 功能权限矩阵

| 功能模块 | SuperAdmin | Technician | Maintainer | Operator |
|----------|------------|------------|------------|----------|
| 账户管理 | ✅ 完全权限 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 |
| 员工管理 | ✅ 完全权限 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 |
| 密码管理 | ✅ 完全权限 | ✅ 完全权限 | 🔸 仅自己 | 🔸 仅自己 |
| 邮件管理 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 | ❌ 无权限 |
| 系统管理 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 | ❌ 无权限 |

## 📱 主要功能操作指南

### 1. 账户管理

#### 1.1 查看账户列表
1. 登录系统后，点击导航菜单"账户管理"
2. 系统显示所有用户账户列表
3. 可以按用户名、邮箱、角色等条件筛选

#### 1.2 创建新账户
1. 在账户管理页面，点击"添加账户"按钮
2. 填写账户信息：
   - **用户名**: 3-50个字符，不能重复
   - **邮箱**: 有效的邮箱地址
   - **角色**: 选择适当的权限级别
   - **关联员工**: 选择对应的员工（可选）
3. 点击"保存"按钮创建账户
4. 系统自动生成安全密码并发送邮件通知

#### 1.3 编辑账户信息
1. 在账户列表中找到要编辑的账户
2. 点击"编辑"按钮
3. 修改需要更改的信息
4. 点击"保存"按钮确认修改

#### 1.4 禁用/启用账户
1. 在账户列表中找到目标账户
2. 点击"禁用"或"启用"按钮
3. 确认操作后，账户状态立即生效

#### 1.5 删除账户
1. 在账户列表中找到要删除的账户
2. 点击"删除"按钮
3. 在确认对话框中点击"确定"
4. ⚠️ **注意**: 删除操作不可恢复

### 2. 员工管理

#### 2.1 查看员工信息
1. 点击导航菜单"员工管理"
2. 查看员工列表和详细信息
3. 可按部门、姓名等条件筛选

#### 2.2 添加员工
1. 在员工管理页面，点击"添加员工"
2. 填写员工信息：
   - **员工编号**: 唯一标识，不能重复
   - **姓名**: 员工真实姓名
   - **部门**: 所属部门
   - **职位**: 工作职位
   - **邮箱**: 联系邮箱
   - **电话**: 联系电话
   - **入职日期**: 员工入职时间
3. 点击"保存"按钮

#### 2.3 批量导入员工
1. 点击"批量导入"按钮
2. 下载Excel模板文件
3. 按模板格式填写员工信息
4. 上传填写好的Excel文件
5. 系统验证数据后批量导入

#### 2.4 编辑员工信息
1. 在员工列表中找到要编辑的员工
2. 点击"编辑"按钮
3. 修改相关信息
4. 点击"保存"按钮

### 3. 密码管理

#### 3.1 查看密码策略
1. 点击导航菜单"密码管理" > "密码策略"
2. 查看当前密码规则设置：
   - 最小长度要求
   - 复杂度要求
   - 历史密码限制
   - 有效期设置

#### 3.2 生成新密码
1. 在密码管理页面，点击"生成密码"
2. 设置密码参数：
   - **长度**: 8-32个字符
   - **包含大写字母**: 建议勾选
   - **包含小写字母**: 建议勾选
   - **包含数字**: 建议勾选
   - **包含特殊字符**: 建议勾选
3. 点击"生成"按钮
4. 复制生成的密码

#### 3.3 批量更改密码
1. 点击"批量密码更改"
2. 选择要更改密码的账户
3. 选择密码生成规则
4. 点击"执行更改"
5. 系统自动生成新密码并发送邮件通知

#### 3.4 导出密码清单
1. 在密码管理页面，点击"导出密码"
2. 选择导出范围：
   - 全部账户
   - 指定部门
   - 指定设备组
3. 选择导出格式（Excel）
4. 点击"导出"按钮下载文件
5. ⚠️ **安全提醒**: 导出文件包含敏感信息，请妥善保管

### 4. 邮件管理（仅超级管理员）

#### 4.1 邮件服务设置
1. 点击导航菜单"邮件管理" > "邮件设置"
2. 配置SMTP服务器信息：
   - **SMTP服务器**: 邮件服务器地址
   - **端口**: 通常为587或25
   - **用户名**: 邮箱账户
   - **密码**: 邮箱密码
   - **启用SSL**: 建议启用
3. 点击"测试连接"验证设置
4. 点击"保存"按钮

#### 4.2 查看邮件日志
1. 点击"邮件日志"查看发送记录
2. 可按状态、时间、收件人筛选
3. 查看邮件发送详情和错误信息

#### 4.3 邮件模板管理
1. 点击"邮件模板"管理邮件内容
2. 编辑密码通知邮件模板
3. 支持变量替换：{用户名}、{密码}、{有效期}等
4. 预览邮件效果后保存

### 5. 系统管理（仅超级管理员）

#### 5.1 系统概览
1. 点击导航菜单"系统管理"
2. 查看系统运行状态：
   - 系统信息
   - 用户统计
   - 最近活动
   - 系统健康状态

#### 5.2 系统监控
1. 点击"系统监控"查看实时状态
2. 监控内容包括：
   - CPU使用率
   - 内存使用率
   - 磁盘空间
   - 网络状态
   - 服务状态

#### 5.3 系统配置
1. 点击"系统配置"修改系统参数
2. 配置分类：
   - **系统设置**: 会话超时、登录限制等
   - **密码策略**: 密码规则、有效期等
   - **邮件设置**: SMTP配置、模板设置
   - **安全设置**: 审计日志、访问控制
   - **备份设置**: 自动备份、保留策略

#### 5.4 备份管理
1. 点击"备份管理"进行数据备份
2. 创建手动备份：
   - 输入备份名称和描述
   - 选择备份类型
   - 点击"创建备份"
3. 查看备份历史和状态
4. 下载或删除备份文件

#### 5.5 审计日志
1. 点击"审计日志"查看操作记录
2. 可按操作类型、用户、时间筛选
3. 查看详细的操作信息
4. 导出审计报告

## 🔧 常用操作技巧

### 快捷键
- **Ctrl + F**: 页面内搜索
- **Ctrl + R**: 刷新页面
- **Esc**: 关闭模态对话框

### 搜索技巧
- 支持模糊搜索
- 可使用通配符 * 和 ?
- 支持多关键词搜索（空格分隔）

### 数据导出
- 支持Excel格式导出
- 导出文件自动包含时间戳
- 大数据量分批导出

### 批量操作
- 使用复选框选择多个项目
- 支持全选/反选操作
- 批量操作前会显示确认对话框

## ⚠️ 安全注意事项

### 密码安全
1. 定期更改密码（建议90天）
2. 不要使用弱密码或常见密码
3. 不要在多个系统使用相同密码
4. 妥善保管密码，不要泄露给他人

### 账户安全
1. 及时注销不再使用的账户
2. 定期检查账户权限设置
3. 监控异常登录活动
4. 启用账户锁定策略

### 数据安全
1. 定期备份重要数据
2. 限制敏感信息的访问权限
3. 不要在不安全的网络环境下操作
4. 及时更新系统补丁

### 操作安全
1. 重要操作前仔细确认
2. 不要在公共场所操作敏感功能
3. 操作完成后及时退出系统
4. 发现异常情况及时报告

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: [<EMAIL>]
- **紧急联系电话**: [联系电话]
- **工作时间**: 周一至周五 9:00-18:00

### 问题反馈
1. 详细描述问题现象
2. 提供操作步骤和截图
3. 说明使用的浏览器和版本
4. 提供错误信息（如有）

### 培训支持
- 新用户培训
- 功能更新培训
- 操作技巧分享
- 最佳实践指导

---

**手册更新**: 本手册随系统版本更新，请关注最新版本。如有疑问，请联系技术支持。
