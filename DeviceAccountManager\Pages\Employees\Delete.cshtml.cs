using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages.Employees
{
    public class DeleteModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        [BindProperty]
        [Required(ErrorMessage = "请选择删除原因")]
        public string DeleteReason { get; set; } = string.Empty;

        [BindProperty]
        public string DeleteNotes { get; set; } = string.Empty;

        [BindProperty]
        public string HandleRelatedAccounts { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请确认删除操作")]
        public bool ConfirmDelete { get; set; }

        public int RelatedAccountsCount { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限：只有工艺员及以上级别可以删除员工
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees.FirstOrDefaultAsync(m => m.Id == id);
            if (employee == null)
            {
                return NotFound();
            }

            Employee = employee;

            // 获取关联账户数量
            RelatedAccountsCount = await _context.Accounts
                .CountAsync(a => a.EmployeeId == id);

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            // 重新获取关联账户数量用于验证
            RelatedAccountsCount = await _context.Accounts
                .CountAsync(a => a.EmployeeId == id);

            // 如果有关联账户，必须选择处理方式
            if (RelatedAccountsCount > 0 && string.IsNullOrEmpty(HandleRelatedAccounts))
            {
                ModelState.AddModelError("HandleRelatedAccounts", "存在关联账户时必须选择处理方式");
            }

            if (!ModelState.IsValid)
            {
                // 重新加载数据
                var employeeForReload = await _context.Employees.FirstOrDefaultAsync(m => m.Id == id);
                if (employeeForReload == null)
                {
                    return NotFound();
                }
                Employee = employeeForReload;
                return Page();
            }

            var employee = await _context.Employees.FirstOrDefaultAsync(e => e.Id == id);
            if (employee == null)
            {
                return NotFound();
            }

            // 处理关联账户
            var relatedAccounts = await _context.Accounts
                .Where(a => a.EmployeeId == employee.Id)
                .ToListAsync();

            foreach (var account in relatedAccounts)
            {
                switch (HandleRelatedAccounts)
                {
                    case "Unbind":
                        account.EmployeeId = null;
                        account.UpdatedAt = DateTime.Now;
                        break;
                    case "Disable":
                        account.EmployeeId = null;
                        account.IsActive = false;
                        account.UpdatedAt = DateTime.Now;
                        break;
                    case "Delete":
                        // 删除账户的密码历史和邮件日志
                        var passwordHistories = await _context.PasswordHistories
                            .Where(ph => ph.AccountId == account.Id)
                            .ToListAsync();
                        _context.PasswordHistories.RemoveRange(passwordHistories);

                        var emailLogs = await _context.EmailLogs
                            .Where(el => el.AccountId == account.Id)
                            .ToListAsync();
                        _context.EmailLogs.RemoveRange(emailLogs);

                        _context.Accounts.Remove(account);
                        break;
                }
            }

            // 记录删除操作日志
            var deleteLog = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "Delete",
                TargetType = "Employee",
                TargetId = employee.Id.ToString(),
                Description = $"删除员工：{employee.Name} ({employee.EmployeeCode})，原因：{DeleteReason}，关联账户处理：{HandleRelatedAccounts}，备注：{DeleteNotes}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(deleteLog);

            // 删除员工
            _context.Employees.Remove(employee);

            await _context.SaveChangesAsync();

            // 如果员工有邮箱，发送删除通知邮件
            if (!string.IsNullOrEmpty(employee.Email))
            {
                var emailLog = new EmailLog
                {
                    ToEmail = employee.Email,
                    ToName = employee.Name,
                    Subject = "员工信息已被删除",
                    Content = $"您好 {employee.Name}，\n\n您的员工信息已于 {DateTime.Now:yyyy-MM-dd HH:mm} 从系统中删除。\n\n删除原因：{DeleteReason}\n\n如有疑问，请联系系统管理员。\n\n此邮件由系统自动发送，请勿回复。",
                    Status = "Pending",
                    CreatedAt = DateTime.Now,
                    EmailType = "EmployeeDeleted"
                };
                _context.EmailLogs.Add(emailLog);
                await _context.SaveChangesAsync();
            }

            return RedirectToPage("./Index");
        }
    }
}
