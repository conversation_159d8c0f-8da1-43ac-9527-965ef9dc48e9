@page
@model DeviceAccountManager.Pages.SystemManagement.IndexModel
@{
    ViewData["Title"] = "系统管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">系统管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Configuration" class="btn btn-sm btn-primary">
                <i class="bi bi-gear"></i> 系统配置
            </a>
            <a asp-page="./AuditLogs" class="btn btn-sm btn-info">
                <i class="bi bi-journal-text"></i> 审计日志
            </a>
            <a asp-page="./Backup" class="btn btn-sm btn-success">
                <i class="bi bi-archive"></i> 备份管理
            </a>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
        @Model.Message
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="row">
    <!-- 系统信息 -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-info-circle"></i> 系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-4"><strong>系统名称：</strong></div>
                    <div class="col-sm-8">@Model.SystemName</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-4"><strong>系统版本：</strong></div>
                    <div class="col-sm-8">@Model.SystemVersion</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-4"><strong>运行时间：</strong></div>
                    <div class="col-sm-8">@Model.SystemUptime</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-4"><strong>服务器时间：</strong></div>
                    <div class="col-sm-8">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-4"><strong>维护模式：</strong></div>
                    <div class="col-sm-8">
                        @if (Model.MaintenanceMode)
                        {
                            <span class="badge bg-warning">启用</span>
                        }
                        else
                        {
                            <span class="badge bg-success">正常</span>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统统计 -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-bar-chart"></i> 系统统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">@Model.TotalDevices</h4>
                            <small class="text-muted">设备总数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@Model.TotalAccounts</h4>
                        <small class="text-muted">账户总数</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning">@Model.TotalEmployees</h4>
                            <small class="text-muted">员工总数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">@Model.ActiveUsers</h4>
                        <small class="text-muted">活跃用户</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近活动 -->
    <div class="col-md-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-activity"></i> 最近活动
                </h6>
            </div>
            <div class="card-body">
                @if (Model.RecentActivities.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>对象</th>
                                    <th>描述</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var activity in Model.RecentActivities)
                                {
                                    <tr>
                                        <td>
                                            <small>@activity.CreatedAt.ToString("MM-dd HH:mm")</small>
                                        </td>
                                        <td>
                                            <small>@activity.User.Username</small>
                                        </td>
                                        <td>
                                            @switch (activity.Action)
                                            {
                                                case "Create":
                                                    <span class="badge bg-success">创建</span>
                                                    break;
                                                case "Update":
                                                    <span class="badge bg-info">更新</span>
                                                    break;
                                                case "Delete":
                                                    <span class="badge bg-danger">删除</span>
                                                    break;
                                                case "Login":
                                                    <span class="badge bg-primary">登录</span>
                                                    break;
                                                case "Logout":
                                                    <span class="badge bg-secondary">退出</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-light text-dark">@activity.Action</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            <small>@activity.EntityType</small>
                                        </td>
                                        <td>
                                            <small>@activity.Description</small>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <p class="text-muted text-center">暂无活动记录</p>
                }
                <div class="text-end">
                    <a asp-page="./AuditLogs" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统健康状态 -->
    <div class="col-md-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="bi bi-heart-pulse"></i> 系统健康
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>数据库连接</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>邮件服务</span>
                        <span class="badge bg-@(Model.EmailServiceStatus ? "success" : "danger")">
                            @(Model.EmailServiceStatus ? "正常" : "异常")
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>备份服务</span>
                        <span class="badge bg-@(Model.BackupServiceStatus ? "success" : "warning")">
                            @(Model.BackupServiceStatus ? "正常" : "待配置")
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>磁盘空间</span>
                        <span class="badge bg-success">充足</span>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="checkSystemHealth()">
                        <i class="bi bi-arrow-clockwise"></i> 检查状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-secondary">
                    <i class="bi bi-lightning"></i> 快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="toggleMaintenanceMode()">
                            <i class="bi bi-tools"></i><br>
                            <small>@(Model.MaintenanceMode ? "退出维护模式" : "进入维护模式")</small>
                        </button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a asp-page="./Backup" class="btn btn-outline-success w-100">
                            <i class="bi bi-archive"></i><br>
                            <small>备份管理</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a asp-page="./AuditLogs" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-journal-text"></i><br>
                            <small>审计日志</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-outline-warning w-100" onclick="cleanupLogs()">
                            <i class="bi bi-trash"></i><br>
                            <small>清理日志</small>
                        </button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a asp-page="./Configuration" class="btn btn-outline-info w-100">
                            <i class="bi bi-gear"></i><br>
                            <small>系统设置</small>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-outline-danger w-100" onclick="restartSystem()">
                            <i class="bi bi-arrow-clockwise"></i><br>
                            <small>重启系统</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function checkSystemHealth() {
            // 这里可以添加系统健康检查的AJAX调用
            alert('系统健康检查功能开发中...');
        }

        function toggleMaintenanceMode() {
            if (confirm('@(Model.MaintenanceMode ? "确定要退出维护模式吗？" : "确定要进入维护模式吗？这将影响所有用户的访问。")')) {
                fetch('/api/system/maintenance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ enabled: @(!Model.MaintenanceMode).ToString().ToLower() })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('操作失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败');
                });
            }
        }

        function cleanupLogs() {
            if (confirm('确定要清理90天前的审计日志吗？此操作不可恢复。')) {
                fetch('/api/system/cleanup-logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('日志清理完成：' + data.message);
                    } else {
                        alert('清理失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('清理失败');
                });
            }
        }

        function restartSystem() {
            if (confirm('确定要重启系统吗？这将中断所有用户的连接。')) {
                fetch('/api/system/restart', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('系统重启指令已发送，请稍候...');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 3000);
                    } else {
                        alert('重启失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('重启失败');
                });
            }
        }
    </script>
}
