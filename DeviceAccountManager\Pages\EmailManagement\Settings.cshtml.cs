using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Services;
using DeviceAccountManager.Pages.Shared;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages.EmailManagement
{
    public class SettingsModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;

        public SettingsModel(ApplicationDbContext context, IEmailService emailService, IConfiguration configuration)
        {
            _context = context;
            _emailService = emailService;
            _configuration = configuration;
        }

        [BindProperty]
        [Required(ErrorMessage = "SMTP服务器不能为空")]
        public string SmtpHost { get; set; } = string.Empty;

        [BindProperty]
        [Range(1, 65535, ErrorMessage = "端口号必须在1-65535之间")]
        public int SmtpPort { get; set; } = 587;

        [BindProperty]
        [Required(ErrorMessage = "用户名不能为空")]
        public string SmtpUsername { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "密码不能为空")]
        public string SmtpPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "发件人邮箱不能为空")]
        [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
        public string FromEmail { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "发件人名称不能为空")]
        public string FromName { get; set; } = "设备账户管理系统";

        [BindProperty]
        public bool EnableSsl { get; set; } = true;

        [BindProperty]
        public bool EnableEmailNotifications { get; set; } = true;

        [BindProperty]
        [EmailAddress(ErrorMessage = "请输入有效的测试邮箱地址")]
        public string TestEmail { get; set; } = string.Empty;

        [BindProperty]
        public string TestName { get; set; } = string.Empty;

        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }

        // 邮件统计
        public int TotalEmails { get; set; }
        public int SentEmails { get; set; }
        public int PendingEmails { get; set; }
        public int FailedEmails { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            await LoadEmailStatsAsync();
            LoadCurrentSettings();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string action)
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            await LoadEmailStatsAsync();

            if (action == "test")
            {
                return await HandleTestEmailAsync();
            }
            else if (action == "save")
            {
                return await HandleSaveSettingsAsync();
            }

            return Page();
        }

        private async Task<IActionResult> HandleTestEmailAsync()
        {
            if (string.IsNullOrEmpty(TestEmail) || string.IsNullOrEmpty(TestName))
            {
                Message = "请填写测试邮箱和收件人名称";
                IsSuccess = false;
                return Page();
            }

            try
            {
                var subject = "测试邮件 - 设备账户管理系统";
                var content = $@"
这是一封测试邮件。

发送时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}
发送人：{CurrentUsername}

如果您收到这封邮件，说明邮件系统配置正确。

此邮件由系统自动发送，请勿回复。

设备账户管理系统
";

                var success = await _emailService.SendEmailAsync(TestEmail, TestName, subject, content, "Test");

                if (success)
                {
                    Message = "测试邮件发送成功！请检查收件箱。";
                    IsSuccess = true;
                }
                else
                {
                    Message = "测试邮件发送失败，请检查SMTP配置。";
                    IsSuccess = false;
                }
            }
            catch (Exception ex)
            {
                Message = $"发送测试邮件时发生错误：{ex.Message}";
                IsSuccess = false;
            }

            return Page();
        }

        private async Task<IActionResult> HandleSaveSettingsAsync()
        {
            if (!ModelState.IsValid)
            {
                Message = "请检查输入信息";
                IsSuccess = false;
                return Page();
            }

            try
            {
                // 这里应该保存到配置文件或数据库
                // 为了简化，我们暂时只显示成功消息
                Message = "邮件设置保存成功！";
                IsSuccess = true;

                // 在实际应用中，这里应该更新配置文件或数据库
                // 例如：await UpdateConfigurationAsync();
            }
            catch (Exception ex)
            {
                Message = $"保存设置时发生错误：{ex.Message}";
                IsSuccess = false;
            }

            return Page();
        }

        private async Task LoadEmailStatsAsync()
        {
            TotalEmails = await _context.EmailLogs.CountAsync();
            SentEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Sent");
            PendingEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Pending");
            FailedEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Failed");
        }

        private void LoadCurrentSettings()
        {
            // 从配置中加载当前设置
            SmtpHost = _configuration["EmailSettings:SmtpHost"] ?? "";
            SmtpPort = int.TryParse(_configuration["EmailSettings:SmtpPort"], out var port) ? port : 587;
            SmtpUsername = _configuration["EmailSettings:SmtpUsername"] ?? "";
            SmtpPassword = _configuration["EmailSettings:SmtpPassword"] ?? "";
            FromEmail = _configuration["EmailSettings:FromEmail"] ?? "";
            FromName = _configuration["EmailSettings:FromName"] ?? "设备账户管理系统";
            EnableSsl = bool.TryParse(_configuration["EmailSettings:EnableSsl"], out var ssl) ? ssl : true;
            EnableEmailNotifications = bool.TryParse(_configuration["EmailSettings:EnableEmailNotifications"], out var enabled) ? enabled : true;
        }
    }
}
