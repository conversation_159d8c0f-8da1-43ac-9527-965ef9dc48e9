using Xunit;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Services;
using DeviceAccountManager.Models;

namespace DeviceAccountManager.Tests.Services
{
    public class PasswordServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly PasswordService _passwordService;

        public PasswordServiceTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _passwordService = new PasswordService(_context);
        }

        [Fact]
        public void GeneratePassword_WithDefaultSettings_ShouldReturnValidPassword()
        {
            // Arrange
            var length = 12;
            var includeUppercase = true;
            var includeLowercase = true;
            var includeNumbers = true;
            var includeSpecialChars = true;

            // Act
            var password = _passwordService.GeneratePassword(length, includeUppercase, includeLowercase, includeNumbers, includeSpecialChars);

            // Assert
            Assert.NotNull(password);
            Assert.Equal(length, password.Length);
            Assert.Contains(password, c => char.IsUpper(c)); // 包含大写字母
            Assert.Contains(password, c => char.IsLower(c)); // 包含小写字母
            Assert.Contains(password, c => char.IsDigit(c)); // 包含数字
            Assert.Contains(password, c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)); // 包含特殊字符
        }

        [Fact]
        public void GeneratePassword_WithMinimumLength_ShouldReturnValidPassword()
        {
            // Arrange
            var length = 8;

            // Act
            var password = _passwordService.GeneratePassword(length);

            // Assert
            Assert.NotNull(password);
            Assert.Equal(length, password.Length);
        }

        [Fact]
        public void GeneratePassword_OnlyNumbers_ShouldContainOnlyNumbers()
        {
            // Arrange
            var length = 10;

            // Act
            var password = _passwordService.GeneratePassword(length, false, false, true, false);

            // Assert
            Assert.NotNull(password);
            Assert.Equal(length, password.Length);
            Assert.All(password, c => Assert.True(char.IsDigit(c)));
        }

        [Fact]
        public void ValidatePasswordStrength_StrongPassword_ShouldReturnStrong()
        {
            // Arrange
            var strongPassword = "MyStr0ng!P@ssw0rd";

            // Act
            var strength = _passwordService.ValidatePasswordStrength(strongPassword);

            // Assert
            Assert.Equal("强", strength);
        }

        [Fact]
        public void ValidatePasswordStrength_WeakPassword_ShouldReturnWeak()
        {
            // Arrange
            var weakPassword = "123456";

            // Act
            var strength = _passwordService.ValidatePasswordStrength(weakPassword);

            // Assert
            Assert.Equal("弱", strength);
        }

        [Fact]
        public void ValidatePasswordStrength_MediumPassword_ShouldReturnMedium()
        {
            // Arrange
            var mediumPassword = "Password123";

            // Act
            var strength = _passwordService.ValidatePasswordStrength(mediumPassword);

            // Assert
            Assert.Equal("中", strength);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public void ValidatePasswordStrength_EmptyOrNullPassword_ShouldReturnWeak(string password)
        {
            // Act
            var strength = _passwordService.ValidatePasswordStrength(password);

            // Assert
            Assert.Equal("弱", strength);
        }

        [Fact]
        public async Task IsPasswordInHistory_ExistingPassword_ShouldReturnTrue()
        {
            // Arrange
            var userId = 1;
            var password = "TestPassword123!";
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);

            var passwordHistory = new PasswordHistory
            {
                UserId = userId,
                PasswordHash = hashedPassword,
                CreatedAt = DateTime.Now
            };

            _context.PasswordHistories.Add(passwordHistory);
            await _context.SaveChangesAsync();

            // Act
            var result = await _passwordService.IsPasswordInHistoryAsync(userId, password);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsPasswordInHistory_NewPassword_ShouldReturnFalse()
        {
            // Arrange
            var userId = 1;
            var password = "NewPassword123!";

            // Act
            var result = await _passwordService.IsPasswordInHistoryAsync(userId, password);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task AddPasswordToHistory_ShouldAddPasswordSuccessfully()
        {
            // Arrange
            var userId = 1;
            var password = "TestPassword123!";

            // Act
            await _passwordService.AddPasswordToHistoryAsync(userId, password);

            // Assert
            var historyCount = await _context.PasswordHistories
                .Where(ph => ph.UserId == userId)
                .CountAsync();
            Assert.Equal(1, historyCount);
        }

        [Fact]
        public async Task CleanupOldPasswords_ShouldRemoveOldPasswords()
        {
            // Arrange
            var userId = 1;
            var maxHistoryCount = 3;

            // 添加5个历史密码
            for (int i = 0; i < 5; i++)
            {
                var passwordHistory = new PasswordHistory
                {
                    UserId = userId,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword($"Password{i}"),
                    CreatedAt = DateTime.Now.AddDays(-i)
                };
                _context.PasswordHistories.Add(passwordHistory);
            }
            await _context.SaveChangesAsync();

            // Act
            await _passwordService.CleanupOldPasswordsAsync(userId, maxHistoryCount);

            // Assert
            var remainingCount = await _context.PasswordHistories
                .Where(ph => ph.UserId == userId)
                .CountAsync();
            Assert.Equal(maxHistoryCount, remainingCount);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
