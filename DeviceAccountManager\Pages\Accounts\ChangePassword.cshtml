@page
@model DeviceAccountManager.Pages.Accounts.ChangePasswordModel
@{
    ViewData["Title"] = "更改密码";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">更改密码</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a asp-page="./Details" asp-route-id="@Model.AccountId" class="btn btn-sm btn-outline-info">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">密码更改 - @Model.Username</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="AccountId" />
                    
                    <div class="form-group mb-3">
                        <label asp-for="NewPassword" class="form-label">新密码 *</label>
                        <div class="input-group">
                            <input asp-for="NewPassword" type="password" class="form-control" placeholder="请输入新密码" />
                            <button type="button" class="btn btn-outline-secondary" onclick="generatePassword()">
                                <i class="bi bi-arrow-clockwise"></i> 生成
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('NewPassword')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="NewPassword" class="text-danger"></span>
                        <div class="form-text">密码必须包含大小写字母、数字和特殊字符，长度至少8位</div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ConfirmPassword" class="form-label">确认密码 *</label>
                        <div class="input-group">
                            <input asp-for="ConfirmPassword" type="password" class="form-control" placeholder="请再次输入新密码" />
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('ConfirmPassword')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ChangeReason" class="form-label">更改原因</label>
                        <select asp-for="ChangeReason" class="form-select">
                            <option value="Manual change">手动更改</option>
                            <option value="Scheduled change">定期更改</option>
                            <option value="Security requirement">安全要求</option>
                            <option value="Password compromised">密码泄露</option>
                            <option value="System requirement">系统要求</option>
                            <option value="Other">其他原因</option>
                        </select>
                        <span asp-validation-for="ChangeReason" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">密码强度检查</label>
                        <div id="passwordStrength" class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="passwordStrengthText" class="form-text">请输入密码以检查强度</div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 更改密码
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">密码要求</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="bi bi-shield-exclamation"></i> 密码规则</h6>
                    <ul class="mb-0">
                        <li>长度至少8个字符</li>
                        <li>包含大写字母(A-Z)</li>
                        <li>包含小写字母(a-z)</li>
                        <li>包含数字(0-9)</li>
                        <li>包含特殊字符(!@@#$%^&*)</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> 密码建议</h6>
                    <ul class="mb-0">
                        <li>使用密码生成器创建强密码</li>
                        <li>避免使用个人信息</li>
                        <li>不要重复使用旧密码</li>
                        <li>定期更改密码</li>
                    </ul>
                </div>

                @if (Model.LastPasswordChange.HasValue)
                {
                    <div class="alert alert-success">
                        <h6><i class="bi bi-clock-history"></i> 历史信息</h6>
                        <p><strong>上次更改：</strong>@Model.LastPasswordChange.Value.ToString("yyyy-MM-dd")</p>
                        <p class="mb-0"><strong>距今：</strong>@((DateTime.Now - Model.LastPasswordChange.Value).Days)天</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function generatePassword() {
            const length = 12;
            const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@@#$%^&*";
            let password = "";
            
            // 确保包含各种字符类型
            password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)]; // 大写字母
            password += "abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random() * 26)]; // 小写字母
            password += "0123456789"[Math.floor(Math.random() * 10)]; // 数字
            password += "!@@#$%^&*"[Math.floor(Math.random() * 9)]; // 特殊字符
            
            // 填充剩余长度
            for (let i = password.length; i < length; i++) {
                password += charset[Math.floor(Math.random() * charset.length)];
            }
            
            // 打乱字符顺序
            password = password.split('').sort(() => 0.5 - Math.random()).join('');
            
            document.getElementById('NewPassword').value = password;
            document.getElementById('ConfirmPassword').value = password;
            checkPasswordStrength(password);
        }

        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
        }

        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) {
                strength += 20;
            } else {
                feedback.push("长度至少8位");
            }

            if (/[a-z]/.test(password)) {
                strength += 20;
            } else {
                feedback.push("需要小写字母");
            }

            if (/[A-Z]/.test(password)) {
                strength += 20;
            } else {
                feedback.push("需要大写字母");
            }

            if (/[0-9]/.test(password)) {
                strength += 20;
            } else {
                feedback.push("需要数字");
            }

            if (/[!@@#$%^&*]/.test(password)) {
                strength += 20;
            } else {
                feedback.push("需要特殊字符");
            }

            const progressBar = document.querySelector('#passwordStrength .progress-bar');
            const strengthText = document.getElementById('passwordStrengthText');

            progressBar.style.width = strength + '%';
            
            if (strength < 40) {
                progressBar.className = 'progress-bar bg-danger';
                strengthText.textContent = '弱密码: ' + feedback.join(', ');
            } else if (strength < 80) {
                progressBar.className = 'progress-bar bg-warning';
                strengthText.textContent = '中等密码: ' + feedback.join(', ');
            } else {
                progressBar.className = 'progress-bar bg-success';
                strengthText.textContent = '强密码';
            }
        }

        document.getElementById('NewPassword').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    </script>
}
