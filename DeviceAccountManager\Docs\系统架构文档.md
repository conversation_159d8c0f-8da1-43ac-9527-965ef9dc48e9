# 生产设备账号权限管理系统 - 系统架构文档

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-01-01
- **最后更新**: 2025-01-01
- **文档状态**: 正式版

## 🏗️ 系统架构概览

### 整体架构
本系统采用经典的三层架构模式，结合现代Web开发最佳实践：

```
┌─────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)              │
├─────────────────────────────────────────────────────────┤
│  Razor Pages  │  Web API  │  Static Files  │  JavaScript │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Business Layer)              │
├─────────────────────────────────────────────────────────┤
│   Services   │  Controllers  │  Models  │  Middleware   │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    数据访问层 (Data Access Layer)           │
├─────────────────────────────────────────────────────────┤
│  Entity Framework Core  │  DbContext  │  Repositories   │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    数据存储层 (Data Storage Layer)          │
├─────────────────────────────────────────────────────────┤
│        SQL Server Express        │      File System     │
└─────────────────────────────────────────────────────────┘
```

### 技术栈选择

#### 后端技术
- **框架**: ASP.NET Core 10.0
- **ORM**: Entity Framework Core 10.0
- **数据库**: SQL Server Express 2022
- **认证**: Session-based Authentication
- **密码加密**: BCrypt.Net
- **依赖注入**: Microsoft.Extensions.DependencyInjection

#### 前端技术
- **UI框架**: Bootstrap 5.3
- **图标库**: Bootstrap Icons + Font Awesome
- **JavaScript**: jQuery 3.6 + 原生JavaScript
- **图表库**: Chart.js 4.0
- **CSS预处理**: 原生CSS + Bootstrap定制

#### 开发工具
- **IDE**: Visual Studio 2022
- **版本控制**: Git
- **包管理**: NuGet
- **构建工具**: .NET CLI

## 🔧 核心组件设计

### 1. 数据访问层 (Data Access Layer)

#### ApplicationDbContext
```csharp
public class ApplicationDbContext : DbContext
{
    // 核心实体集合
    public DbSet<Account> Users { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<PasswordHistory> PasswordHistories { get; set; }
    public DbSet<EmailLog> EmailLogs { get; set; }
    public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }
    public DbSet<SystemBackup> SystemBackups { get; set; }
    public DbSet<UserPreference> UserPreferences { get; set; }
}
```

#### 实体关系设计
- **Account (用户账户)**: 系统核心实体，包含用户基本信息和权限
- **Employee (员工信息)**: 与Account一对一关系，存储员工详细信息
- **PasswordHistory (密码历史)**: 与Account一对多关系，追踪密码变更历史
- **EmailLog (邮件日志)**: 独立实体，记录所有邮件发送记录
- **AuditLog (审计日志)**: 独立实体，记录所有系统操作
- **SystemConfiguration (系统配置)**: 独立实体，存储系统配置参数
- **SystemBackup (系统备份)**: 独立实体，记录备份操作历史

### 2. 业务逻辑层 (Business Layer)

#### 服务架构
```
IPasswordService ──────────── PasswordService
IEmailService ────────────── EmailService  
ISystemConfigurationService ── SystemConfigurationService
IAuditLogService ──────────── AuditLogService
IBackupService ────────────── BackupService
```

#### 核心服务职责

**PasswordService**
- 密码生成算法
- 密码强度验证
- 密码历史管理
- 密码策略执行

**EmailService**
- SMTP邮件发送
- 邮件队列管理
- 邮件模板处理
- 发送状态追踪

**SystemConfigurationService**
- 系统参数管理
- 配置缓存机制
- 默认配置初始化
- 配置变更审计

**AuditLogService**
- 操作日志记录
- 日志查询过滤
- 自动清理机制
- 实体变更追踪

**BackupService**
- 数据库备份
- 文件系统备份
- 备份压缩存储
- 自动清理策略

### 3. 表示层 (Presentation Layer)

#### Razor Pages 架构
```
Pages/
├── Account/           # 账户管理页面
├── Employee/          # 员工管理页面
├── PasswordManagement/ # 密码管理页面
├── EmailManagement/   # 邮件管理页面
├── SystemManagement/  # 系统管理页面
└── Shared/           # 共享布局和组件
```

#### Web API 设计
```
Controllers/
├── AccountsApiController    # 账户管理API
├── EmployeesApiController   # 员工管理API
├── PasswordApiController    # 密码管理API
├── EmailApiController       # 邮件管理API
├── SystemApiController      # 系统管理API
└── BackupApiController      # 备份管理API
```

## 🔐 安全架构设计

### 认证机制
- **Session-based Authentication**: 基于Session的用户认证
- **角色权限控制**: 四级权限体系 (SuperAdmin > Technician > Maintainer > Operator)
- **页面级权限验证**: AuthorizedPageModel基类实现
- **API级权限控制**: 控制器级别权限验证

### 数据安全
- **密码加密**: BCrypt单向哈希加密
- **敏感数据保护**: 配置文件加密存储
- **SQL注入防护**: Entity Framework参数化查询
- **XSS防护**: Razor Pages自动编码

### 审计追踪
- **操作审计**: 所有CRUD操作自动记录
- **登录审计**: 用户登录/登出记录
- **配置变更审计**: 系统配置修改追踪
- **数据变更审计**: 实体属性变更对比

## 📊 数据流设计

### 用户操作流程
```
用户请求 → 认证中间件 → 权限验证 → 控制器 → 服务层 → 数据层 → 数据库
    ↓
审计日志 ← 业务逻辑 ← 数据处理 ← 实体操作 ← EF Core ← SQL Server
```

### 密码管理流程
```
密码生成请求 → PasswordService → 策略验证 → 历史检查 → 密码生成 → 加密存储
       ↓
   邮件通知 → EmailService → SMTP发送 → 状态记录 → 日志存储
```

### 系统备份流程
```
备份触发 → BackupService → 数据导出 → 文件压缩 → 存储管理 → 清理策略
    ↓
状态更新 → 审计记录 → 通知发送 → 监控更新
```

## 🚀 性能优化设计

### 数据库优化
- **索引策略**: 主键、外键、查询字段建立索引
- **查询优化**: 避免N+1查询，使用Include预加载
- **连接池**: Entity Framework连接池管理
- **分页查询**: 大数据集分页处理

### 缓存策略
- **内存缓存**: 系统配置、用户权限缓存
- **Session缓存**: 用户状态信息缓存
- **静态资源缓存**: CSS、JS、图片文件缓存

### 异步处理
- **邮件发送**: 异步队列处理
- **备份操作**: 后台服务异步执行
- **日志记录**: 异步写入优化
- **文件操作**: 异步I/O操作

## 🔄 扩展性设计

### 模块化设计
- **服务接口**: 基于接口的服务设计
- **依赖注入**: 松耦合组件架构
- **插件机制**: 可扩展的功能模块
- **配置驱动**: 参数化配置管理

### 可扩展点
- **认证提供者**: 支持多种认证方式
- **邮件提供者**: 支持多种邮件服务
- **存储提供者**: 支持多种存储方式
- **通知提供者**: 支持多种通知渠道

### 部署扩展
- **容器化**: Docker容器部署支持
- **负载均衡**: 多实例部署支持
- **数据库扩展**: 读写分离、分库分表
- **缓存扩展**: Redis分布式缓存

## 📈 监控和运维

### 系统监控
- **健康检查**: 定期系统状态检查
- **性能监控**: CPU、内存、磁盘使用率
- **服务监控**: 数据库、邮件服务状态
- **业务监控**: 用户活动、操作统计

### 日志管理
- **结构化日志**: JSON格式日志输出
- **日志级别**: Debug、Info、Warning、Error
- **日志轮转**: 按日期和大小轮转
- **日志聚合**: 集中式日志收集

### 备份策略
- **自动备份**: 定时数据库备份
- **增量备份**: 差异数据备份
- **异地备份**: 备份文件异地存储
- **恢复测试**: 定期备份恢复验证

## 🔮 未来规划

### 技术升级
- **.NET版本**: 跟随.NET最新LTS版本
- **数据库**: 考虑PostgreSQL、MySQL支持
- **前端**: 考虑React、Vue.js单页应用
- **移动端**: 开发移动应用支持

### 功能扩展
- **多租户**: 支持多组织架构
- **工作流**: 审批流程管理
- **报表系统**: 丰富的统计报表
- **API网关**: 微服务架构演进

## 📚 相关文档

- [数据库设计文档](./数据库设计文档.md)
- [API接口文档](./API接口文档.md)
- [部署指南](./部署指南.md)
- [用户操作手册](./用户操作手册.md)
- [故障排除手册](./故障排除手册.md)

---

**文档维护**: 本文档随系统版本更新而更新，请关注版本变更记录。
