using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class MonitorModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuditLogService _auditService;
        private readonly ILogger<MonitorModel> _logger;

        public MonitorModel(ApplicationDbContext context, IAuditLogService auditService, ILogger<MonitorModel> logger)
        {
            _context = context;
            _auditService = auditService;
            _logger = logger;
        }

        public string SystemUptime { get; set; } = string.Empty;
        public int OnlineUsers { get; set; }
        public int TodayRequests { get; set; }
        public List<AuditLog> RecentActivities { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            var currentUserRole = HttpContext.Session.GetString("UserRole");
            if (currentUserRole != "SuperAdmin")
            {
                return RedirectToPage("/Account/AccessDenied");
            }

            await LoadMonitoringDataAsync();
            return Page();
        }

        private async Task LoadMonitoringDataAsync()
        {
            try
            {
                // 计算系统运行时间（简化版本）
                var startTime = DateTime.Now.AddHours(-24); // 假设24小时前启动
                var uptime = DateTime.Now - startTime;
                SystemUptime = $"{uptime.Days}天 {uptime.Hours}小时";

                // 获取在线用户数（简化版本 - 基于最近活动）
                var recentActiveTime = DateTime.Now.AddMinutes(-30);
                OnlineUsers = await _context.AuditLogs
                    .Where(a => a.CreatedAt >= recentActiveTime)
                    .Select(a => a.UserId)
                    .Distinct()
                    .CountAsync();

                // 获取今日请求数
                var today = DateTime.Today;
                TodayRequests = await _context.AuditLogs
                    .Where(a => a.CreatedAt >= today)
                    .CountAsync();

                // 获取最近活动
                RecentActivities = await _context.AuditLogs
                    .Include(a => a.User)
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(10)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载监控数据失败");
                SystemUptime = "未知";
                OnlineUsers = 0;
                TodayRequests = 0;
            }
        }
    }
}
