# 生产设备账号权限管理系统

## 项目概述

这是一个专为生产设备环境设计的账号权限管理系统，提供完整的用户账户管理、权限控制、密码策略、邮件通知和系统管理功能。

## 主要功能

### 1. 用户账户管理
- **四级权限体系**: SuperAdmin > Technician > Maintainer > Operator
- **账户生命周期管理**: 创建、激活、禁用、删除
- **批量操作**: 支持批量导入和管理用户
- **员工信息管理**: 完整的员工档案和部门管理

### 2. 密码安全策略
- **可配置密码策略**: 长度、复杂度、历史记录等
- **季度密码更换**: 自动提醒和强制更换机制
- **密码历史追踪**: 防止重复使用历史密码
- **密码强度检测**: 实时密码强度评估

### 3. 邮件通知系统
- **SMTP邮件服务**: 支持多种邮件服务器配置
- **邮件队列处理**: 异步邮件发送和重试机制
- **邮件模板管理**: 可自定义的邮件模板
- **邮件日志追踪**: 完整的邮件发送记录

### 4. 系统管理功能
- **系统监控**: 实时性能监控和资源使用情况
- **系统优化**: 一键优化和性能调优工具
- **备份管理**: 自动备份和手动备份功能
- **审计日志**: 完整的操作审计和日志管理
- **系统配置**: 灵活的系统参数配置

## 技术架构

### 后端技术栈
- **框架**: ASP.NET Core 10.0
- **数据库**: Entity Framework Core + SQL Server Express
- **认证**: Session-based Authentication
- **密码加密**: BCrypt.Net
- **依赖注入**: Microsoft.Extensions.DependencyInjection

### 前端技术栈
- **UI框架**: Bootstrap 5
- **图标**: Bootstrap Icons + Font Awesome
- **图表**: Chart.js
- **JavaScript**: jQuery + 原生JavaScript

### 数据库设计
- **用户表**: Users (账户信息)
- **员工表**: Employees (员工档案)
- **密码历史**: PasswordHistories (密码变更记录)
- **邮件日志**: EmailLogs (邮件发送记录)
- **审计日志**: AuditLogs (操作审计)
- **系统配置**: SystemConfigurations (系统参数)
- **系统备份**: SystemBackups (备份记录)

## 部署说明

### 环境要求
- .NET 10.0 Runtime
- SQL Server Express 2019+
- Windows Server 2019+ 或 Windows 10+

### 安装步骤
1. 克隆项目代码
2. 配置数据库连接字符串
3. 运行数据库迁移: `dotnet ef database update`
4. 配置SMTP邮件服务器
5. 启动应用程序: `dotnet run`

### 默认管理员账户
- **用户名**: admin
- **密码**: Admin123!
- **权限**: SuperAdmin

## 系统配置

### 邮件服务配置
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.example.com",
    "SmtpPort": 587,
    "Username": "<EMAIL>",
    "Password": "your-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "设备账号管理系统"
  }
}
```

### 密码策略配置
- 最小长度: 8字符
- 必须包含大小写字母
- 必须包含数字和特殊字符
- 密码历史记录: 5个
- 密码有效期: 90天

## 安全特性

### 访问控制
- 基于角色的权限控制 (RBAC)
- 页面级权限验证
- API接口权限检查
- 会话超时管理

### 数据安全
- 密码BCrypt加密存储
- 敏感数据传输加密
- SQL注入防护
- XSS攻击防护

### 审计追踪
- 完整的用户操作日志
- 系统管理操作记录
- 登录/登出追踪
- 数据变更历史

## 维护指南

### 日常维护
- 定期检查系统日志
- 监控系统性能指标
- 执行数据库备份
- 清理过期日志文件

### 故障排除
- 检查应用程序日志
- 验证数据库连接
- 确认邮件服务状态
- 监控系统资源使用

## 版本历史

### v1.0.0 (当前版本)
- 完整的用户账户管理功能
- 四级权限体系实现
- 密码策略和安全控制
- 邮件通知系统
- 系统管理和监控功能
- 备份和恢复机制
- 审计日志和操作追踪

## 联系信息

如有问题或建议，请联系系统管理员。

---

**注意**: 请定期更新系统并保持安全补丁的及时安装。
