using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class BackupModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly ISystemConfigurationService _configService;

        public BackupModel(ApplicationDbContext context, ISystemConfigurationService configService)
        {
            _context = context;
            _configService = configService;
        }

        public List<SystemBackup> Backups { get; set; } = new();
        
        // 统计信息
        public int TotalBackups { get; set; }
        public int SuccessfulBackups { get; set; }
        public long TotalSize { get; set; }
        public string TotalSizeFormatted { get; set; } = "0 B";
        public SystemBackup? LatestBackup { get; set; }

        // 备份设置
        public bool AutoBackupEnabled { get; set; }
        public string BackupFrequency { get; set; } = "Weekly";
        public int BackupRetentionDays { get; set; } = 30;

        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            await LoadBackupsAsync();
            await LoadStatisticsAsync();
            await LoadSettingsAsync();

            return Page();
        }

        private async Task LoadBackupsAsync()
        {
            Backups = await _context.SystemBackups
                .Include(b => b.CreatedByUser)
                .OrderByDescending(b => b.CreatedAt)
                .Take(50) // 只显示最近50个备份
                .ToListAsync();
        }

        private async Task LoadStatisticsAsync()
        {
            TotalBackups = await _context.SystemBackups.CountAsync();
            SuccessfulBackups = await _context.SystemBackups
                .Where(b => b.Status == "Completed")
                .CountAsync();

            TotalSize = await _context.SystemBackups
                .Where(b => b.Status == "Completed")
                .SumAsync(b => b.FileSize);

            TotalSizeFormatted = FormatFileSize(TotalSize);

            LatestBackup = await _context.SystemBackups
                .Where(b => b.Status == "Completed")
                .OrderByDescending(b => b.CreatedAt)
                .FirstOrDefaultAsync();
        }

        private async Task LoadSettingsAsync()
        {
            AutoBackupEnabled = await _configService.GetConfigValueAsync("Backup.AutoEnabled", false);
            BackupFrequency = await _configService.GetConfigValueAsync("Backup.Frequency", "Weekly");
            BackupRetentionDays = await _configService.GetConfigValueAsync("Backup.RetentionDays", 30);
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
