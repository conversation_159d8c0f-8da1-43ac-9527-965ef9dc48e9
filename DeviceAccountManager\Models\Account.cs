using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 账户表 - 设备账户信息
    /// </summary>
    public class Account
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 账户权限级别：SuperAdmin=超级管理员, Technician=工艺员, Maintainer=维护者, Operator=操作者
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PermissionLevel { get; set; } = string.Empty;

        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否免于季度密码更改
        /// </summary>
        [Required]
        public bool IsExemptFromPasswordChange { get; set; } = false;

        /// <summary>
        /// 最后密码更改时间
        /// </summary>
        public DateTime? LastPasswordChangeAt { get; set; }

        /// <summary>
        /// 下次密码更改时间
        /// </summary>
        public DateTime? NextPasswordChangeAt { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // 外键
        [Required]
        public int DeviceId { get; set; }

        public int? EmployeeId { get; set; }

        // 导航属性
        [ForeignKey("DeviceId")]
        public virtual Device Device { get; set; } = null!;

        [ForeignKey("EmployeeId")]
        public virtual Employee? Employee { get; set; }

        public virtual ICollection<PasswordHistory> PasswordHistories { get; set; } = new List<PasswordHistory>();
    }
}
