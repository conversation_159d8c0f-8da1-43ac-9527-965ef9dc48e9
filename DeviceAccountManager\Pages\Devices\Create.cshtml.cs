using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Devices
{
    public class CreateModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IActionResult OnGet()
        {
            // 检查权限：只有技术员及以上级别可以添加设备
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }
            return Page();
        }

        [BindProperty]
        public Device Device { get; set; } = default!;

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // 检查设备代码是否已存在
            var existingDevice = await _context.Devices
                .FirstOrDefaultAsync(d => d.DeviceCode == Device.DeviceCode);
            
            if (existingDevice != null)
            {
                ModelState.AddModelError("Device.DeviceCode", "设备代码已存在，请使用其他代码");
                return Page();
            }

            // 设置创建信息
            Device.CreatedAt = DateTime.Now;
            Device.UpdatedAt = DateTime.Now;

            _context.Devices.Add(Device);
            await _context.SaveChangesAsync();

            // 记录操作日志
            var log = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "Create",
                TargetType = "Device",
                TargetId = Device.Id.ToString(),
                Description = $"创建设备：{Device.DeviceCode} - {Device.DeviceName}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(log);
            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }
    }
}
