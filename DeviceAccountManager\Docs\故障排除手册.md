# 生产设备账号权限管理系统 - 故障排除手册

## 📋 手册信息

- **手册版本**: v1.0
- **适用系统**: 生产设备账号权限管理系统 v1.0
- **目标用户**: 系统管理员、技术支持人员
- **最后更新**: 2025-01-01

## 🚨 常见问题快速诊断

### 问题分类
| 问题类型 | 紧急程度 | 影响范围 | 处理时间 |
|----------|----------|----------|----------|
| 系统无法访问 | 🔴 紧急 | 全系统 | 立即处理 |
| 登录问题 | 🟡 重要 | 单用户/多用户 | 30分钟内 |
| 功能异常 | 🟡 重要 | 特定功能 | 1小时内 |
| 性能问题 | 🟢 一般 | 用户体验 | 4小时内 |
| 数据问题 | 🔴 紧急 | 数据完整性 | 立即处理 |

## 🔧 系统访问问题

### 问题1: 无法访问系统（页面无法打开）

**症状描述**:
- 浏览器显示"无法访问此网站"
- 连接超时错误
- 页面一直加载中

**可能原因**:
1. 应用程序服务未启动
2. 端口被占用或防火墙阻止
3. 网络连接问题
4. 服务器资源不足

**诊断步骤**:
```bash
# 1. 检查应用程序进程
tasklist | findstr DeviceAccountManager

# 2. 检查端口监听状态
netstat -an | findstr :5000

# 3. 检查Windows服务状态
sc query DeviceAccountManager

# 4. 检查防火墙规则
netsh advfirewall firewall show rule name="DeviceAccountManager"
```

**解决方案**:
```bash
# 方案1: 重启应用程序服务
net stop DeviceAccountManager
net start DeviceAccountManager

# 方案2: 手动启动应用程序
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll

# 方案3: 检查并修复防火墙规则
netsh advfirewall firewall add rule name="DeviceAccountManager" dir=in action=allow protocol=TCP localport=5000

# 方案4: 重启服务器（最后手段）
shutdown /r /t 0
```

### 问题2: 页面加载缓慢

**症状描述**:
- 页面响应时间超过10秒
- 部分功能加载失败
- 浏览器显示"页面无响应"

**可能原因**:
1. 数据库查询性能问题
2. 服务器资源不足
3. 网络带宽限制
4. 大量并发用户访问

**诊断步骤**:
```bash
# 1. 检查CPU和内存使用率
wmic cpu get loadpercentage /value
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value

# 2. 检查数据库连接
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT @@VERSION"

# 3. 检查应用程序日志
type logs\app*.log | findstr "ERROR\|WARN"

# 4. 检查网络连接
ping localhost
```

**解决方案**:
```bash
# 方案1: 重启应用程序
net restart DeviceAccountManager

# 方案2: 清理系统缓存
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll --cleanup

# 方案3: 优化数据库
sqlcmd -S .\SQLEXPRESS -E -Q "USE DeviceAccountManager; EXEC sp_updatestats"

# 方案4: 增加系统资源或优化配置
```

## 🔐 登录认证问题

### 问题3: 无法登录系统

**症状描述**:
- 输入正确用户名密码后提示"用户名或密码错误"
- 登录页面一直刷新
- 提示"账户已被锁定"

**可能原因**:
1. 密码已过期或被修改
2. 账户被禁用或删除
3. 数据库连接问题
4. Session配置问题

**诊断步骤**:
```sql
-- 1. 检查用户账户状态
USE DeviceAccountManager;
SELECT Id, Username, IsActive, LastLoginAt, PasswordChangedAt 
FROM Users 
WHERE Username = '用户名';

-- 2. 检查密码历史
SELECT TOP 5 * FROM PasswordHistories 
WHERE UserId = (SELECT Id FROM Users WHERE Username = '用户名')
ORDER BY CreatedAt DESC;

-- 3. 检查审计日志
SELECT TOP 10 * FROM AuditLogs 
WHERE Action LIKE '%Login%' 
ORDER BY CreatedAt DESC;
```

**解决方案**:
```sql
-- 方案1: 重置用户密码
UPDATE Users 
SET PasswordHash = '$2a$11$...' -- 使用BCrypt生成的新密码哈希
WHERE Username = '用户名';

-- 方案2: 启用被禁用的账户
UPDATE Users 
SET IsActive = 1 
WHERE Username = '用户名';

-- 方案3: 清除登录锁定（如果实现了锁定机制）
-- 根据具体实现调整
```

### 问题4: 登录后立即退出

**症状描述**:
- 登录成功后自动跳转到登录页面
- Session信息丢失
- 提示"会话已过期"

**可能原因**:
1. Session配置错误
2. Cookie设置问题
3. 浏览器兼容性问题
4. 系统时间不同步

**诊断步骤**:
```bash
# 1. 检查系统时间
date /t && time /t

# 2. 检查应用程序配置
type appsettings.json | findstr Session

# 3. 检查浏览器Cookie设置
# 在浏览器开发者工具中检查Cookie
```

**解决方案**:
```json
// 方案1: 调整Session配置（appsettings.json）
{
  "SessionSettings": {
    "TimeoutMinutes": 30,
    "CookieName": "DeviceAccountManager.Session",
    "CookieHttpOnly": true,
    "CookieSecure": false
  }
}
```

```bash
# 方案2: 重启应用程序
net restart DeviceAccountManager

# 方案3: 清除浏览器缓存和Cookie
# 指导用户清除浏览器数据
```

## 📧 邮件功能问题

### 问题5: 邮件发送失败

**症状描述**:
- 密码重置邮件未收到
- 邮件日志显示发送失败
- SMTP连接错误

**可能原因**:
1. SMTP服务器配置错误
2. 邮箱账户认证失败
3. 网络连接问题
4. 邮件服务器限制

**诊断步骤**:
```sql
-- 1. 检查邮件发送日志
USE DeviceAccountManager;
SELECT TOP 20 * FROM EmailLogs 
WHERE Status = 'Failed' 
ORDER BY SentAt DESC;

-- 2. 检查系统配置
SELECT * FROM SystemConfigurations 
WHERE ConfigKey LIKE 'Email%';
```

```bash
# 3. 测试SMTP连接
telnet smtp.company.com 587

# 4. 检查网络连接
nslookup smtp.company.com
```

**解决方案**:
```json
// 方案1: 更新邮件配置（appsettings.json）
{
  "EmailSettings": {
    "SmtpServer": "smtp.company.com",
    "SmtpPort": 587,
    "Username": "correct-username",
    "Password": "correct-password",
    "EnableSsl": true,
    "Timeout": 30000
  }
}
```

```bash
# 方案2: 重启应用程序
net restart DeviceAccountManager

# 方案3: 测试邮件发送
curl -X POST http://localhost:5000/api/email/test \
  -H "Content-Type: application/json" \
  -d '{"toEmail":"<EMAIL>","subject":"测试邮件"}'
```

## 💾 数据库问题

### 问题6: 数据库连接失败

**症状描述**:
- 系统提示"数据库连接失败"
- 页面显示数据库错误
- 应用程序无法启动

**可能原因**:
1. SQL Server服务未启动
2. 数据库不存在或损坏
3. 连接字符串错误
4. 权限不足

**诊断步骤**:
```bash
# 1. 检查SQL Server服务状态
sc query MSSQL$SQLEXPRESS

# 2. 检查数据库是否存在
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT name FROM sys.databases WHERE name = 'DeviceAccountManager'"

# 3. 测试数据库连接
sqlcmd -S .\SQLEXPRESS -E -d DeviceAccountManager -Q "SELECT 1"

# 4. 检查连接字符串
type appsettings.json | findstr ConnectionStrings
```

**解决方案**:
```bash
# 方案1: 启动SQL Server服务
net start MSSQL$SQLEXPRESS

# 方案2: 重新创建数据库
cd C:\Applications\DeviceAccountManager
dotnet ef database update

# 方案3: 修复数据库
sqlcmd -S .\SQLEXPRESS -E -Q "DBCC CHECKDB('DeviceAccountManager', REPAIR_REBUILD)"

# 方案4: 从备份恢复数据库
sqlcmd -S .\SQLEXPRESS -E -Q "RESTORE DATABASE DeviceAccountManager FROM DISK = 'C:\Backup\DeviceAccountManager.bak'"
```

### 问题7: 数据丢失或损坏

**症状描述**:
- 用户数据显示不正确
- 部分记录丢失
- 数据库一致性错误

**可能原因**:
1. 硬件故障
2. 软件错误
3. 人为误操作
4. 数据库损坏

**诊断步骤**:
```sql
-- 1. 检查数据库完整性
DBCC CHECKDB('DeviceAccountManager');

-- 2. 检查表记录数
SELECT 
    t.name AS TableName,
    p.rows AS RecordCount
FROM sys.tables t
INNER JOIN sys.partitions p ON t.object_id = p.object_id
WHERE p.index_id < 2;

-- 3. 检查最近的操作日志
SELECT TOP 50 * FROM AuditLogs 
ORDER BY CreatedAt DESC;
```

**解决方案**:
```sql
-- 方案1: 修复数据库
DBCC CHECKDB('DeviceAccountManager', REPAIR_REBUILD);

-- 方案2: 从备份恢复特定表
-- 需要根据具体情况制定恢复策略

-- 方案3: 数据重建（最后手段）
-- 从最近的完整备份恢复整个数据库
```

## 🔄 系统性能问题

### 问题8: 系统响应缓慢

**症状描述**:
- 页面加载时间超过正常范围
- 数据库查询超时
- 用户操作响应延迟

**可能原因**:
1. 数据库查询效率低
2. 服务器资源不足
3. 并发用户过多
4. 缓存失效

**诊断步骤**:
```sql
-- 1. 检查慢查询
SELECT TOP 10 
    total_elapsed_time/execution_count AS avg_time,
    text
FROM sys.dm_exec_query_stats 
CROSS APPLY sys.dm_exec_sql_text(sql_handle)
ORDER BY avg_time DESC;

-- 2. 检查数据库等待
SELECT * FROM sys.dm_os_wait_stats
ORDER BY wait_time_ms DESC;
```

```bash
# 3. 检查系统资源
wmic cpu get loadpercentage /value
wmic process where name="DeviceAccountManager.exe" get PageFileUsage,WorkingSetSize
```

**解决方案**:
```sql
-- 方案1: 优化数据库
UPDATE STATISTICS;
EXEC sp_recompile;

-- 方案2: 重建索引
ALTER INDEX ALL ON Users REBUILD;
ALTER INDEX ALL ON AuditLogs REBUILD;
```

```bash
# 方案3: 重启应用程序
net restart DeviceAccountManager

# 方案4: 清理系统缓存
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll --cleanup
```

## 🛠️ 系统维护工具

### 健康检查脚本
```bash
# 运行系统健康检查
cd C:\Applications\DeviceAccountManager\Scripts
health-check.bat
```

### 备份脚本
```bash
# 执行系统备份
cd C:\Applications\DeviceAccountManager\Scripts
backup.bat
```

### 日志分析
```bash
# 查看错误日志
cd C:\Applications\DeviceAccountManager\logs
findstr /i "error\|exception\|fail" app*.log

# 查看最近的活动
findstr /i "login\|logout\|create\|update\|delete" app*.log | tail -20
```

## 📞 紧急联系流程

### 紧急情况处理
1. **立即评估**: 确定问题影响范围和紧急程度
2. **临时措施**: 采取临时解决方案减少影响
3. **联系支持**: 根据问题类型联系相应技术支持
4. **记录问题**: 详细记录问题现象和处理过程
5. **跟踪解决**: 持续跟踪问题解决进度

### 联系信息
- **技术支持热线**: [紧急联系电话]
- **技术支持邮箱**: [<EMAIL>]
- **系统管理员**: [<EMAIL>]
- **数据库管理员**: [<EMAIL>]

### 问题上报模板
```
问题标题: [简要描述问题]
发生时间: [YYYY-MM-DD HH:MM:SS]
影响范围: [用户数量/功能模块]
紧急程度: [紧急/重要/一般]
问题描述: [详细描述问题现象]
重现步骤: [如何重现问题]
错误信息: [具体错误消息]
已尝试解决方案: [已经尝试的解决方法]
联系人: [报告人姓名和联系方式]
```

## 📚 相关资源

### 文档链接
- [系统架构文档](./系统架构文档.md)
- [数据库设计文档](./数据库设计文档.md)
- [部署指南](./部署指南.md)
- [用户操作手册](./用户操作手册.md)

### 工具下载
- SQL Server Management Studio
- .NET Runtime
- 系统监控工具
- 数据库备份工具

### 在线资源
- Microsoft .NET 文档
- SQL Server 帮助文档
- Bootstrap 官方文档
- 系统更新公告

---

**手册维护**: 本手册定期更新，包含最新的故障排除方法和解决方案。遇到新问题请及时反馈以完善手册内容。
