@page
@model DeviceAccountManager.Pages.SystemManagement.ConfigurationModel
@{
    ViewData["Title"] = "系统配置";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">系统配置</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回系统管理
            </a>
            <button type="button" class="btn btn-sm btn-success" onclick="saveAllConfigurations()">
                <i class="bi bi-check-circle"></i> 保存所有配置
            </button>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
        @Model.Message
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- 配置分类标签页 -->
<ul class="nav nav-tabs" id="configTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
            <i class="bi bi-gear"></i> 系统设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab">
            <i class="bi bi-key"></i> 密码策略
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
            <i class="bi bi-envelope"></i> 邮件设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
            <i class="bi bi-shield"></i> 安全设置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
            <i class="bi bi-archive"></i> 备份设置
        </button>
    </li>
</ul>

<div class="tab-content" id="configTabContent">
    <!-- 系统设置 -->
    <div class="tab-pane fade show active" id="system" role="tabpanel">
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">系统基本设置</h6>
            </div>
            <div class="card-body">
                <form id="systemForm">
                    @foreach (var config in Model.SystemConfigurations)
                    {
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">@config.Description</label>
                            </div>
                            <div class="col-md-6">
                                @if (config.DataType == "Boolean")
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="@config.ConfigKey" name="@config.ConfigKey" 
                                               value="true" checked="@(config.ConfigValue.ToLower() == "true")"
                                               disabled="@config.IsReadOnly" />
                                        <label class="form-check-label" for="@config.ConfigKey">
                                            启用
                                        </label>
                                    </div>
                                }
                                else
                                {
                                    <input type="@(config.DataType == "Integer" ? "number" : "text")" 
                                           class="form-control" 
                                           id="@config.ConfigKey" name="@config.ConfigKey" 
                                           value="@config.ConfigValue"
                                           readonly="@config.IsReadOnly" />
                                }
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">@config.ConfigKey</small>
                                @if (config.IsReadOnly)
                                {
                                    <br><span class="badge bg-secondary">只读</span>
                                }
                            </div>
                        </div>
                    }
                </form>
            </div>
        </div>
    </div>

    <!-- 密码策略 -->
    <div class="tab-pane fade" id="password" role="tabpanel">
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-warning">密码策略设置</h6>
            </div>
            <div class="card-body">
                <form id="passwordForm">
                    @foreach (var config in Model.PasswordConfigurations)
                    {
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">@config.Description</label>
                            </div>
                            <div class="col-md-6">
                                @if (config.DataType == "Boolean")
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="@config.ConfigKey" name="@config.ConfigKey" 
                                               value="true" checked="@(config.ConfigValue.ToLower() == "true")" />
                                        <label class="form-check-label" for="@config.ConfigKey">
                                            启用
                                        </label>
                                    </div>
                                }
                                else
                                {
                                    <input type="@(config.DataType == "Integer" ? "number" : "text")" 
                                           class="form-control" 
                                           id="@config.ConfigKey" name="@config.ConfigKey" 
                                           value="@config.ConfigValue" />
                                }
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">@config.ConfigKey</small>
                            </div>
                        </div>
                    }
                </form>
            </div>
        </div>
    </div>

    <!-- 邮件设置 -->
    <div class="tab-pane fade" id="email" role="tabpanel">
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">邮件服务设置</h6>
            </div>
            <div class="card-body">
                <form id="emailForm">
                    @foreach (var config in Model.EmailConfigurations)
                    {
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">@config.Description</label>
                            </div>
                            <div class="col-md-6">
                                @if (config.DataType == "Boolean")
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="@config.ConfigKey" name="@config.ConfigKey" 
                                               value="true" checked="@(config.ConfigValue.ToLower() == "true")" />
                                        <label class="form-check-label" for="@config.ConfigKey">
                                            启用
                                        </label>
                                    </div>
                                }
                                else
                                {
                                    <input type="@(config.DataType == "Integer" ? "number" : (config.ConfigKey.Contains("Password") ? "password" : "text"))" 
                                           class="form-control" 
                                           id="@config.ConfigKey" name="@config.ConfigKey" 
                                           value="@config.ConfigValue" />
                                }
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">@config.ConfigKey</small>
                            </div>
                        </div>
                    }
                </form>
                <div class="text-end">
                    <button type="button" class="btn btn-outline-info" onclick="testEmailSettings()">
                        <i class="bi bi-envelope-check"></i> 测试邮件设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 安全设置 -->
    <div class="tab-pane fade" id="security" role="tabpanel">
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-danger">安全策略设置</h6>
            </div>
            <div class="card-body">
                <form id="securityForm">
                    @foreach (var config in Model.SecurityConfigurations)
                    {
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">@config.Description</label>
                            </div>
                            <div class="col-md-6">
                                @if (config.DataType == "Boolean")
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="@config.ConfigKey" name="@config.ConfigKey" 
                                               value="true" checked="@(config.ConfigValue.ToLower() == "true")" />
                                        <label class="form-check-label" for="@config.ConfigKey">
                                            启用
                                        </label>
                                    </div>
                                }
                                else
                                {
                                    <input type="@(config.DataType == "Integer" ? "number" : "text")" 
                                           class="form-control" 
                                           id="@config.ConfigKey" name="@config.ConfigKey" 
                                           value="@config.ConfigValue" />
                                }
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">@config.ConfigKey</small>
                            </div>
                        </div>
                    }
                </form>
            </div>
        </div>
    </div>

    <!-- 备份设置 -->
    <div class="tab-pane fade" id="backup" role="tabpanel">
        <div class="card shadow mt-3">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">备份策略设置</h6>
            </div>
            <div class="card-body">
                <form id="backupForm">
                    @foreach (var config in Model.BackupConfigurations)
                    {
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">@config.Description</label>
                            </div>
                            <div class="col-md-6">
                                @if (config.DataType == "Boolean")
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="@config.ConfigKey" name="@config.ConfigKey" 
                                               value="true" checked="@(config.ConfigValue.ToLower() == "true")" />
                                        <label class="form-check-label" for="@config.ConfigKey">
                                            启用
                                        </label>
                                    </div>
                                }
                                else
                                {
                                    <input type="@(config.DataType == "Integer" ? "number" : "text")" 
                                           class="form-control" 
                                           id="@config.ConfigKey" name="@config.ConfigKey" 
                                           value="@config.ConfigValue" />
                                }
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">@config.ConfigKey</small>
                            </div>
                        </div>
                    }
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function saveAllConfigurations() {
            const forms = ['systemForm', 'passwordForm', 'emailForm', 'securityForm', 'backupForm'];
            const allData = {};

            forms.forEach(formId => {
                const form = document.getElementById(formId);
                if (form) {
                    const formData = new FormData(form);
                    for (let [key, value] of formData.entries()) {
                        allData[key] = value;
                    }
                    
                    // 处理未选中的复选框
                    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        if (!checkbox.checked) {
                            allData[checkbox.name] = 'false';
                        }
                    });
                }
            });

            fetch('/api/system/configuration', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(allData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('配置保存成功！');
                    location.reload();
                } else {
                    alert('保存失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败');
            });
        }

        function testEmailSettings() {
            alert('邮件设置测试功能开发中...');
        }
    </script>
}
