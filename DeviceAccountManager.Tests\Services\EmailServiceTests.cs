using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Services;
using DeviceAccountManager.Models;

namespace DeviceAccountManager.Tests.Services
{
    public class EmailServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<ILogger<EmailService>> _mockLogger;
        private readonly EmailService _emailService;

        public EmailServiceTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _mockLogger = new Mock<ILogger<EmailService>>();
            _emailService = new EmailService(_context, _mockLogger.Object);
        }

        [Fact]
        public async Task LogEmailAsync_ShouldAddEmailLogToDatabase()
        {
            // Arrange
            var toEmail = "<EMAIL>";
            var subject = "Test Subject";
            var body = "Test Body";
            var status = "Sent";

            // Act
            await _emailService.LogEmailAsync(toEmail, subject, body, status);

            // Assert
            var emailLog = await _context.EmailLogs.FirstOrDefaultAsync();
            Assert.NotNull(emailLog);
            Assert.Equal(toEmail, emailLog.ToEmail);
            Assert.Equal(subject, emailLog.Subject);
            Assert.Equal(body, emailLog.Body);
            Assert.Equal(status, emailLog.Status);
        }

        [Fact]
        public async Task GetEmailLogsAsync_ShouldReturnPagedResults()
        {
            // Arrange
            for (int i = 0; i < 15; i++)
            {
                var emailLog = new EmailLog
                {
                    ToEmail = $"test{i}@example.com",
                    Subject = $"Subject {i}",
                    Body = $"Body {i}",
                    Status = "Sent",
                    SentAt = DateTime.Now.AddMinutes(-i)
                };
                _context.EmailLogs.Add(emailLog);
            }
            await _context.SaveChangesAsync();

            // Act
            var result = await _emailService.GetEmailLogsAsync(1, 10);

            // Assert
            Assert.Equal(10, result.Count);
            Assert.Equal("<EMAIL>", result.First().ToEmail); // 最新的在前面
        }

        [Fact]
        public async Task GetEmailLogsAsync_WithStatusFilter_ShouldReturnFilteredResults()
        {
            // Arrange
            var sentLog = new EmailLog
            {
                ToEmail = "<EMAIL>",
                Subject = "Sent Email",
                Body = "Body",
                Status = "Sent",
                SentAt = DateTime.Now
            };

            var failedLog = new EmailLog
            {
                ToEmail = "<EMAIL>",
                Subject = "Failed Email",
                Body = "Body",
                Status = "Failed",
                SentAt = DateTime.Now
            };

            _context.EmailLogs.AddRange(sentLog, failedLog);
            await _context.SaveChangesAsync();

            // Act
            var sentResults = await _emailService.GetEmailLogsAsync(1, 10, "Sent");
            var failedResults = await _emailService.GetEmailLogsAsync(1, 10, "Failed");

            // Assert
            Assert.Single(sentResults);
            Assert.Equal("<EMAIL>", sentResults.First().ToEmail);
            
            Assert.Single(failedResults);
            Assert.Equal("<EMAIL>", failedResults.First().ToEmail);
        }

        [Fact]
        public async Task GetEmailStatsAsync_ShouldReturnCorrectStatistics()
        {
            // Arrange
            var today = DateTime.Today;
            
            // 今天的邮件
            var todayEmails = new[]
            {
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Test", Body = "Body", Status = "Sent", SentAt = today.AddHours(1) },
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Test", Body = "Body", Status = "Sent", SentAt = today.AddHours(2) },
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Test", Body = "Body", Status = "Failed", SentAt = today.AddHours(3) }
            };

            // 昨天的邮件
            var yesterdayEmails = new[]
            {
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Test", Body = "Body", Status = "Sent", SentAt = today.AddDays(-1) }
            };

            _context.EmailLogs.AddRange(todayEmails);
            _context.EmailLogs.AddRange(yesterdayEmails);
            await _context.SaveChangesAsync();

            // Act
            var stats = await _emailService.GetEmailStatsAsync();

            // Assert
            Assert.Equal(3, stats.TodayCount);
            Assert.Equal(2, stats.TodaySuccessCount);
            Assert.Equal(1, stats.TodayFailedCount);
            Assert.Equal(4, stats.TotalCount);
        }

        [Fact]
        public void ValidateEmailAddress_ValidEmail_ShouldReturnTrue()
        {
            // Arrange
            var validEmails = new[]
            {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            };

            // Act & Assert
            foreach (var email in validEmails)
            {
                var result = _emailService.ValidateEmailAddress(email);
                Assert.True(result, $"Email {email} should be valid");
            }
        }

        [Fact]
        public void ValidateEmailAddress_InvalidEmail_ShouldReturnFalse()
        {
            // Arrange
            var invalidEmails = new[]
            {
                "invalid-email",
                "@example.com",
                "test@",
                "<EMAIL>",
                ""
            };

            // Act & Assert
            foreach (var email in invalidEmails)
            {
                var result = _emailService.ValidateEmailAddress(email);
                Assert.False(result, $"Email {email} should be invalid");
            }
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public void ValidateEmailAddress_NullOrEmptyEmail_ShouldReturnFalse(string email)
        {
            // Act
            var result = _emailService.ValidateEmailAddress(email);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CleanupOldEmailLogsAsync_ShouldRemoveOldLogs()
        {
            // Arrange
            var cutoffDate = DateTime.Now.AddDays(-30);
            
            var oldLogs = new[]
            {
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Old", Body = "Body", Status = "Sent", SentAt = cutoffDate.AddDays(-1) },
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Old", Body = "Body", Status = "Sent", SentAt = cutoffDate.AddDays(-5) }
            };

            var recentLogs = new[]
            {
                new EmailLog { ToEmail = "<EMAIL>", Subject = "Recent", Body = "Body", Status = "Sent", SentAt = DateTime.Now }
            };

            _context.EmailLogs.AddRange(oldLogs);
            _context.EmailLogs.AddRange(recentLogs);
            await _context.SaveChangesAsync();

            // Act
            await _emailService.CleanupOldEmailLogsAsync(30);

            // Assert
            var remainingLogs = await _context.EmailLogs.ToListAsync();
            Assert.Single(remainingLogs);
            Assert.Equal("<EMAIL>", remainingLogs.First().ToEmail);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
