using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.EmailManagement
{
    public class DetailsModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public DetailsModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public EmailLog EmailLog { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var emailLog = await _context.EmailLogs
                .Include(e => e.Account)
                .ThenInclude(a => a.Device)
                .Include(e => e.Account)
                .ThenInclude(a => a.Employee)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (emailLog == null)
            {
                return NotFound();
            }

            EmailLog = emailLog;
            return Page();
        }
    }
}
