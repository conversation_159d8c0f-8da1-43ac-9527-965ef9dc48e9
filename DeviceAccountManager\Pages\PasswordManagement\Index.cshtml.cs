using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Pages.PasswordManagement
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IPasswordService _passwordService;

        public IndexModel(ApplicationDbContext context, IPasswordService passwordService)
        {
            _context = context;
            _passwordService = passwordService;
        }

        public int ExpiredPasswordsCount { get; set; }
        public int ExpiringPasswordsCount { get; set; }
        public int TotalAccountsCount { get; set; }
        public int PasswordHealthPercentage { get; set; }

        public IList<Account> ExpiredAccounts { get; set; } = default!;
        public IList<Account> ExpiringAccounts { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            // 获取需要立即更改密码的账户
            ExpiredAccounts = await _passwordService.GetAccountsNeedingPasswordChangeAsync();
            ExpiredPasswordsCount = ExpiredAccounts.Count;

            // 获取7天内到期的账户
            ExpiringAccounts = await _passwordService.GetPasswordExpiringAccountsAsync(7);
            ExpiringPasswordsCount = ExpiringAccounts.Count;

            // 获取总账户数
            TotalAccountsCount = await _context.Accounts
                .CountAsync(a => a.IsActive);

            // 计算密码健康度
            var healthyAccountsCount = TotalAccountsCount - ExpiredPasswordsCount - ExpiringPasswordsCount;
            PasswordHealthPercentage = TotalAccountsCount > 0 
                ? (int)Math.Round((double)healthyAccountsCount / TotalAccountsCount * 100) 
                : 100;

            return Page();
        }
    }
}
