@page
@model DeviceAccountManager.Pages.Accounts.DeleteModel
@{
    ViewData["Title"] = "删除账户";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">删除账户</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a asp-page="./Details" asp-route-id="@Model.Account.Id" class="btn btn-sm btn-outline-info">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow border-danger">
            <div class="card-header py-3 bg-danger text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-exclamation-triangle"></i> 确认删除账户
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle-fill"></i> 警告</h5>
                    <p>您即将删除以下账户，此操作<strong>不可撤销</strong>！</p>
                    <p>删除账户将同时删除：</p>
                    <ul>
                        <li>账户的所有密码历史记录</li>
                        <li>与此账户相关的邮件日志</li>
                        <li>与此账户相关的操作日志</li>
                    </ul>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">账户名：</dt>
                            <dd class="col-sm-8">
                                <strong>@Model.Account.Username</strong>
                                @if (Model.Account.IsActive)
                                {
                                    <span class="badge bg-success ms-2">启用</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger ms-2">禁用</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-4">权限级别：</dt>
                            <dd class="col-sm-8">
                                @switch (Model.Account.PermissionLevel)
                                {
                                    case "SuperAdmin":
                                        <span class="badge bg-danger">超级管理员</span>
                                        break;
                                    case "Technician":
                                        <span class="badge bg-warning">工艺员</span>
                                        break;
                                    case "Maintainer":
                                        <span class="badge bg-info">维护者</span>
                                        break;
                                    case "Operator":
                                        <span class="badge bg-secondary">操作者</span>
                                        break;
                                    default:
                                        <span class="badge bg-light text-dark">@Model.Account.PermissionLevel</span>
                                        break;
                                }
                            </dd>

                            <dt class="col-sm-4">关联设备：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.Device != null)
                                {
                                    <span>@Model.Account.Device.DeviceCode</span>
                                    <br><small class="text-muted">@Model.Account.Device.DeviceName</small>
                                }
                                else
                                {
                                    <span class="text-muted">未关联设备</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">绑定员工：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.Employee != null)
                                {
                                    <span>@Model.Account.Employee.Name</span>
                                    <br><small class="text-muted">@Model.Account.Employee.EmployeeCode</small>
                                }
                                else
                                {
                                    <span class="text-muted">未绑定员工</span>
                                }
                            </dd>

                            <dt class="col-sm-4">创建时间：</dt>
                            <dd class="col-sm-8">@Model.Account.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>

                            <dt class="col-sm-4">上次更新：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.UpdatedAt.HasValue)
                                {
                                    @Model.Account.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")
                                }
                                else
                                {
                                    <span class="text-muted">未更新</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Account.Description))
                {
                    <div class="mt-3">
                        <h6>账户描述：</h6>
                        <p class="text-muted">@Model.Account.Description</p>
                    </div>
                }

                <form method="post" class="mt-4">
                    <input type="hidden" asp-for="Account.Id" />
                    
                    <div class="form-group mb-3">
                        <label asp-for="DeleteReason" class="form-label">删除原因 *</label>
                        <select asp-for="DeleteReason" class="form-select" required>
                            <option value="">请选择删除原因</option>
                            <option value="Account no longer needed">账户不再需要</option>
                            <option value="Employee left">员工离职</option>
                            <option value="Device decommissioned">设备停用</option>
                            <option value="Security requirement">安全要求</option>
                            <option value="Duplicate account">重复账户</option>
                            <option value="System cleanup">系统清理</option>
                            <option value="Other">其他原因</option>
                        </select>
                        <span asp-validation-for="DeleteReason" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="DeleteNotes" class="form-label">删除备注</label>
                        <textarea asp-for="DeleteNotes" class="form-control" rows="3" placeholder="请输入删除的详细说明..."></textarea>
                        <span asp-validation-for="DeleteNotes" class="text-danger"></span>
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="ConfirmDelete" class="form-check-input" type="checkbox" required />
                        <label asp-for="ConfirmDelete" class="form-check-label">
                            我确认要删除此账户，并了解此操作不可撤销
                        </label>
                        <span asp-validation-for="ConfirmDelete" class="text-danger d-block"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('您确定要删除此账户吗？此操作不可撤销！')">
                            <i class="bi bi-trash"></i> 确认删除
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">相关数据统计</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="bi bi-database"></i> 将被删除的数据</h6>
                    <ul class="mb-0">
                        <li><strong>密码历史：</strong>@Model.PasswordHistoryCount 条记录</li>
                        <li><strong>邮件日志：</strong>@Model.EmailLogCount 条记录</li>
                        <li><strong>操作日志：</strong>@Model.OperationLogCount 条记录</li>
                    </ul>
                </div>

                @if (Model.Account.PermissionLevel == "SuperAdmin")
                {
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-shield-exclamation"></i> 特别提醒</h6>
                        <p class="mb-0">此账户是<strong>超级管理员</strong>账户，删除后可能影响系统管理功能，请谨慎操作！</p>
                    </div>
                }

                @if (Model.Account.Device != null)
                {
                    <div class="alert alert-info">
                        <h6><i class="bi bi-gear"></i> 设备影响</h6>
                        <p class="mb-0">删除此账户后，设备 <strong>@Model.Account.Device.DeviceCode</strong> 将失去此账户的访问权限。</p>
                    </div>
                }

                @if (Model.Account.Employee != null)
                {
                    <div class="alert alert-info">
                        <h6><i class="bi bi-person"></i> 员工影响</h6>
                        <p class="mb-0">删除此账户后，员工 <strong>@Model.Account.Employee.Name</strong> 将失去此账户的使用权限。</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
