using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class OptimizationModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuditLogService _auditService;
        private readonly ILogger<OptimizationModel> _logger;

        public OptimizationModel(ApplicationDbContext context, IAuditLogService auditService, ILogger<OptimizationModel> logger)
        {
            _context = context;
            _auditService = auditService;
            _logger = logger;
        }

        public int ResponseTime { get; set; }
        public int Throughput { get; set; }
        public decimal ErrorRate { get; set; }
        public decimal Availability { get; set; }
        public List<AuditLog> OptimizationHistory { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            var currentUserRole = HttpContext.Session.GetString("UserRole");
            if (currentUserRole != "SuperAdmin")
            {
                return RedirectToPage("/Account/AccessDenied");
            }

            await LoadPerformanceDataAsync();
            return Page();
        }

        private async Task LoadPerformanceDataAsync()
        {
            try
            {
                // 模拟性能数据
                var random = new Random();
                ResponseTime = random.Next(50, 150); // 50-150ms
                Throughput = random.Next(100, 500); // 100-500 req/s
                ErrorRate = Math.Round((decimal)random.NextDouble() * 2, 2); // 0-2%
                Availability = Math.Round(99.5m + (decimal)random.NextDouble() * 0.5m, 2); // 99.5-100%

                // 获取优化历史（使用审计日志中的优化相关记录）
                OptimizationHistory = await _context.AuditLogs
                    .Include(a => a.User)
                    .Where(a => a.Action.Contains("优化") || a.Action.Contains("清理") || a.Action.Contains("Optimize"))
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(10)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载性能数据失败");
                ResponseTime = 100;
                Throughput = 200;
                ErrorRate = 1.0m;
                Availability = 99.9m;
            }
        }
    }
}
