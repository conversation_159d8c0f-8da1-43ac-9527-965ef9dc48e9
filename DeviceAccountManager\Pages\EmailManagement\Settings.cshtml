@page
@model DeviceAccountManager.Pages.EmailManagement.SettingsModel
@{
    ViewData["Title"] = "邮件设置";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">邮件设置</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回邮件管理
            </a>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
        @Model.Message
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="row">
    <div class="col-md-8">
        <!-- SMTP配置 -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-gear"></i> SMTP服务器配置
                </h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="SmtpHost" class="form-label">SMTP服务器</label>
                            <input asp-for="SmtpHost" class="form-control" placeholder="smtp.example.com" />
                            <span asp-validation-for="SmtpHost" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="SmtpPort" class="form-label">端口</label>
                            <input asp-for="SmtpPort" class="form-control" type="number" placeholder="587" />
                            <span asp-validation-for="SmtpPort" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="SmtpUsername" class="form-label">用户名</label>
                            <input asp-for="SmtpUsername" class="form-control" placeholder="<EMAIL>" />
                            <span asp-validation-for="SmtpUsername" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="SmtpPassword" class="form-label">密码</label>
                            <input asp-for="SmtpPassword" class="form-control" type="password" placeholder="输入SMTP密码" />
                            <span asp-validation-for="SmtpPassword" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="FromEmail" class="form-label">发件人邮箱</label>
                            <input asp-for="FromEmail" class="form-control" placeholder="<EMAIL>" />
                            <span asp-validation-for="FromEmail" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="FromName" class="form-label">发件人名称</label>
                            <input asp-for="FromName" class="form-control" placeholder="设备账户管理系统" />
                            <span asp-validation-for="FromName" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input asp-for="EnableSsl" class="form-check-input" type="checkbox" />
                                <label asp-for="EnableSsl" class="form-check-label">启用SSL/TLS</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input asp-for="EnableEmailNotifications" class="form-check-input" type="checkbox" />
                                <label asp-for="EnableEmailNotifications" class="form-check-label">启用邮件通知</label>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="TestEmail" class="form-label">测试邮箱</label>
                            <input asp-for="TestEmail" class="form-control" placeholder="<EMAIL>" />
                            <small class="form-text text-muted">用于发送测试邮件</small>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="TestName" class="form-label">测试收件人</label>
                            <input asp-for="TestName" class="form-control" placeholder="测试用户" />
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存设置
                            </button>
                            <button type="submit" name="action" value="test" class="btn btn-info">
                                <i class="bi bi-envelope-check"></i> 发送测试邮件
                            </button>
                        </div>
                        <button type="button" class="btn btn-warning" onclick="resetToDefaults()">
                            <i class="bi bi-arrow-clockwise"></i> 恢复默认
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- 邮件统计 -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-bar-chart"></i> 邮件统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">@Model.TotalEmails</h4>
                            <small class="text-muted">总邮件数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@Model.SentEmails</h4>
                        <small class="text-muted">已发送</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning">@Model.PendingEmails</h4>
                            <small class="text-muted">待发送</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger">@Model.FailedEmails</h4>
                        <small class="text-muted">发送失败</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置说明 -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-secondary">
                    <i class="bi bi-info-circle"></i> 配置说明
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>常用SMTP配置：</strong></p>
                    <ul class="list-unstyled">
                        <li><strong>QQ邮箱：</strong><br>
                            服务器：smtp.qq.com<br>
                            端口：587 (SSL)
                        </li>
                        <li><strong>163邮箱：</strong><br>
                            服务器：smtp.163.com<br>
                            端口：25 或 994 (SSL)
                        </li>
                        <li><strong>Gmail：</strong><br>
                            服务器：smtp.gmail.com<br>
                            端口：587 (TLS)
                        </li>
                    </ul>
                    <p class="text-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        请确保SMTP账户已启用第三方应用授权
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function resetToDefaults() {
            if (confirm('确定要恢复默认设置吗？这将清除当前所有配置。')) {
                document.getElementById('SmtpHost').value = '';
                document.getElementById('SmtpPort').value = '587';
                document.getElementById('SmtpUsername').value = '';
                document.getElementById('SmtpPassword').value = '';
                document.getElementById('FromEmail').value = '';
                document.getElementById('FromName').value = '设备账户管理系统';
                document.getElementById('EnableSsl').checked = true;
                document.getElementById('EnableEmailNotifications').checked = true;
                document.getElementById('TestEmail').value = '';
                document.getElementById('TestName').value = '';
            }
        }
    </script>
}
