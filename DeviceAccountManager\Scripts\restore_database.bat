@echo off
echo ================================
echo   Database Restoration Tool
echo ================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: This script requires Administrator privileges
    echo Please run as Administrator
    pause
    exit /b 1
)

:: 设置变量
set CURRENT_DIR=%~dp0
set DB_NAME=DeviceAccountManagerDb
set BACKUP_DIR=%CURRENT_DIR%..\Database

echo Checking SQL Server connection...
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT @@VERSION" >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: Cannot connect to SQL Server Express
    echo Please ensure SQL Server Express is installed and running
    echo.
    echo To install SQL Server Express:
    echo 1. Download from: https://www.microsoft.com/sql-server/sql-server-downloads
    echo 2. Install with default settings
    echo 3. Ensure SQL Server Browser service is running
    pause
    exit /b 1
)
echo SQL Server connection successful
echo.

:: 查找备份文件
echo Looking for database backup files...
if exist "%BACKUP_DIR%\*.bak" (
    echo Found backup files:
    dir /b "%BACKUP_DIR%\*.bak"
    echo.
    
    :: 获取最新的备份文件
    for /f "delims=" %%i in ('dir /b /o-d "%BACKUP_DIR%\*.bak" 2^>nul') do (
        set BACKUP_FILE=%BACKUP_DIR%\%%i
        goto :found_backup
    )
    
    :found_backup
    echo Using backup file: %BACKUP_FILE%
    echo.
    
    :: 检查数据库是否已存在
    sqlcmd -S .\SQLEXPRESS -E -Q "SELECT name FROM sys.databases WHERE name = '%DB_NAME%'" -h -1 | findstr /i "%DB_NAME%" >nul
    if %errorLevel% equ 0 (
        echo Warning: Database '%DB_NAME%' already exists
        set /p confirm="Do you want to overwrite it? (Y/N): "
        if /i not "%confirm%"=="Y" (
            echo Operation cancelled
            pause
            exit /b 0
        )
        
        echo Dropping existing database...
        sqlcmd -S .\SQLEXPRESS -E -Q "ALTER DATABASE [%DB_NAME%] SET SINGLE_USER WITH ROLLBACK IMMEDIATE; DROP DATABASE [%DB_NAME%];"
        if %errorLevel% neq 0 (
            echo Warning: Failed to drop existing database
        )
    )
    
    echo Restoring database from backup...
    sqlcmd -S .\SQLEXPRESS -E -Q "RESTORE DATABASE [%DB_NAME%] FROM DISK = '%BACKUP_FILE%' WITH REPLACE, STATS = 10"
    
    if %errorLevel% equ 0 (
        echo Database restoration completed successfully!
    ) else (
        echo Error: Database restoration failed
        echo Trying alternative restoration method...
        goto :try_create_new
    )
    
) else (
    echo No backup files found in %BACKUP_DIR%
    echo Trying to create new database...
    goto :try_create_new
)

goto :verify_database

:try_create_new
echo Creating new database...

:: 创建数据库
sqlcmd -S .\SQLEXPRESS -E -Q "CREATE DATABASE [%DB_NAME%]"
if %errorLevel% neq 0 (
    echo Error: Failed to create database
    pause
    exit /b 1
)

echo Database created successfully
echo.

:: 检查是否有数据导出文件
if exist "%BACKUP_DIR%\*_export.txt" (
    echo Found data export files, importing data...
    
    :: 这里需要根据实际的表结构来创建表和导入数据
    :: 由于导出的是文本格式，需要手动处理
    echo Note: Data import from text files requires manual processing
    echo Please check the export files in: %BACKUP_DIR%
) else (
    echo No data export files found
    echo Database will be initialized with default data when application starts
)

:verify_database
echo.
echo Verifying database...
sqlcmd -S .\SQLEXPRESS -E -d %DB_NAME% -Q "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"

if %errorLevel% equ 0 (
    echo Database verification successful
    echo.
    echo Database restoration completed!
    echo.
    echo Next steps:
    echo 1. Start the application: dotnet DeviceAccountManager.dll
    echo 2. Access the system: http://localhost:5000
    echo 3. Login with: admin / Admin123!
    echo 4. Change the default password immediately
) else (
    echo Warning: Database verification failed
    echo The application will attempt to initialize the database on first run
)

echo.
pause
