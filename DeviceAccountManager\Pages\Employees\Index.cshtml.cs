using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Employees
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Employee> Employees { get; set; } = default!;
        public IList<string> Departments { get; set; } = default!;
        
        [BindProperty(SupportsGet = true)]
        public string? SearchString { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? DepartmentFilter { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        public int ActiveEmployeesCount { get; set; }
        public int BoundEmployeesCount { get; set; }
        public int UnboundEmployeesCount { get; set; }
        public int TotalEmployeesCount { get; set; }

        public async Task OnGetAsync()
        {
            var employeesQuery = _context.Employees
                .Include(e => e.Accounts)
                .AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(SearchString))
            {
                employeesQuery = employeesQuery.Where(e =>
                    e.Name.Contains(SearchString) ||
                    e.EmployeeCode.Contains(SearchString) ||
                    (e.Email != null && e.Email.Contains(SearchString)) ||
                    (e.Department != null && e.Department.Contains(SearchString)) ||
                    (e.Position != null && e.Position.Contains(SearchString)));
            }

            // 部门过滤
            if (!string.IsNullOrEmpty(DepartmentFilter))
            {
                employeesQuery = employeesQuery.Where(e => e.Department == DepartmentFilter);
            }

            // 状态过滤
            if (!string.IsNullOrEmpty(StatusFilter) && bool.TryParse(StatusFilter, out bool isActive))
            {
                employeesQuery = employeesQuery.Where(e => e.IsActive == isActive);
            }

            Employees = await employeesQuery
                .OrderBy(e => e.EmployeeCode)
                .ToListAsync();

            // 获取所有部门用于筛选下拉框
            Departments = await _context.Employees
                .Where(e => !string.IsNullOrEmpty(e.Department))
                .Select(e => e.Department!)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            // 统计数据
            ActiveEmployeesCount = await _context.Employees.CountAsync(e => e.IsActive);
            BoundEmployeesCount = await _context.Employees
                .CountAsync(e => e.IsActive && e.Accounts.Any(a => a.IsActive));
            UnboundEmployeesCount = await _context.Employees
                .CountAsync(e => e.IsActive && !e.Accounts.Any(a => a.IsActive));
            TotalEmployeesCount = await _context.Employees.CountAsync();
        }
    }
}
