using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using DeviceAccountManager.Services;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages.PasswordManagement
{
    public class BatchChangeModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IPasswordService _passwordService;
        private readonly IEmailService _emailService;

        public BatchChangeModel(ApplicationDbContext context, IPasswordService passwordService, IEmailService emailService)
        {
            _context = context;
            _passwordService = passwordService;
            _emailService = emailService;
        }

        public IList<Account> Accounts { get; set; } = default!;

        [BindProperty]
        public List<int> SelectedAccountIds { get; set; } = new();

        [BindProperty]
        public string PasswordGenerationMode { get; set; } = "Auto";

        [BindProperty]
        public string ManualPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请选择更改原因")]
        public string ChangeReason { get; set; } = string.Empty;

        [BindProperty]
        public string Notes { get; set; } = string.Empty;

        [BindProperty]
        public bool SendEmailNotification { get; set; } = true;

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限：只有工艺员及以上级别可以批量更改密码
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            await LoadAccounts();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (SelectedAccountIds == null || !SelectedAccountIds.Any())
            {
                ModelState.AddModelError("", "请至少选择一个账户");
                await LoadAccounts();
                return Page();
            }

            // 验证手动密码
            if (PasswordGenerationMode == "Manual")
            {
                if (string.IsNullOrEmpty(ManualPassword))
                {
                    ModelState.AddModelError("ManualPassword", "请输入密码");
                    await LoadAccounts();
                    return Page();
                }

                if (!await _passwordService.ValidatePasswordAsync(ManualPassword))
                {
                    ModelState.AddModelError("ManualPassword", "密码不符合安全策略要求");
                    await LoadAccounts();
                    return Page();
                }
            }

            if (!ModelState.IsValid)
            {
                await LoadAccounts();
                return Page();
            }

            var successCount = 0;
            var failureCount = 0;
            var passwordResults = new List<(int AccountId, string Username, string NewPassword, bool Success, string Error)>();

            foreach (var accountId in SelectedAccountIds)
            {
                try
                {
                    var account = await _context.Accounts
                        .Include(a => a.Employee)
                        .Include(a => a.Device)
                        .FirstOrDefaultAsync(a => a.Id == accountId);

                    if (account == null)
                    {
                        failureCount++;
                        passwordResults.Add((accountId, "未知", "", false, "账户不存在"));
                        continue;
                    }

                    string newPassword;
                    if (PasswordGenerationMode == "Manual")
                    {
                        newPassword = ManualPassword;
                    }
                    else
                    {
                        newPassword = await _passwordService.GeneratePasswordAsync();
                    }

                    var success = await _passwordService.ChangePasswordAsync(
                        accountId, 
                        newPassword, 
                        $"批量更改 - {ChangeReason}", 
                        CurrentUserId);

                    if (success)
                    {
                        successCount++;
                        passwordResults.Add((accountId, account.Username, newPassword, true, ""));

                        // 发送邮件通知
                        if (SendEmailNotification && account.Employee != null && !string.IsNullOrEmpty(account.Employee.Email))
                        {
                            await _emailService.SendPasswordChangeNotificationAsync(account, newPassword, $"{ChangeReason}. {Notes}");
                        }
                    }
                    else
                    {
                        failureCount++;
                        passwordResults.Add((accountId, account.Username, "", false, "密码更改失败"));
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    passwordResults.Add((accountId, "未知", "", false, ex.Message));
                }
            }

            // 记录批量操作日志
            var operationLog = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "BatchPasswordChange",
                TargetType = "Account",
                TargetId = string.Join(",", SelectedAccountIds),
                Description = $"批量更改密码：成功 {successCount} 个，失败 {failureCount} 个。原因：{ChangeReason}。备注：{Notes}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(operationLog);

            await _context.SaveChangesAsync();

            // 将结果存储到TempData中，用于显示结果页面
            TempData["BatchChangeResults"] = System.Text.Json.JsonSerializer.Serialize(passwordResults);
            TempData["SuccessCount"] = successCount;
            TempData["FailureCount"] = failureCount;

            return RedirectToPage("./BatchChangeResult");
        }

        private async Task LoadAccounts()
        {
            Accounts = await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .Where(a => a.IsActive)
                .OrderBy(a => a.Username)
                .ToListAsync();
        }
    }
}
