@page
@model DeviceAccountManager.Pages.Employees.IndexModel
@{
    ViewData["Title"] = "员工管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">员工管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Create" class="btn btn-sm btn-primary">
                <i class="bi bi-plus-circle"></i> 添加员工
            </a>
            <a asp-page="./Import" class="btn btn-sm btn-success">
                <i class="bi bi-file-earmark-excel"></i> 批量导入
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-3">
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <input type="text" class="form-control" name="searchString" value="@Model.SearchString" placeholder="搜索员工姓名或工号...">
                <button class="btn btn-outline-secondary" type="submit">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <select class="form-select" name="departmentFilter" onchange="this.form.submit()">
                    <option value="">所有部门</option>
                    @foreach (var dept in Model.Departments)
                    {
                        <option value="@dept" selected="@(Model.DepartmentFilter == dept)">@dept</option>
                    }
                </select>
                <input type="hidden" name="searchString" value="@Model.SearchString" />
            </div>
        </form>
    </div>
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <select class="form-select" name="statusFilter" onchange="this.form.submit()">
                    <option value="">所有状态</option>
                    <option value="true" selected="@(Model.StatusFilter == "true")">在职</option>
                    <option value="false" selected="@(Model.StatusFilter == "false")">离职</option>
                </select>
                <input type="hidden" name="searchString" value="@Model.SearchString" />
                <input type="hidden" name="departmentFilter" value="@Model.DepartmentFilter" />
            </div>
        </form>
    </div>
</div>

<!-- 员工列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">员工列表</h6>
    </div>
    <div class="card-body">
        @if (Model.Employees.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>部门</th>
                            <th>职位</th>
                            <th>邮箱</th>
                            <th>关联账户</th>
                            <th>状态</th>
                            <th>入职时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in Model.Employees)
                        {
                            <tr>
                                <td>
                                    <strong>@employee.EmployeeCode</strong>
                                </td>
                                <td>@employee.Name</td>
                                <td>@employee.Department</td>
                                <td>@employee.Position</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(employee.Email))
                                    {
                                        <a href="mailto:@employee.Email">@employee.Email</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (employee.Accounts != null && employee.Accounts.Any())
                                    {
                                        @foreach (var account in employee.Accounts.Take(2))
                                        {
                                            <span class="badge bg-info me-1">@account.Username</span>
                                        }
                                        @if (employee.Accounts.Count > 2)
                                        {
                                            <span class="badge bg-secondary">+@(employee.Accounts.Count - 2)</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted">无关联</span>
                                    }
                                </td>
                                <td>
                                    @if (employee.IsActive)
                                    {
                                        <span class="badge bg-success">在职</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">离职</span>
                                    }
                                </td>
                                <td>
                                    @if (employee.HireDate.HasValue)
                                    {
                                        @employee.HireDate.Value.ToString("yyyy-MM-dd")
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-page="./Details" asp-route-id="@employee.Id" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="./Edit" asp-route-id="@employee.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        @if (Model.IsSuperAdmin || Model.IsTechnician)
                                        {
                                            <a asp-page="./Delete" asp-route-id="@employee.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-people" style="font-size: 3rem; color: #ccc;"></i>
                <p class="text-muted mt-2">暂无员工数据</p>
                <div>
                    <a asp-page="./Create" class="btn btn-primary me-2">
                        <i class="bi bi-plus-circle"></i> 添加员工
                    </a>
                    <a asp-page="./Import" class="btn btn-success">
                        <i class="bi bi-file-earmark-excel"></i> 批量导入
                    </a>
                </div>
            </div>
        }
    </div>
</div>

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.ActiveEmployeesCount</h5>
                <p class="card-text">在职员工</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@Model.BoundEmployeesCount</h5>
                <p class="card-text">已绑定账户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.UnboundEmployeesCount</h5>
                <p class="card-text">未绑定账户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.TotalEmployeesCount</h5>
                <p class="card-text">总员工数</p>
            </div>
        </div>
    </div>
</div>
