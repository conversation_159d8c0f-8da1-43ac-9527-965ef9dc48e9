using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 用户表 - 系统登录用户
    /// </summary>
    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RealName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 用户角色：SuperAdmin=超级管理员, Technician=工艺员, Maintainer=维护者, Operator=操作者
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Role { get; set; } = string.Empty;

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? LastLoginAt { get; set; }

        // 导航属性
        public virtual ICollection<OperationLog> OperationLogs { get; set; } = new List<OperationLog>();
    }
}
