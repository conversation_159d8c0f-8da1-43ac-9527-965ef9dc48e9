@echo off
echo ================================
echo   Windows Service Installation
echo ================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: This script requires Administrator privileges
    echo Please run as Administrator
    pause
    exit /b 1
)

set SERVICE_NAME=DeviceAccountManager
set SERVICE_DISPLAY_NAME=设备账号权限管理系统
set CURRENT_DIR=%~dp0
set APP_DIR=%CURRENT_DIR%..
set APP_EXE=%APP_DIR%\DeviceAccountManager.exe

echo Installing Windows Service: %SERVICE_NAME%
echo Application path: %APP_EXE%
echo.

:: 检查应用程序文件
if not exist "%APP_EXE%" (
    echo Error: Application executable not found: %APP_EXE%
    echo.
    echo Looking for DLL version...
    set APP_EXE=%APP_DIR%\DeviceAccountManager.dll
    if not exist "%APP_EXE%" (
        echo Error: Application files not found
        echo Please ensure the application is properly deployed
        pause
        exit /b 1
    ) else (
        echo Found DLL version: %APP_EXE%
        set USE_DLL=1
    )
)

:: 检查服务是否已存在
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Service already exists. Stopping and removing...
    
    :: 停止服务
    net stop "%SERVICE_NAME%" >nul 2>&1
    if %errorLevel% equ 0 (
        echo Service stopped
    ) else (
        echo Service was not running
    )
    
    :: 删除服务
    sc delete "%SERVICE_NAME%" >nul 2>&1
    if %errorLevel% equ 0 (
        echo Old service removed
    ) else (
        echo Warning: Failed to remove old service
    )
    
    :: 等待服务完全删除
    timeout /t 3 /nobreak >nul
)

:: 创建服务
echo Creating Windows Service...

if defined USE_DLL (
    :: 使用dotnet运行DLL
    sc create "%SERVICE_NAME%" binPath= "dotnet \"%APP_EXE%\"" DisplayName= "%SERVICE_DISPLAY_NAME%" start= auto
) else (
    :: 直接运行EXE
    sc create "%SERVICE_NAME%" binPath= "\"%APP_EXE%\"" DisplayName= "%SERVICE_DISPLAY_NAME%" start= auto
)

if %errorLevel% neq 0 (
    echo Error: Failed to create service
    pause
    exit /b 1
)

echo Service created successfully
echo.

:: 配置服务描述
sc description "%SERVICE_NAME%" "生产设备账号权限管理系统 - 提供设备账户管理、权限控制、密码管理等功能"

:: 配置服务恢复选项
echo Configuring service recovery options...
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/60000/restart/60000/restart/60000

:: 设置服务启动目录
if defined USE_DLL (
    sc config "%SERVICE_NAME%" binPath= "dotnet \"%APP_EXE%\"" start= auto obj= "LocalSystem"
) else (
    sc config "%SERVICE_NAME%" binPath= "\"%APP_EXE%\"" start= auto obj= "LocalSystem"
)

echo Service configuration completed
echo.

:: 启动服务
echo Starting service...
net start "%SERVICE_NAME%"

if %errorLevel% equ 0 (
    echo Service started successfully!
    echo.
    
    :: 等待服务启动
    echo Waiting for service to initialize...
    timeout /t 10 /nobreak >nul
    
    :: 检查服务状态
    sc query "%SERVICE_NAME%" | findstr "RUNNING" >nul
    if %errorLevel% equ 0 (
        echo Service is running properly
        
        :: 测试HTTP连接
        echo Testing HTTP connection...
        timeout /t 5 /nobreak >nul
        curl -s http://localhost:5000 >nul 2>&1
        if %errorLevel% equ 0 (
            echo HTTP service is responding
            echo.
            echo ================================
            echo Installation completed successfully!
            echo.
            echo Service Name: %SERVICE_NAME%
            echo Service Status: Running
            echo Access URL: http://localhost:5000
            echo.
            echo To manage the service:
            echo   Start:   net start %SERVICE_NAME%
            echo   Stop:    net stop %SERVICE_NAME%
            echo   Restart: net stop %SERVICE_NAME% ^&^& net start %SERVICE_NAME%
            echo.
            echo To uninstall the service:
            echo   Run: %CURRENT_DIR%\uninstall_service.bat
        ) else (
            echo Warning: HTTP service is not responding
            echo Please check the application logs
        )
    ) else (
        echo Warning: Service may not be running properly
        echo Please check Windows Event Log for details
    )
) else (
    echo Error: Failed to start service
    echo.
    echo Troubleshooting steps:
    echo 1. Check application configuration
    echo 2. Verify database connection
    echo 3. Check Windows Event Log
    echo 4. Try running manually: dotnet "%APP_EXE%"
)

echo.
pause
