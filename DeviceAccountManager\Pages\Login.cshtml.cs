using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages
{
    public class LoginModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public LoginModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        [Required(ErrorMessage = "请输入用户名")]
        public string Username { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请输入密码")]
        public string Password { get; set; } = string.Empty;

        public string ErrorMessage { get; set; } = string.Empty;

        public IActionResult OnGet()
        {
            // 如果已经登录，重定向到首页
            if (HttpContext.Session.GetString("UserId") != null)
            {
                return RedirectToPage("/Index");
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == Username && u.IsActive);

                if (user == null)
                {
                    ErrorMessage = "用户名或密码错误";
                    return Page();
                }

                // 验证密码
                if (!BCrypt.Net.BCrypt.Verify(Password, user.PasswordHash))
                {
                    ErrorMessage = "用户名或密码错误";
                    return Page();
                }

                // 登录成功，设置Session
                HttpContext.Session.SetString("UserId", user.Id.ToString());
                HttpContext.Session.SetString("Username", user.Username);
                HttpContext.Session.SetString("RealName", user.RealName);
                HttpContext.Session.SetString("Role", user.Role);

                // 更新最后登录时间
                user.LastLoginAt = DateTime.Now;
                await _context.SaveChangesAsync();

                // 记录登录日志
                var log = new OperationLog
                {
                    UserId = user.Id,
                    Operation = "Login",
                    TargetType = "User",
                    TargetId = user.Id.ToString(),
                    Description = "用户登录系统",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                    UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    CreatedAt = DateTime.Now
                };
                _context.OperationLogs.Add(log);
                await _context.SaveChangesAsync();

                return RedirectToPage("/Index");
            }
            catch (Exception)
            {
                ErrorMessage = "登录过程中发生错误，请稍后重试";
                // 这里可以记录详细的错误日志
                return Page();
            }
        }
    }
}
