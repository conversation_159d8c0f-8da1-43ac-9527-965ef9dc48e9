using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using BCrypt.Net;

namespace DeviceAccountManager.Pages.Accounts
{
    public class CreateModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IActionResult OnGet()
        {
            // 检查权限：只有技术员及以上级别可以添加账户
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }
            
            LoadSelectLists();
            return Page();
        }

        [BindProperty]
        public Account Account { get; set; } = default!;

        public SelectList DeviceSelectList { get; set; } = default!;
        public SelectList EmployeeSelectList { get; set; } = default!;

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                LoadSelectLists();
                return Page();
            }

            // 检查账户名是否已存在
            var existingAccount = await _context.Accounts
                .FirstOrDefaultAsync(a => a.Username == Account.Username);
            
            if (existingAccount != null)
            {
                ModelState.AddModelError("Account.Username", "账户名已存在，请使用其他账户名");
                LoadSelectLists();
                return Page();
            }

            // 验证密码强度
            if (!IsPasswordStrong(Account.Password))
            {
                ModelState.AddModelError("Account.Password", "密码强度不足，请确保包含大小写字母、数字和特殊字符，长度至少8位");
                LoadSelectLists();
                return Page();
            }

            // 加密密码
            Account.Password = BCrypt.Net.BCrypt.HashPassword(Account.Password);
            
            // 设置创建信息
            Account.CreatedAt = DateTime.Now;
            Account.UpdatedAt = DateTime.Now;
            
            // 设置下次密码更改时间（90天后）
            if (!Account.IsExemptFromPasswordChange)
            {
                Account.NextPasswordChangeAt = DateTime.Now.AddDays(90);
            }

            _context.Accounts.Add(Account);
            await _context.SaveChangesAsync();

            // 记录密码历史
            var passwordHistory = new PasswordHistory
            {
                AccountId = Account.Id,
                NewPassword = Account.Password,
                OldPassword = "",
                ChangeDate = DateTime.Now,
                ChangedBy = CurrentUserId.ToString(),
                ChangeReason = "Initial password creation"
            };
            _context.PasswordHistories.Add(passwordHistory);

            // 记录操作日志
            var log = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "Create",
                TargetType = "Account",
                TargetId = Account.Id.ToString(),
                Description = $"创建账户：{Account.Username}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(log);
            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }

        private void LoadSelectLists()
        {
            DeviceSelectList = new SelectList(_context.Devices
                .Where(d => d.Status == "Active")
                .OrderBy(d => d.DeviceCode), "Id", "DeviceCode");
                
            EmployeeSelectList = new SelectList(_context.Employees
                .Where(e => e.IsActive)
                .OrderBy(e => e.Name), "Id", "Name");
        }

        private bool IsPasswordStrong(string password)
        {
            if (string.IsNullOrEmpty(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }
    }
}
