@page
@model DeviceAccountManager.Pages.Accounts.DetailsModel
@{
    ViewData["Title"] = "账户详情";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">账户详情</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            @if (Model.IsMaintainer)
            {
                <a asp-page="./Edit" asp-route-id="@Model.Account.Id" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-pencil"></i> 编辑
                </a>
                <a asp-page="./ChangePassword" asp-route-id="@Model.Account.Id" class="btn btn-sm btn-warning">
                    <i class="bi bi-key"></i> 更改密码
                </a>
            }
            @if (Model.IsSuperAdmin || Model.IsTechnician)
            {
                <a asp-page="./Delete" asp-route-id="@Model.Account.Id" class="btn btn-sm btn-outline-danger">
                    <i class="bi bi-trash"></i> 删除
                </a>
            }
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">基本信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">账户名：</dt>
                            <dd class="col-sm-8">
                                <strong>@Model.Account.Username</strong>
                                @if (Model.Account.IsActive)
                                {
                                    <span class="badge bg-success ms-2">启用</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger ms-2">禁用</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-4">权限级别：</dt>
                            <dd class="col-sm-8">
                                @switch (Model.Account.PermissionLevel)
                                {
                                    case "SuperAdmin":
                                        <span class="badge bg-danger">超级管理员</span>
                                        break;
                                    case "Technician":
                                        <span class="badge bg-warning">工艺员</span>
                                        break;
                                    case "Maintainer":
                                        <span class="badge bg-info">维护者</span>
                                        break;
                                    case "Operator":
                                        <span class="badge bg-secondary">操作者</span>
                                        break;
                                    default:
                                        <span class="badge bg-light text-dark">@Model.Account.PermissionLevel</span>
                                        break;
                                }
                            </dd>

                            <dt class="col-sm-4">关联设备：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.Device != null)
                                {
                                    <span>@Model.Account.Device.DeviceCode</span>
                                    <br><small class="text-muted">@Model.Account.Device.DeviceName</small>
                                }
                                else
                                {
                                    <span class="text-muted">未关联设备</span>
                                }
                            </dd>

                            <dt class="col-sm-4">绑定员工：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.Employee != null)
                                {
                                    <span>@Model.Account.Employee.Name</span>
                                    <br><small class="text-muted">@Model.Account.Employee.EmployeeCode</small>
                                }
                                else
                                {
                                    <span class="text-muted">未绑定员工</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">创建时间：</dt>
                            <dd class="col-sm-8">@Model.Account.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>

                            <dt class="col-sm-4">更新时间：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.UpdatedAt.HasValue)
                                {
                                    @Model.Account.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")
                                }
                                else
                                {
                                    <span class="text-muted">未更新</span>
                                }
                            </dd>

                            <dt class="col-sm-4">密码更改：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.IsExemptFromPasswordChange)
                                {
                                    <span class="badge bg-info">免于更改</span>
                                }
                                else
                                {
                                    <span class="badge bg-warning">需要更改</span>
                                }
                            </dd>

                            <dt class="col-sm-4">上次密码更改：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.LastPasswordChangeAt.HasValue)
                                {
                                    @Model.Account.LastPasswordChangeAt.Value.ToString("yyyy-MM-dd")
                                }
                                else
                                {
                                    <span class="text-muted">从未更改</span>
                                }
                            </dd>

                            <dt class="col-sm-4">下次密码更改：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Account.NextPasswordChangeAt.HasValue)
                                {
                                    @Model.Account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")
                                    @if (Model.Account.NextPasswordChangeAt.Value < DateTime.Now)
                                    {
                                        <span class="badge bg-danger ms-2">已过期</span>
                                    }
                                    else if (Model.Account.NextPasswordChangeAt.Value < DateTime.Now.AddDays(7))
                                    {
                                        <span class="badge bg-warning ms-2">即将过期</span>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">无需更改</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Account.Description))
                {
                    <div class="mt-3">
                        <h6>账户描述：</h6>
                        <p class="text-muted">@Model.Account.Description</p>
                    </div>
                }
            </div>
        </div>

        <!-- 密码历史记录 -->
        @if (Model.PasswordHistories.Any())
        {
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">密码更改历史</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>更改时间</th>
                                    <th>更改原因</th>
                                    <th>操作人</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var history in Model.PasswordHistories.OrderByDescending(h => h.ChangeDate).Take(10))
                                {
                                    <tr>
                                        <td>@history.ChangeDate.ToString("yyyy-MM-dd HH:mm")</td>
                                        <td>@history.ChangeReason</td>
                                        <td>@history.ChangedBy</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    @if (Model.PasswordHistories.Count > 10)
                    {
                        <p class="text-muted mt-2">显示最近10条记录，共@Model.PasswordHistories.Count条</p>
                    }
                </div>
            </div>
        }
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">快速操作</h6>
            </div>
            <div class="card-body">
                @if (Model.IsMaintainer)
                {
                    <div class="d-grid gap-2">
                        <a asp-page="./Edit" asp-route-id="@Model.Account.Id" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> 编辑账户
                        </a>
                        <a asp-page="./ChangePassword" asp-route-id="@Model.Account.Id" class="btn btn-warning">
                            <i class="bi bi-key"></i> 更改密码
                        </a>
                    </div>
                    <hr>
                }

                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 权限说明</h6>
                    <p class="mb-0">
                        @switch (Model.Account.PermissionLevel)
                        {
                            case "SuperAdmin":
                                <span>拥有系统完全管理权限，可以执行所有操作。</span>
                                break;
                            case "Technician":
                                <span>拥有工艺调整权限，可以管理设备和账户。</span>
                                break;
                            case "Maintainer":
                                <span>拥有设备维护权限，可以管理员工和基本操作。</span>
                                break;
                            case "Operator":
                                <span>拥有基本操作权限，可以查看信息和执行基本操作。</span>
                                break;
                            default:
                                <span>未知权限级别。</span>
                                break;
                        }
                    </p>
                </div>

                @if (Model.Account.Device != null)
                {
                    <div class="alert alert-success">
                        <h6><i class="bi bi-gear"></i> 设备信息</h6>
                        <p><strong>设备编号：</strong>@Model.Account.Device.DeviceCode</p>
                        <p><strong>设备名称：</strong>@Model.Account.Device.DeviceName</p>
                        <p class="mb-0"><strong>设备状态：</strong>
                            @if (Model.Account.Device.Status == "Active")
                            {
                                <span class="badge bg-success">运行中</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">@Model.Account.Device.Status</span>
                            }
                        </p>
                    </div>
                }

                @if (Model.Account.Employee != null)
                {
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-person"></i> 员工信息</h6>
                        <p><strong>员工姓名：</strong>@Model.Account.Employee.Name</p>
                        <p><strong>员工工号：</strong>@Model.Account.Employee.EmployeeCode</p>
                        @if (!string.IsNullOrEmpty(Model.Account.Employee.Department))
                        {
                            <p><strong>所属部门：</strong>@Model.Account.Employee.Department</p>
                        }
                        <p class="mb-0"><strong>员工状态：</strong>
                            @if (Model.Account.Employee.IsActive)
                            {
                                <span class="badge bg-success">在职</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">离职</span>
                            }
                        </p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
