using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages;

public class IndexModel : AuthorizedPageModel
{
    private readonly ApplicationDbContext _context;

    public IndexModel(ApplicationDbContext context)
    {
        _context = context;
    }

    public int TotalDevices { get; set; }
    public int TotalAccounts { get; set; }
    public int TotalEmployees { get; set; }
    public int PendingPasswordChanges { get; set; }
    public int RecentEmailsSent { get; set; }

    public async Task OnGetAsync()
    {
        // 统计数据
        TotalDevices = await _context.Devices.CountAsync(d => d.Status == "Active");
        TotalAccounts = await _context.Accounts.CountAsync(a => a.IsActive);
        TotalEmployees = await _context.Employees.CountAsync(e => e.IsActive);

        // 计算需要更改密码的账户数量（下次更改时间已过期的）
        PendingPasswordChanges = await _context.Accounts
            .CountAsync(a => a.IsActive && !a.IsExemptFromPasswordChange &&
                           a.NextPasswordChangeAt.HasValue && a.NextPasswordChangeAt <= DateTime.Now);

        // 最近7天发送的邮件数量
        var sevenDaysAgo = DateTime.Now.AddDays(-7);
        RecentEmailsSent = await _context.EmailLogs
            .CountAsync(e => e.Status == "Sent" && e.SentAt >= sevenDaysAgo);
    }
}
