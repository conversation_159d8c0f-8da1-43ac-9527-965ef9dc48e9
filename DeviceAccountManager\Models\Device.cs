using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 设备表
    /// </summary>
    public class Device
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string DeviceCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string DeviceName { get; set; } = string.Empty;

        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 产线名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProductionLine { get; set; } = string.Empty;

        /// <summary>
        /// 设备位置
        /// </summary>
        [StringLength(100)]
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// 设备状态：Active=正常, Inactive=停用, Maintenance=维护中
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active";

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // 导航属性
        public virtual ICollection<Account> Accounts { get; set; } = new List<Account>();
    }
}
