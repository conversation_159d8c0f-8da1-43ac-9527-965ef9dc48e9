using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Models;

namespace DeviceAccountManager.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Device> Devices { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<PasswordHistory> PasswordHistories { get; set; }
        public DbSet<OperationLog> OperationLogs { get; set; }
        public DbSet<EmailLog> EmailLogs { get; set; }
        public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<SystemBackup> SystemBackups { get; set; }
        public DbSet<UserPreference> UserPreferences { get; set; }
        public DbSet<PasswordPolicy> PasswordPolicies { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置索引
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<Device>()
                .HasIndex(d => d.DeviceCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();

            modelBuilder.Entity<Account>()
                .HasIndex(a => new { a.DeviceId, a.Username })
                .IsUnique();

            // 配置关系
            modelBuilder.Entity<Account>()
                .HasOne(a => a.Device)
                .WithMany(d => d.Accounts)
                .HasForeignKey(a => a.DeviceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Account>()
                .HasOne(a => a.Employee)
                .WithMany(e => e.Accounts)
                .HasForeignKey(a => a.EmployeeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<PasswordHistory>()
                .HasOne(ph => ph.Account)
                .WithMany(a => a.PasswordHistories)
                .HasForeignKey(ph => ph.AccountId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OperationLog>()
                .HasOne(ol => ol.User)
                .WithMany(u => u.OperationLogs)
                .HasForeignKey(ol => ol.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<EmailLog>()
                .HasOne(el => el.Account)
                .WithMany()
                .HasForeignKey(el => el.AccountId)
                .OnDelete(DeleteBehavior.SetNull);

            // 种子数据
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // 创建默认超级管理员用户
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = "$2a$11$8K1p/a0dL2LkqvMA5/YL4.dXd7Q8QHcQqU5qF5JJXJ5J5J5J5J5J5J",
                    RealName = "系统管理员",
                    Email = "<EMAIL>",
                    Role = "SuperAdmin",
                    IsActive = true,
                    CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );
        }
    }
}
