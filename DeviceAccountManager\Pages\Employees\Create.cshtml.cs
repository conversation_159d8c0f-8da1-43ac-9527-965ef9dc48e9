using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Employees
{
    public class CreateModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public CreateModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IActionResult OnGet()
        {
            // 检查权限：只有维护者及以上级别可以添加员工
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }
            
            LoadExistingDepartments();
            return Page();
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        public IList<string> ExistingDepartments { get; set; } = default!;

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                LoadExistingDepartments();
                return Page();
            }

            // 检查员工工号是否已存在
            var existingEmployee = await _context.Employees
                .FirstOrDefaultAsync(e => e.EmployeeCode == Employee.EmployeeCode);

            if (existingEmployee != null)
            {
                ModelState.AddModelError("Employee.EmployeeCode", "员工工号已存在，请使用其他工号");
                LoadExistingDepartments();
                return Page();
            }

            // 验证邮箱格式（如果提供了邮箱）
            if (!string.IsNullOrEmpty(Employee.Email) && !IsValidEmail(Employee.Email))
            {
                ModelState.AddModelError("Employee.Email", "邮箱格式不正确");
                LoadExistingDepartments();
                return Page();
            }

            // 设置创建信息
            Employee.CreatedAt = DateTime.Now;
            Employee.UpdatedAt = DateTime.Now;

            _context.Employees.Add(Employee);
            await _context.SaveChangesAsync();

            // 记录操作日志
            var log = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "Create",
                TargetType = "Employee",
                TargetId = Employee.Id.ToString(),
                Description = $"创建员工：{Employee.EmployeeCode} - {Employee.Name}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(log);
            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }

        private void LoadExistingDepartments()
        {
            ExistingDepartments = _context.Employees
                .Where(e => !string.IsNullOrEmpty(e.Department))
                .Select(e => e.Department!)
                .Distinct()
                .OrderBy(d => d)
                .ToList();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
