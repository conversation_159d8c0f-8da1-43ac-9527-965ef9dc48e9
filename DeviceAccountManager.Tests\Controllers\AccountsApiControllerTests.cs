using Xunit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using DeviceAccountManager.Controllers;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Tests.Controllers
{
    public class AccountsApiControllerTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<IPasswordService> _mockPasswordService;
        private readonly Mock<ILogger<AccountsApiController>> _mockLogger;
        private readonly AccountsApiController _controller;

        public AccountsApiControllerTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _mockPasswordService = new Mock<IPasswordService>();
            _mockLogger = new Mock<ILogger<AccountsApiController>>();
            
            _controller = new AccountsApiController(_context, _mockPasswordService.Object, _mockLogger.Object);
            
            // 模拟HttpContext和Session
            var httpContext = new DefaultHttpContext();
            var session = new Mock<ISession>();
            httpContext.Session = session.Object;
            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = httpContext
            };
        }

        [Fact]
        public async Task GetAccounts_ShouldReturnAllAccounts()
        {
            // Arrange
            var accounts = new[]
            {
                new Account { Id = 1, Username = "user1", IsActive = true },
                new Account { Id = 2, Username = "user2", IsActive = true },
                new Account { Id = 3, Username = "user3", IsActive = false }
            };

            _context.Users.AddRange(accounts);
            await _context.SaveChangesAsync();

            // Act
            var result = await _controller.GetAccounts();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value;
            
            // 使用反射获取data属性
            var dataProperty = response?.GetType().GetProperty("data");
            var data = dataProperty?.GetValue(response) as IEnumerable<object>;
            
            Assert.NotNull(data);
            Assert.Equal(3, data.Count());
        }

        [Fact]
        public async Task GetAccount_ExistingId_ShouldReturnAccount()
        {
            // Arrange
            var account = new Account 
            { 
                Id = 1, 
                Username = "testuser", 
                Email = "<EMAIL>",
                IsActive = true 
            };

            _context.Users.Add(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _controller.GetAccount(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task GetAccount_NonExistingId_ShouldReturnNotFound()
        {
            // Act
            var result = await _controller.GetAccount(999);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task CreateAccount_ValidData_ShouldCreateAccount()
        {
            // Arrange
            var employee = new Employee
            {
                Id = 1,
                EmployeeCode = "EMP001",
                Name = "Test Employee",
                Department = "IT",
                Email = "<EMAIL>"
            };
            _context.Employees.Add(employee);
            await _context.SaveChangesAsync();

            var request = new
            {
                Username = "newuser",
                Email = "<EMAIL>",
                Role = "Operator",
                EmployeeId = 1
            };

            _mockPasswordService.Setup(x => x.GeneratePassword(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .Returns("GeneratedPassword123!");

            // 模拟Session返回SuperAdmin权限
            var mockSession = new Mock<ISession>();
            mockSession.Setup(x => x.GetString("UserRole")).Returns("SuperAdmin");
            mockSession.Setup(x => x.GetString("Username")).Returns("admin");
            _controller.HttpContext.Session = mockSession.Object;

            // Act
            var result = await _controller.CreateAccount(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            
            var createdAccount = await _context.Users.FirstOrDefaultAsync(u => u.Username == "newuser");
            Assert.NotNull(createdAccount);
            Assert.Equal("newuser", createdAccount.Username);
            Assert.Equal("<EMAIL>", createdAccount.Email);
            Assert.Equal("Operator", createdAccount.Role);
        }

        [Fact]
        public async Task CreateAccount_DuplicateUsername_ShouldReturnBadRequest()
        {
            // Arrange
            var existingAccount = new Account 
            { 
                Username = "existinguser", 
                Email = "<EMAIL>",
                IsActive = true 
            };
            _context.Users.Add(existingAccount);
            await _context.SaveChangesAsync();

            var request = new
            {
                Username = "existinguser",
                Email = "<EMAIL>",
                Role = "Operator",
                EmployeeId = 1
            };

            // 模拟Session返回SuperAdmin权限
            var mockSession = new Mock<ISession>();
            mockSession.Setup(x => x.GetString("UserRole")).Returns("SuperAdmin");
            _controller.HttpContext.Session = mockSession.Object;

            // Act
            var result = await _controller.CreateAccount(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task UpdateAccount_ValidData_ShouldUpdateAccount()
        {
            // Arrange
            var account = new Account 
            { 
                Id = 1,
                Username = "testuser", 
                Email = "<EMAIL>",
                Role = "Operator",
                IsActive = true 
            };
            _context.Users.Add(account);
            await _context.SaveChangesAsync();

            var request = new
            {
                Email = "<EMAIL>",
                Role = "Maintainer",
                IsActive = true
            };

            // 模拟Session返回SuperAdmin权限
            var mockSession = new Mock<ISession>();
            mockSession.Setup(x => x.GetString("UserRole")).Returns("SuperAdmin");
            mockSession.Setup(x => x.GetString("Username")).Returns("admin");
            _controller.HttpContext.Session = mockSession.Object;

            // Act
            var result = await _controller.UpdateAccount(1, request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            
            var updatedAccount = await _context.Users.FindAsync(1);
            Assert.NotNull(updatedAccount);
            Assert.Equal("<EMAIL>", updatedAccount.Email);
            Assert.Equal("Maintainer", updatedAccount.Role);
        }

        [Fact]
        public async Task DeleteAccount_ExistingAccount_ShouldDeleteAccount()
        {
            // Arrange
            var account = new Account 
            { 
                Id = 1,
                Username = "testuser", 
                Email = "<EMAIL>",
                IsActive = true 
            };
            _context.Users.Add(account);
            await _context.SaveChangesAsync();

            // 模拟Session返回SuperAdmin权限
            var mockSession = new Mock<ISession>();
            mockSession.Setup(x => x.GetString("UserRole")).Returns("SuperAdmin");
            mockSession.Setup(x => x.GetString("Username")).Returns("admin");
            _controller.HttpContext.Session = mockSession.Object;

            // Act
            var result = await _controller.DeleteAccount(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            
            var deletedAccount = await _context.Users.FindAsync(1);
            Assert.Null(deletedAccount);
        }

        [Fact]
        public async Task GeneratePassword_ShouldReturnGeneratedPassword()
        {
            // Arrange
            var expectedPassword = "GeneratedPassword123!";
            _mockPasswordService.Setup(x => x.GeneratePassword(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .Returns(expectedPassword);

            var request = new
            {
                Length = 12,
                IncludeUppercase = true,
                IncludeLowercase = true,
                IncludeNumbers = true,
                IncludeSpecialChars = true
            };

            // Act
            var result = _controller.GeneratePassword(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value;
            
            // 验证返回的密码
            var passwordProperty = response?.GetType().GetProperty("password");
            var password = passwordProperty?.GetValue(response) as string;
            
            Assert.Equal(expectedPassword, password);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
