@echo off
echo ================================
echo   Creating Simple Installer
echo ================================
echo.

:: 设置变量
set CURRENT_DIR=%~dp0
set PROJECT_ROOT=%CURRENT_DIR%..
set SOURCE_DIR=%PROJECT_ROOT%\publish
set OUTPUT_DIR=%CURRENT_DIR%output
set INSTALLER_NAME=DeviceAccountManager_Setup

:: 创建输出目录
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
if exist "%OUTPUT_DIR%\%INSTALLER_NAME%" rmdir /s /q "%OUTPUT_DIR%\%INSTALLER_NAME%"
mkdir "%OUTPUT_DIR%\%INSTALLER_NAME%"

echo [1/4] Preparing installer files...

:: 复制应用程序文件
if exist "%SOURCE_DIR%" (
    echo Copying application files...
    xcopy "%SOURCE_DIR%\*" "%OUTPUT_DIR%\%INSTALLER_NAME%\App\" /E /I /Y >nul
) else (
    echo Warning: Publish directory not found, using current build...
    xcopy "%PROJECT_ROOT%\bin\Release\net10.0\*" "%OUTPUT_DIR%\%INSTALLER_NAME%\App\" /E /I /Y >nul
)

:: 复制管理脚本
echo Copying management scripts...
xcopy "%PROJECT_ROOT%\Scripts\*" "%OUTPUT_DIR%\%INSTALLER_NAME%\Scripts\" /E /I /Y >nul

echo Files prepared
echo.

echo [2/4] Creating installer script...

:: 创建主安装脚本
echo @echo off > "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo title 设备账户权限管理系统 - 安装程序 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo color 0A >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ================================================ >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo           设备账户权限管理系统 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo           专业安装程序 v1.0 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ================================================ >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 欢迎使用设备账户权限管理系统安装程序 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 本程序将为您自动安装和配置系统 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo pause >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 检查管理员权限
echo echo [1/6] 检查管理员权限... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo net session ^>nul 2^>^&1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo if %%errorLevel%% neq 0 ^( >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 错误：需要管理员权限 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 请右键选择"以管理员身份运行" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     pause >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     exit /b 1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo ^) >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ 管理员权限确认 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 检查系统要求
echo echo [2/6] 检查系统要求... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo dotnet --version ^>nul 2^>^&1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo if %%errorLevel%% neq 0 ^( >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 错误：未找到.NET Runtime >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 请先安装.NET 10.0 Runtime >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 下载地址：https://dotnet.microsoft.com/download >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     pause >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     exit /b 1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo ^) >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ .NET Runtime 已安装 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 创建安装目录
echo echo [3/6] 创建安装目录... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo set INSTALL_PATH=C:\Program Files\DeviceAccountManager >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo if exist "%%INSTALL_PATH%%" ^( >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     echo 检测到已安装版本，正在备份... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     if exist "%%INSTALL_PATH%%_backup" rmdir /s /q "%%INSTALL_PATH%%_backup" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo     move "%%INSTALL_PATH%%" "%%INSTALL_PATH%%_backup" ^>nul >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo ^) >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo mkdir "%%INSTALL_PATH%%" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ 安装目录已创建：%%INSTALL_PATH%% >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 复制文件
echo echo [4/6] 安装应用程序文件... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo xcopy "App\*" "%%INSTALL_PATH%%\" /E /I /Y ^>nul >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo xcopy "Scripts\*" "%%INSTALL_PATH%%\Scripts\" /E /I /Y ^>nul >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ 文件安装完成 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 配置系统
echo echo [5/6] 配置系统环境... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo cd /d "%%INSTALL_PATH%%\Scripts" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo call configure_new_environment.bat >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo call restore_database.bat >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ 系统配置完成 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 创建快捷方式
echo echo [6/6] 创建快捷方式... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo powershell -command "$$WshShell = New-Object -comObject WScript.Shell; $$Shortcut = $$WshShell.CreateShortcut('%%USERPROFILE%%\Desktop\设备账户权限管理系统.lnk'); $$Shortcut.TargetPath = 'http://localhost:5000'; $$Shortcut.Save()" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ✓ 桌面快捷方式已创建 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

:: 完成安装
echo echo ================================================ >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo           安装完成！ >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo ================================================ >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 系统已成功安装到：%%INSTALL_PATH%% >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 访问地址：http://localhost:5000 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 管理员账户：admin >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 默认密码：Admin123! >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 正在启动系统... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo cd /d "%%INSTALL_PATH%%" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo start http://localhost:5000 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo start /b dotnet DeviceAccountManager.dll >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo echo 安装程序将在10秒后自动关闭... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"
echo timeout /t 10 /nobreak ^>nul >> "%OUTPUT_DIR%\%INSTALLER_NAME%\INSTALL.bat"

echo Installer script created
echo.

echo [3/4] Creating uninstaller...

:: 创建卸载脚本
echo @echo off > "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo title 设备账户权限管理系统 - 卸载程序 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo echo 正在卸载设备账户权限管理系统... >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo net stop DeviceAccountManager ^>nul 2^>^&1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo sc delete DeviceAccountManager ^>nul 2^>^&1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo if exist "C:\Program Files\DeviceAccountManager" rmdir /s /q "C:\Program Files\DeviceAccountManager" >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo del "%%USERPROFILE%%\Desktop\设备账户权限管理系统.lnk" ^>nul 2^>^&1 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo echo 卸载完成 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"
echo pause >> "%OUTPUT_DIR%\%INSTALLER_NAME%\UNINSTALL.bat"

echo Uninstaller created
echo.

echo [4/4] Creating distribution package...

:: 创建说明文件
echo 设备账户权限管理系统 > "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo ==================== >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 安装说明： >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 1. 右键以管理员身份运行 INSTALL.bat >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 2. 按照提示完成安装 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 3. 访问 http://localhost:5000 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 4. 使用 admin / Admin123! 登录 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 卸载说明： >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 运行 UNINSTALL.bat >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo. >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo 系统要求： >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo - Windows 10/11 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo - .NET 10.0 Runtime >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"
echo - 管理员权限 >> "%OUTPUT_DIR%\%INSTALLER_NAME%\README.txt"

:: 创建最终安装包
echo Creating final installer package...
powershell -command "Compress-Archive -Path '%OUTPUT_DIR%\%INSTALLER_NAME%\*' -DestinationPath '%OUTPUT_DIR%\%INSTALLER_NAME%.zip' -Force"

echo.
echo ================================
echo Simple Installer Created!
echo ================================
echo.
echo 📦 安装包位置：%OUTPUT_DIR%\%INSTALLER_NAME%.zip
echo 📁 安装文件夹：%OUTPUT_DIR%\%INSTALLER_NAME%\
echo.
echo 🎯 使用方法：
echo 1. 将 %INSTALLER_NAME%.zip 发送给用户
echo 2. 用户解压后运行 INSTALL.bat
echo 3. 按照向导完成安装
echo.
echo ✨ 特点：
echo - 无需额外工具，纯批处理实现
echo - 自动检查系统要求
echo - 自动配置环境
echo - 创建桌面快捷方式
echo - 包含完整卸载程序
echo.

for %%f in ("%OUTPUT_DIR%\%INSTALLER_NAME%.zip") do echo 📊 文件大小：%%~zf 字节

echo.
pause
