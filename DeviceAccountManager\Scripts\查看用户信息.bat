@echo off
echo ================================
echo     用户信息查询工具
echo ================================
echo.

if "%1"=="" (
    echo 使用方法: 查看用户信息.bat [用户名]
    echo.
    echo 示例:
    echo   查看用户信息.bat admin
    echo   查看用户信息.bat           ^(查看所有用户^)
    echo.
    pause
    exit /b
)

echo 正在查询用户信息...
echo.

if "%1"=="all" (
    sqlcmd -S .\SQLEXPRESS -E -d DeviceAccountManagerDb -Q "SELECT Id, Username, Email, Role, IsActive, LastLoginAt, PasswordChangedAt FROM Users ORDER BY Id"
) else (
    sqlcmd -S .\SQLEXPRESS -E -d DeviceAccountManagerDb -Q "SELECT Id, Username, Email, Role, IsActive, LastLoginAt, PasswordChangedAt FROM Users WHERE Username = '%1'"
)

echo.
echo 注意: 出于安全考虑，密码哈希不会显示
echo.
pause
