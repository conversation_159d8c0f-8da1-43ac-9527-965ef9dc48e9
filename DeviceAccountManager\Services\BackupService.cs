using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using Microsoft.EntityFrameworkCore;
using System.IO.Compression;
using System.Text.Json;

namespace DeviceAccountManager.Services
{
    public interface IBackupService
    {
        Task<bool> CreateBackupAsync(string backupName, string backupType = "Full", string description = "", int userId = 0);
        Task<bool> RestoreBackupAsync(int backupId);
        Task<bool> DeleteBackupAsync(int backupId);
        Task<List<SystemBackup>> GetBackupsAsync();
        Task<SystemBackup?> GetBackupAsync(int backupId);
        Task CleanupOldBackupsAsync();
        Task<bool> IsBackupInProgressAsync();
    }

    public class BackupService : IBackupService
    {
        private readonly ApplicationDbContext _context;
        private readonly ISystemConfigurationService _configService;
        private readonly ILogger<BackupService> _logger;
        private readonly IWebHostEnvironment _environment;
        private static readonly SemaphoreSlim _backupSemaphore = new(1, 1);

        public BackupService(
            ApplicationDbContext context,
            ISystemConfigurationService configService,
            ILogger<BackupService> logger,
            IWebHostEnvironment environment)
        {
            _context = context;
            _configService = configService;
            _logger = logger;
            _environment = environment;
        }

        public async Task<bool> CreateBackupAsync(string backupName, string backupType = "Full", string description = "", int userId = 0)
        {
            if (!await _backupSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                _logger.LogWarning("备份正在进行中，无法创建新备份");
                return false;
            }

            try
            {
                // 创建备份记录
                var backup = new SystemBackup
                {
                    BackupName = backupName,
                    BackupType = backupType,
                    Description = description,
                    Status = "InProgress",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = userId
                };

                _context.SystemBackups.Add(backup);
                await _context.SaveChangesAsync();

                // 执行备份
                var success = await PerformBackupAsync(backup);

                if (success)
                {
                    backup.Status = "Completed";
                    backup.CompletedAt = DateTime.Now;
                    _logger.LogInformation("备份完成: {BackupName}", backupName);
                }
                else
                {
                    backup.Status = "Failed";
                    backup.ErrorMessage = "备份过程中发生错误";
                    _logger.LogError("备份失败: {BackupName}", backupName);
                }

                await _context.SaveChangesAsync();
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建备份失败: {BackupName}", backupName);
                return false;
            }
            finally
            {
                _backupSemaphore.Release();
            }
        }

        public async Task<bool> RestoreBackupAsync(int backupId)
        {
            try
            {
                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null || backup.Status != "Completed")
                {
                    return false;
                }

                // 这里实现恢复逻辑
                // 实际生产环境中需要更复杂的恢复逻辑
                _logger.LogInformation("开始恢复备份: {BackupName}", backup.BackupName);
                
                // 模拟恢复过程
                await Task.Delay(1000);
                
                _logger.LogInformation("备份恢复完成: {BackupName}", backup.BackupName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复备份失败: {BackupId}", backupId);
                return false;
            }
        }

        public async Task<bool> DeleteBackupAsync(int backupId)
        {
            try
            {
                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null)
                {
                    return false;
                }

                // 删除备份文件
                if (!string.IsNullOrEmpty(backup.FilePath) && File.Exists(backup.FilePath))
                {
                    File.Delete(backup.FilePath);
                }

                // 删除数据库记录
                _context.SystemBackups.Remove(backup);
                await _context.SaveChangesAsync();

                _logger.LogInformation("删除备份: {BackupName}", backup.BackupName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除备份失败: {BackupId}", backupId);
                return false;
            }
        }

        public async Task<List<SystemBackup>> GetBackupsAsync()
        {
            return await _context.SystemBackups
                .Include(b => b.CreatedByUser)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<SystemBackup?> GetBackupAsync(int backupId)
        {
            return await _context.SystemBackups
                .Include(b => b.CreatedByUser)
                .FirstOrDefaultAsync(b => b.Id == backupId);
        }

        public async Task CleanupOldBackupsAsync()
        {
            try
            {
                var retentionDays = await _configService.GetConfigValueAsync("Backup.RetentionDays", 30);
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);

                var oldBackups = await _context.SystemBackups
                    .Where(b => b.CreatedAt < cutoffDate)
                    .ToListAsync();

                foreach (var backup in oldBackups)
                {
                    // 删除备份文件
                    if (!string.IsNullOrEmpty(backup.FilePath) && File.Exists(backup.FilePath))
                    {
                        File.Delete(backup.FilePath);
                    }

                    _context.SystemBackups.Remove(backup);
                }

                if (oldBackups.Any())
                {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("清理了 {Count} 个过期备份", oldBackups.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期备份失败");
            }
        }

        public async Task<bool> IsBackupInProgressAsync()
        {
            return await _context.SystemBackups
                .AnyAsync(b => b.Status == "InProgress" || b.Status == "Pending");
        }

        private async Task<bool> PerformBackupAsync(SystemBackup backup)
        {
            try
            {
                // 创建备份目录
                var backupDir = Path.Combine(_environment.ContentRootPath, "Backups");
                Directory.CreateDirectory(backupDir);

                var fileName = $"{backup.BackupName}_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                var filePath = Path.Combine(backupDir, fileName);

                // 创建备份文件
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                using (var archive = new ZipArchive(fileStream, ZipArchiveMode.Create))
                {
                    // 备份数据库数据
                    await BackupDatabaseDataAsync(archive);
                    
                    // 备份配置文件
                    await BackupConfigurationFilesAsync(archive);
                }

                // 更新备份记录
                var fileInfo = new FileInfo(filePath);
                backup.FilePath = filePath;
                backup.FileSize = fileInfo.Length;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行备份失败: {BackupName}", backup.BackupName);
                backup.ErrorMessage = ex.Message;
                return false;
            }
        }

        private async Task BackupDatabaseDataAsync(ZipArchive archive)
        {
            // 导出用户数据
            var users = await _context.Users.ToListAsync();
            await AddJsonToArchive(archive, "users.json", users);

            // 导出设备数据
            var devices = await _context.Devices.ToListAsync();
            await AddJsonToArchive(archive, "devices.json", devices);

            // 导出账户数据
            var accounts = await _context.Accounts.ToListAsync();
            await AddJsonToArchive(archive, "accounts.json", accounts);

            // 导出员工数据
            var employees = await _context.Employees.ToListAsync();
            await AddJsonToArchive(archive, "employees.json", employees);

            // 导出系统配置
            var configs = await _context.SystemConfigurations.ToListAsync();
            await AddJsonToArchive(archive, "system_configurations.json", configs);

            // 导出密码历史
            var passwordHistories = await _context.PasswordHistories.ToListAsync();
            await AddJsonToArchive(archive, "password_histories.json", passwordHistories);

            // 导出邮件记录
            var emailLogs = await _context.EmailLogs.ToListAsync();
            await AddJsonToArchive(archive, "email_logs.json", emailLogs);

            // 导出审计日志（最近30天）
            var recentAuditLogs = await _context.AuditLogs
                .Where(a => a.CreatedAt >= DateTime.Now.AddDays(-30))
                .ToListAsync();
            await AddJsonToArchive(archive, "audit_logs.json", recentAuditLogs);
        }

        private async Task AddJsonToArchive<T>(ZipArchive archive, string fileName, T data)
        {
            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            var entry = archive.CreateEntry(fileName);
            using var stream = entry.Open();
            using var writer = new StreamWriter(stream);
            await writer.WriteAsync(json);
        }

        private async Task BackupConfigurationFilesAsync(ZipArchive archive)
        {
            try
            {
                // 备份appsettings.json
                var appSettingsPath = Path.Combine(_environment.ContentRootPath, "appsettings.json");
                if (File.Exists(appSettingsPath))
                {
                    var entry = archive.CreateEntry("config/appsettings.json");
                    using var entryStream = entry.Open();
                    using var fileStream = new FileStream(appSettingsPath, FileMode.Open, FileAccess.Read);
                    await fileStream.CopyToAsync(entryStream);
                }

                // 备份appsettings.Development.json
                var devSettingsPath = Path.Combine(_environment.ContentRootPath, "appsettings.Development.json");
                if (File.Exists(devSettingsPath))
                {
                    var entry = archive.CreateEntry("config/appsettings.Development.json");
                    using var entryStream = entry.Open();
                    using var fileStream = new FileStream(devSettingsPath, FileMode.Open, FileAccess.Read);
                    await fileStream.CopyToAsync(entryStream);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "备份配置文件失败");
            }
        }
    }
}
