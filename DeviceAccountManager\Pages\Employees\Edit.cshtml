@page
@model DeviceAccountManager.Pages.Employees.EditModel
@{
    ViewData["Title"] = "编辑员工";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑员工</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a asp-page="./Details" asp-route-id="@Model.Employee?.Id" class="btn btn-sm btn-outline-info">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">员工信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="Employee.Id" />
                    <input type="hidden" asp-for="Employee.CreatedAt" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Name" class="form-label">员工姓名 *</label>
                                <input asp-for="Employee.Name" class="form-control" />
                                <span asp-validation-for="Employee.Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.EmployeeCode" class="form-label">员工工号 *</label>
                                <input asp-for="Employee.EmployeeCode" class="form-control" />
                                <span asp-validation-for="Employee.EmployeeCode" class="text-danger"></span>
                                <div class="form-text">员工的唯一标识码</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Department" class="form-label">所属部门</label>
                                <input asp-for="Employee.Department" class="form-control" list="departmentList" />
                                <datalist id="departmentList">
                                    @foreach (var dept in Model.ExistingDepartments)
                                    {
                                        <option value="@dept"></option>
                                    }
                                </datalist>
                                <span asp-validation-for="Employee.Department" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Position" class="form-label">职位</label>
                                <input asp-for="Employee.Position" class="form-control" list="positionList" />
                                <datalist id="positionList">
                                    @foreach (var pos in Model.ExistingPositions)
                                    {
                                        <option value="@pos"></option>
                                    }
                                </datalist>
                                <span asp-validation-for="Employee.Position" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Email" class="form-label">邮箱地址</label>
                                <input asp-for="Employee.Email" type="email" class="form-control" />
                                <span asp-validation-for="Employee.Email" class="text-danger"></span>
                                <div class="form-text">用于接收系统通知邮件</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Phone" class="form-label">联系电话</label>
                                <input asp-for="Employee.Phone" class="form-control" />
                                <span asp-validation-for="Employee.Phone" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.HireDate" class="form-label">入职日期</label>
                                <input asp-for="Employee.HireDate" type="date" class="form-control" />
                                <span asp-validation-for="Employee.HireDate" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">员工状态</label>
                                <div class="form-check">
                                    <input asp-for="Employee.IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="Employee.IsActive" class="form-check-label">
                                        员工在职
                                    </label>
                                </div>
                                <div class="form-text">取消勾选表示员工已离职</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Employee.Notes" class="form-label">备注信息</label>
                        <textarea asp-for="Employee.Notes" class="form-control" rows="3" placeholder="员工的其他信息和备注..."></textarea>
                        <span asp-validation-for="Employee.Notes" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存更改
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">员工信息</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 创建信息</h6>
                    <p><strong>创建时间：</strong>@Model.Employee?.CreatedAt.ToString("yyyy-MM-dd HH:mm")</p>
                    @if (Model.Employee?.UpdatedAt.HasValue == true)
                    {
                        <p><strong>更新时间：</strong>@Model.Employee.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")</p>
                    }
                </div>

                @if (Model.RelatedAccountsCount > 0)
                {
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-person-badge"></i> 关联账户</h6>
                        <p class="mb-0">此员工关联了 <strong>@Model.RelatedAccountsCount</strong> 个账户</p>
                    </div>
                }

                <div class="alert alert-success">
                    <h6><i class="bi bi-lightbulb"></i> 编辑提示</h6>
                    <ul class="mb-0">
                        <li>员工工号必须唯一</li>
                        <li>邮箱地址用于接收系统通知</li>
                        <li>部门和职位支持自动补全</li>
                        <li>离职员工请取消"在职"状态</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
