@page
@model DeviceAccountManager.Pages.EmailManagement.DetailsModel
@{
    ViewData["Title"] = "邮件详情";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">邮件详情</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回邮件列表
            </a>
            @if (Model.EmailLog.Status == "Failed" || Model.EmailLog.Status == "Pending")
            {
                <button type="button" class="btn btn-sm btn-primary" onclick="resendEmail(@Model.EmailLog.Id)">
                    <i class="bi bi-arrow-clockwise"></i> 重新发送
                </button>
            }
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- 邮件内容 -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-envelope"></i> 邮件内容
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>主题：</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.EmailLog.Subject
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>收件人：</strong>
                    </div>
                    <div class="col-sm-9">
                        <strong>@Model.EmailLog.ToName</strong>
                        <br>
                        <small class="text-muted">@Model.EmailLog.ToEmail</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>邮件类型：</strong>
                    </div>
                    <div class="col-sm-9">
                        @switch (Model.EmailLog.EmailType)
                        {
                            case "PasswordChanged":
                                <span class="badge bg-info">密码更改通知</span>
                                break;
                            case "PasswordExpiring":
                                <span class="badge bg-warning">密码到期提醒</span>
                                break;
                            case "AccountCreated":
                                <span class="badge bg-success">账户创建通知</span>
                                break;
                            case "AccountDisabled":
                                <span class="badge bg-danger">账户禁用通知</span>
                                break;
                            default:
                                <span class="badge bg-secondary">@Model.EmailLog.EmailType</span>
                                break;
                        }
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-12">
                        <strong>邮件正文：</strong>
                        <div class="mt-2 p-3 border rounded bg-light">
                            <pre style="white-space: pre-wrap; font-family: inherit;">@Model.EmailLog.Content</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- 邮件状态信息 -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-info-circle"></i> 状态信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-5">
                        <strong>状态：</strong>
                    </div>
                    <div class="col-sm-7">
                        @switch (Model.EmailLog.Status)
                        {
                            case "Pending":
                                <span class="badge bg-warning">
                                    <i class="bi bi-clock"></i> 待发送
                                </span>
                                break;
                            case "Sent":
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle"></i> 已发送
                                </span>
                                break;
                            case "Failed":
                                <span class="badge bg-danger">
                                    <i class="bi bi-x-circle"></i> 发送失败
                                </span>
                                break;
                            default:
                                <span class="badge bg-secondary">@Model.EmailLog.Status</span>
                                break;
                        }
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-5">
                        <strong>创建时间：</strong>
                    </div>
                    <div class="col-sm-7">
                        @Model.EmailLog.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                    </div>
                </div>

                @if (Model.EmailLog.SentAt.HasValue)
                {
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <strong>发送时间：</strong>
                        </div>
                        <div class="col-sm-7">
                            @Model.EmailLog.SentAt.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.EmailLog.ErrorMessage))
                {
                    <div class="row mb-2">
                        <div class="col-12">
                            <strong>错误信息：</strong>
                            <div class="mt-1 p-2 border rounded bg-danger text-white">
                                <small>@Model.EmailLog.ErrorMessage</small>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- 关联账户信息 -->
        @if (Model.EmailLog.Account != null)
        {
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-person-badge"></i> 关联账户
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <strong>账户名：</strong>
                        </div>
                        <div class="col-sm-7">
                            <a asp-page="/Accounts/Details" asp-route-id="@Model.EmailLog.Account.Id" class="text-decoration-none">
                                @Model.EmailLog.Account.Username
                            </a>
                        </div>
                    </div>

                    @if (Model.EmailLog.Account.Device != null)
                    {
                        <div class="row mb-2">
                            <div class="col-sm-5">
                                <strong>设备：</strong>
                            </div>
                            <div class="col-sm-7">
                                <a asp-page="/Devices/Details" asp-route-id="@Model.EmailLog.Account.Device.Id" class="text-decoration-none">
                                    @Model.EmailLog.Account.Device.DeviceCode
                                </a>
                            </div>
                        </div>
                    }

                    @if (Model.EmailLog.Account.Employee != null)
                    {
                        <div class="row mb-2">
                            <div class="col-sm-5">
                                <strong>员工：</strong>
                            </div>
                            <div class="col-sm-7">
                                <a asp-page="/Employees/Details" asp-route-id="@Model.EmailLog.Account.Employee.Id" class="text-decoration-none">
                                    @Model.EmailLog.Account.Employee.Name
                                </a>
                            </div>
                        </div>
                    }

                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <strong>权限级别：</strong>
                        </div>
                        <div class="col-sm-7">
                            @switch (Model.EmailLog.Account.PermissionLevel)
                            {
                                case "SuperAdmin":
                                    <span class="badge bg-danger">超级管理员</span>
                                    break;
                                case "Technician":
                                    <span class="badge bg-warning">工艺员</span>
                                    break;
                                case "Maintainer":
                                    <span class="badge bg-info">维护者</span>
                                    break;
                                case "Operator":
                                    <span class="badge bg-secondary">操作者</span>
                                    break;
                                default:
                                    <span class="badge bg-light text-dark">@Model.EmailLog.Account.PermissionLevel</span>
                                    break;
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function resendEmail(emailId) {
            if (confirm('确定要重新发送这封邮件吗？')) {
                fetch(`/api/email/resend/${emailId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('邮件重新发送成功！');
                        location.reload();
                    } else {
                        alert('邮件重新发送失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('重新发送邮件时发生错误');
                });
            }
        }
    </script>
}
