@page
@model DeviceAccountManager.Pages.PasswordManagement.BatchChangeModel
@{
    ViewData["Title"] = "批量更改密码";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">批量更改密码</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回密码管理
            </a>
        </div>
    </div>
</div>

<form method="post">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">选择要更改密码的账户</h6>
                </div>
                <div class="card-body">
                    <!-- 筛选选项 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">权限级别筛选</label>
                            <select id="permissionFilter" class="form-select" onchange="filterAccounts()">
                                <option value="">全部权限级别</option>
                                <option value="SuperAdmin">超级管理员</option>
                                <option value="Technician">工艺员</option>
                                <option value="Maintainer">维护者</option>
                                <option value="Operator">操作者</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">密码状态筛选</label>
                            <select id="statusFilter" class="form-select" onchange="filterAccounts()">
                                <option value="">全部状态</option>
                                <option value="expired">已过期</option>
                                <option value="expiring">即将过期</option>
                                <option value="normal">正常</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">快速选择</label>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="selectAll()">全选</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="selectNone()">全不选</button>
                                <button type="button" class="btn btn-outline-warning" onclick="selectExpired()">选择过期</button>
                            </div>
                        </div>
                    </div>

                    <!-- 账户列表 -->
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th>账户名</th>
                                    <th>设备</th>
                                    <th>员工</th>
                                    <th>权限级别</th>
                                    <th>密码状态</th>
                                    <th>到期日期</th>
                                </tr>
                            </thead>
                            <tbody id="accountsTableBody">
                                @for (int i = 0; i < Model.Accounts.Count; i++)
                                {
                                    var account = Model.Accounts[i];
                                    var isExpired = account.NextPasswordChangeAt.HasValue && account.NextPasswordChangeAt.Value.Date <= DateTime.Now.Date;
                                    var isExpiring = account.NextPasswordChangeAt.HasValue && 
                                                   account.NextPasswordChangeAt.Value.Date > DateTime.Now.Date && 
                                                   account.NextPasswordChangeAt.Value.Date <= DateTime.Now.Date.AddDays(7);
                                    var rowClass = isExpired ? "table-danger" : (isExpiring ? "table-warning" : "");
                                    
                                    <tr class="@rowClass account-row" 
                                        data-permission="@account.PermissionLevel" 
                                        data-status="@(isExpired ? "expired" : (isExpiring ? "expiring" : "normal"))">
                                        <td>
                                            <input type="checkbox" name="SelectedAccountIds" value="@account.Id" class="account-checkbox">
                                        </td>
                                        <td><strong>@account.Username</strong></td>
                                        <td>
                                            @if (account.Device != null)
                                            {
                                                <span>@account.Device.DeviceCode</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">未关联</span>
                                            }
                                        </td>
                                        <td>
                                            @if (account.Employee != null)
                                            {
                                                <span>@account.Employee.Name</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">未关联</span>
                                            }
                                        </td>
                                        <td>
                                            @switch (account.PermissionLevel)
                                            {
                                                case "SuperAdmin":
                                                    <span class="badge bg-danger">超级管理员</span>
                                                    break;
                                                case "Technician":
                                                    <span class="badge bg-warning">工艺员</span>
                                                    break;
                                                case "Maintainer":
                                                    <span class="badge bg-info">维护者</span>
                                                    break;
                                                case "Operator":
                                                    <span class="badge bg-secondary">操作者</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-light text-dark">@account.PermissionLevel</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (isExpired)
                                            {
                                                <span class="badge bg-danger">已过期</span>
                                            }
                                            else if (isExpiring)
                                            {
                                                <span class="badge bg-warning">即将过期</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">正常</span>
                                            }
                                        </td>
                                        <td>
                                            @if (account.NextPasswordChangeAt.HasValue)
                                            {
                                                @account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")
                                            }
                                            else
                                            {
                                                <span class="text-muted">未设置</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            已选择 <span id="selectedCount">0</span> 个账户，共 @Model.Accounts.Count 个账户
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">密码设置</h6>
                </div>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <label asp-for="PasswordGenerationMode" class="form-label">密码生成方式</label>
                        <select asp-for="PasswordGenerationMode" class="form-select" onchange="togglePasswordMode()">
                            <option value="Auto">自动生成（推荐）</option>
                            <option value="Manual">手动设置</option>
                        </select>
                    </div>

                    <div id="manualPasswordSection" style="display: none;">
                        <div class="form-group mb-3">
                            <label asp-for="ManualPassword" class="form-label">统一密码</label>
                            <div class="input-group">
                                <input asp-for="ManualPassword" type="password" class="form-control" placeholder="输入统一密码">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('ManualPassword')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="ManualPassword" class="text-danger"></span>
                            <div class="form-text">所有选中的账户将使用此密码</div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ChangeReason" class="form-label">更改原因 *</label>
                        <select asp-for="ChangeReason" class="form-select" required>
                            <option value="">请选择更改原因</option>
                            <option value="Scheduled quarterly change">季度定期更改</option>
                            <option value="Security policy compliance">安全策略合规</option>
                            <option value="Password expired">密码过期</option>
                            <option value="Security incident">安全事件</option>
                            <option value="Administrative requirement">管理要求</option>
                            <option value="System maintenance">系统维护</option>
                        </select>
                        <span asp-validation-for="ChangeReason" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Notes" class="form-label">备注说明</label>
                        <textarea asp-for="Notes" class="form-control" rows="3" placeholder="请输入更改说明..."></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="SendEmailNotification" class="form-check-input" type="checkbox" checked>
                        <label asp-for="SendEmailNotification" class="form-check-label">
                            发送邮件通知
                        </label>
                        <div class="form-text">向关联员工发送密码更改通知邮件</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" onclick="return confirmBatchChange()">
                            <i class="bi bi-key"></i> 批量更改密码
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">操作说明</h6>
                </div>
                <div class="card-body">
                    <ol class="mb-0">
                        <li>选择需要更改密码的账户</li>
                        <li>选择密码生成方式</li>
                        <li>填写更改原因和备注</li>
                        <li>确认执行批量更改</li>
                    </ol>
                    <hr>
                    <div class="alert alert-warning">
                        <small>
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>注意：</strong>批量更改密码后，新密码将通过邮件发送给相关员工。请确保员工邮箱地址正确。
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function filterAccounts() {
            const permissionFilter = document.getElementById('permissionFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const rows = document.querySelectorAll('.account-row');
            
            rows.forEach(row => {
                const permission = row.getAttribute('data-permission');
                const status = row.getAttribute('data-status');
                
                const permissionMatch = !permissionFilter || permission === permissionFilter;
                const statusMatch = !statusFilter || status === statusFilter;
                
                if (permissionMatch && statusMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                    row.querySelector('.account-checkbox').checked = false;
                }
            });
            
            updateSelectedCount();
        }

        function selectAll() {
            const visibleCheckboxes = document.querySelectorAll('.account-row:not([style*="display: none"]) .account-checkbox');
            visibleCheckboxes.forEach(cb => cb.checked = true);
            updateSelectedCount();
        }

        function selectNone() {
            const checkboxes = document.querySelectorAll('.account-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            updateSelectedCount();
        }

        function selectExpired() {
            selectNone();
            const expiredRows = document.querySelectorAll('.account-row[data-status="expired"]:not([style*="display: none"])');
            expiredRows.forEach(row => {
                row.querySelector('.account-checkbox').checked = true;
            });
            updateSelectedCount();
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const visibleCheckboxes = document.querySelectorAll('.account-row:not([style*="display: none"]) .account-checkbox');
            
            visibleCheckboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.account-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selectedCount;
            
            // 更新全选复选框状态
            const visibleCheckboxes = document.querySelectorAll('.account-row:not([style*="display: none"]) .account-checkbox');
            const checkedVisibleCount = document.querySelectorAll('.account-row:not([style*="display: none"]) .account-checkbox:checked').length;
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            if (checkedVisibleCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedVisibleCount === visibleCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }

        function togglePasswordMode() {
            const mode = document.getElementById('PasswordGenerationMode').value;
            const manualSection = document.getElementById('manualPasswordSection');
            
            if (mode === 'Manual') {
                manualSection.style.display = 'block';
            } else {
                manualSection.style.display = 'none';
            }
        }

        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'bi bi-eye';
            }
        }

        function confirmBatchChange() {
            const selectedCount = document.querySelectorAll('.account-checkbox:checked').length;
            
            if (selectedCount === 0) {
                alert('请至少选择一个账户');
                return false;
            }
            
            return confirm(`确定要为 ${selectedCount} 个账户批量更改密码吗？此操作不可撤销！`);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有账户复选框添加事件监听
            document.querySelectorAll('.account-checkbox').forEach(cb => {
                cb.addEventListener('change', updateSelectedCount);
            });
            
            updateSelectedCount();
        });
    </script>
}
