@page
@model DeviceAccountManager.Pages.PasswordManagement.IndexModel
@{
    ViewData["Title"] = "密码管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">密码管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            @if (Model.IsTechnician)
            {
                <a asp-page="./BatchChange" class="btn btn-sm btn-primary">
                    <i class="bi bi-arrow-repeat"></i> 批量更改密码
                </a>
                <a asp-page="./Export" class="btn btn-sm btn-success">
                    <i class="bi bi-download"></i> 导出密码
                </a>
            }
            @if (Model.IsSuperAdmin)
            {
                <a asp-page="./Policy" class="btn btn-sm btn-warning">
                    <i class="bi bi-gear"></i> 密码策略
                </a>
            }
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            需要立即更改
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ExpiredPasswordsCount</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle-fill fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            7天内到期
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ExpiringPasswordsCount</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock-fill fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总账户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalAccountsCount</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people-fill fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            密码健康度
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.PasswordHealthPercentage%</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shield-check-fill fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 需要立即更改密码的账户 -->
@if (Model.ExpiredAccounts.Any())
{
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="bi bi-exclamation-triangle-fill"></i> 需要立即更改密码的账户
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>账户名</th>
                            <th>设备</th>
                            <th>员工</th>
                            <th>权限级别</th>
                            <th>应更改日期</th>
                            <th>逾期天数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var account in Model.ExpiredAccounts)
                        {
                            <tr class="table-danger">
                                <td><strong>@account.Username</strong></td>
                                <td>
                                    @if (account.Device != null)
                                    {
                                        <span>@account.Device.DeviceCode</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未关联</span>
                                    }
                                </td>
                                <td>
                                    @if (account.Employee != null)
                                    {
                                        <span>@account.Employee.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未关联</span>
                                    }
                                </td>
                                <td>
                                    @switch (account.PermissionLevel)
                                    {
                                        case "SuperAdmin":
                                            <span class="badge bg-danger">超级管理员</span>
                                            break;
                                        case "Technician":
                                            <span class="badge bg-warning">工艺员</span>
                                            break;
                                        case "Maintainer":
                                            <span class="badge bg-info">维护者</span>
                                            break;
                                        case "Operator":
                                            <span class="badge bg-secondary">操作者</span>
                                            break;
                                        default:
                                            <span class="badge bg-light text-dark">@account.PermissionLevel</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @if (account.NextPasswordChangeAt.HasValue)
                                    {
                                        @account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (account.NextPasswordChangeAt.HasValue)
                                    {
                                        var overdueDays = (DateTime.Now.Date - account.NextPasswordChangeAt.Value.Date).Days;
                                        <span class="badge bg-danger">@overdueDays 天</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (Model.IsMaintainer)
                                    {
                                        <a asp-page="/Accounts/ChangePassword" asp-route-id="@account.Id" class="btn btn-sm btn-danger">
                                            <i class="bi bi-key"></i> 更改密码
                                        </a>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<!-- 即将到期的账户 -->
@if (Model.ExpiringAccounts.Any())
{
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="bi bi-clock-fill"></i> 7天内密码到期的账户
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>账户名</th>
                            <th>设备</th>
                            <th>员工</th>
                            <th>权限级别</th>
                            <th>到期日期</th>
                            <th>剩余天数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var account in Model.ExpiringAccounts)
                        {
                            <tr class="table-warning">
                                <td><strong>@account.Username</strong></td>
                                <td>
                                    @if (account.Device != null)
                                    {
                                        <span>@account.Device.DeviceCode</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未关联</span>
                                    }
                                </td>
                                <td>
                                    @if (account.Employee != null)
                                    {
                                        <span>@account.Employee.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未关联</span>
                                    }
                                </td>
                                <td>
                                    @switch (account.PermissionLevel)
                                    {
                                        case "SuperAdmin":
                                            <span class="badge bg-danger">超级管理员</span>
                                            break;
                                        case "Technician":
                                            <span class="badge bg-warning">工艺员</span>
                                            break;
                                        case "Maintainer":
                                            <span class="badge bg-info">维护者</span>
                                            break;
                                        case "Operator":
                                            <span class="badge bg-secondary">操作者</span>
                                            break;
                                        default:
                                            <span class="badge bg-light text-dark">@account.PermissionLevel</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @if (account.NextPasswordChangeAt.HasValue)
                                    {
                                        @account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>
                                    @if (account.NextPasswordChangeAt.HasValue)
                                    {
                                        var remainingDays = (account.NextPasswordChangeAt.Value.Date - DateTime.Now.Date).Days;
                                        <span class="badge bg-warning">@remainingDays 天</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (Model.IsMaintainer)
                                    {
                                        <a asp-page="/Accounts/ChangePassword" asp-route-id="@account.Id" class="btn btn-sm btn-warning">
                                            <i class="bi bi-key"></i> 更改密码
                                        </a>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<!-- 快速操作 -->
@if (Model.IsTechnician)
{
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-lightning-fill"></i> 快速操作
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="d-grid">
                        <a asp-page="./BatchChange" class="btn btn-primary">
                            <i class="bi bi-arrow-repeat"></i> 批量更改密码
                        </a>
                    </div>
                    <small class="text-muted">为多个账户同时更改密码</small>
                </div>
                <div class="col-md-4">
                    <div class="d-grid">
                        <a asp-page="./Export" class="btn btn-success">
                            <i class="bi bi-download"></i> 导出密码清单
                        </a>
                    </div>
                    <small class="text-muted">导出账户密码信息到Excel</small>
                </div>
                <div class="col-md-4">
                    <div class="d-grid">
                        <button type="button" class="btn btn-info" onclick="schedulePasswordChanges()">
                            <i class="bi bi-calendar-check"></i> 计划密码更改
                        </button>
                    </div>
                    <small class="text-muted">为所有账户设置密码更改计划</small>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        function schedulePasswordChanges() {
            if (confirm('确定要为所有账户重新设置密码更改计划吗？')) {
                fetch('/api/password/schedule', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('密码更改计划已设置成功！');
                        location.reload();
                    } else {
                        alert('设置失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请稍后重试');
                });
            }
        }
    </script>
}
