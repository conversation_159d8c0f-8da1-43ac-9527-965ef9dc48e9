using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 操作日志表
    /// </summary>
    public class OperationLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Operation { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string TargetType { get; set; } = string.Empty;

        [StringLength(50)]
        public string TargetId { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        public string IpAddress { get; set; } = string.Empty;

        [StringLength(500)]
        public string UserAgent { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 外键
        [Required]
        public int UserId { get; set; }

        // 导航属性
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
