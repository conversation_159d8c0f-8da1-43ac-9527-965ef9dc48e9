@page
@model DeviceAccountManager.Pages.Employees.CreateModel
@{
    ViewData["Title"] = "添加员工";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">添加员工</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">员工信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.EmployeeCode" class="form-label">员工工号 *</label>
                                <input asp-for="Employee.EmployeeCode" class="form-control" placeholder="例如：EMP001" />
                                <span asp-validation-for="Employee.EmployeeCode" class="text-danger"></span>
                                <div class="form-text">员工的唯一标识工号</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Name" class="form-label">员工姓名 *</label>
                                <input asp-for="Employee.Name" class="form-control" placeholder="例如：张三" />
                                <span asp-validation-for="Employee.Name" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Department" class="form-label">部门</label>
                                <input asp-for="Employee.Department" class="form-control" placeholder="例如：生产部" list="departmentList" />
                                <datalist id="departmentList">
                                    @foreach (var dept in Model.ExistingDepartments)
                                    {
                                        <option value="@dept"></option>
                                    }
                                </datalist>
                                <span asp-validation-for="Employee.Department" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Position" class="form-label">职位</label>
                                <input asp-for="Employee.Position" class="form-control" placeholder="例如：操作员" />
                                <span asp-validation-for="Employee.Position" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Email" class="form-label">邮箱地址</label>
                                <input asp-for="Employee.Email" type="email" class="form-control" placeholder="例如：<EMAIL>" />
                                <span asp-validation-for="Employee.Email" class="text-danger"></span>
                                <div class="form-text">用于接收密码更改通知</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.Phone" class="form-label">联系电话</label>
                                <input asp-for="Employee.Phone" class="form-control" placeholder="例如：13800138000" />
                                <span asp-validation-for="Employee.Phone" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Employee.HireDate" class="form-label">入职日期</label>
                                <input asp-for="Employee.HireDate" type="date" class="form-control" />
                                <span asp-validation-for="Employee.HireDate" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">员工状态</label>
                                <div class="form-check">
                                    <input asp-for="Employee.IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="Employee.IsActive" class="form-check-label">
                                        在职状态
                                    </label>
                                </div>
                                <div class="form-text">取消勾选表示员工已离职</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Employee.Notes" class="form-label">备注信息</label>
                        <textarea asp-for="Employee.Notes" class="form-control" rows="3" placeholder="员工的其他信息和备注..."></textarea>
                        <span asp-validation-for="Employee.Notes" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存员工
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">填写说明</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 工号规范</h6>
                    <ul class="mb-0">
                        <li>工号必须唯一</li>
                        <li>建议使用统一格式</li>
                        <li>例如：EMP001, EMP002</li>
                        <li>避免使用特殊字符</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="bi bi-envelope"></i> 邮箱重要性</h6>
                    <ul class="mb-0">
                        <li>用于密码更改通知</li>
                        <li>系统重要消息推送</li>
                        <li>建议使用公司邮箱</li>
                        <li>确保邮箱地址有效</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6><i class="bi bi-person-check"></i> 后续操作</h6>
                    <ul class="mb-0">
                        <li>创建员工后可绑定账户</li>
                        <li>可批量导入员工信息</li>
                        <li>支持Excel文件导入</li>
                        <li>可随时修改员工信息</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
