using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly ISystemConfigurationService _configService;
        private readonly IAuditLogService _auditService;

        public IndexModel(ApplicationDbContext context, ISystemConfigurationService configService, IAuditLogService auditService)
        {
            _context = context;
            _configService = configService;
            _auditService = auditService;
        }

        public string SystemName { get; set; } = string.Empty;
        public string SystemVersion { get; set; } = string.Empty;
        public string SystemUptime { get; set; } = string.Empty;
        public bool MaintenanceMode { get; set; }

        public int TotalDevices { get; set; }
        public int TotalAccounts { get; set; }
        public int TotalEmployees { get; set; }
        public int ActiveUsers { get; set; }

        public bool EmailServiceStatus { get; set; }
        public bool BackupServiceStatus { get; set; }

        public List<AuditLog> RecentActivities { get; set; } = new();

        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            await LoadSystemInfoAsync();
            await LoadStatisticsAsync();
            await LoadRecentActivitiesAsync();
            CheckServiceStatus();

            return Page();
        }

        private async Task LoadSystemInfoAsync()
        {
            SystemName = await _configService.GetConfigValueAsync("System.Name", "设备账户权限管理系统");
            SystemVersion = await _configService.GetConfigValueAsync("System.Version", "1.0.0");
            MaintenanceMode = await _configService.GetConfigValueAsync("System.MaintenanceMode", false);

            // 计算系统运行时间（简化版本）
            var startTime = DateTime.Now.AddHours(-24); // 假设系统运行了24小时
            var uptime = DateTime.Now - startTime;
            SystemUptime = $"{uptime.Days}天 {uptime.Hours}小时 {uptime.Minutes}分钟";
        }

        private async Task LoadStatisticsAsync()
        {
            TotalDevices = await _context.Devices.CountAsync();
            TotalAccounts = await _context.Accounts.CountAsync();
            TotalEmployees = await _context.Employees.CountAsync();
            
            // 活跃用户（最近7天有登录记录的用户）
            var sevenDaysAgo = DateTime.Now.AddDays(-7);
            ActiveUsers = await _context.AuditLogs
                .Where(a => a.Action == "Login" && a.CreatedAt >= sevenDaysAgo)
                .Select(a => a.UserId)
                .Distinct()
                .CountAsync();
        }

        private async Task LoadRecentActivitiesAsync()
        {
            RecentActivities = await _auditService.GetAuditLogsAsync(1, 10);
        }

        private void CheckServiceStatus()
        {
            // 检查邮件服务状态
            try
            {
                EmailServiceStatus = true; // 简化版本，实际应该检查SMTP连接
            }
            catch
            {
                EmailServiceStatus = false;
            }

            // 检查备份服务状态
            try
            {
                BackupServiceStatus = true; // 简化版本，实际应该检查备份配置
            }
            catch
            {
                BackupServiceStatus = false;
            }
        }
    }
}
