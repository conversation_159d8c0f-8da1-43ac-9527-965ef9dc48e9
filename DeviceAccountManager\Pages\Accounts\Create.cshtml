@page
@model DeviceAccountManager.Pages.Accounts.CreateModel
@{
    ViewData["Title"] = "添加账户";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">添加账户</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">账户信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.Username" class="form-label">账户名 *</label>
                                <input asp-for="Account.Username" class="form-control" placeholder="例如：device001_admin" />
                                <span asp-validation-for="Account.Username" class="text-danger"></span>
                                <div class="form-text">账户的登录用户名，建议使用设备代码+角色格式</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.Password" class="form-label">初始密码 *</label>
                                <div class="input-group">
                                    <input asp-for="Account.Password" type="password" class="form-control" placeholder="输入初始密码" />
                                    <button type="button" class="btn btn-outline-secondary" onclick="generatePassword()">
                                        <i class="bi bi-arrow-clockwise"></i> 生成
                                    </button>
                                </div>
                                <span asp-validation-for="Account.Password" class="text-danger"></span>
                                <div class="form-text">密码长度至少8位，包含大小写字母、数字和特殊字符</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.DeviceId" class="form-label">关联设备</label>
                                <select asp-for="Account.DeviceId" class="form-select" asp-items="Model.DeviceSelectList">
                                    <option value="">请选择设备</option>
                                </select>
                                <span asp-validation-for="Account.DeviceId" class="text-danger"></span>
                                <div class="form-text">选择此账户关联的设备</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.EmployeeId" class="form-label">绑定员工</label>
                                <select asp-for="Account.EmployeeId" class="form-select" asp-items="Model.EmployeeSelectList">
                                    <option value="">请选择员工</option>
                                </select>
                                <span asp-validation-for="Account.EmployeeId" class="text-danger"></span>
                                <div class="form-text">选择此账户绑定的员工</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.PermissionLevel" class="form-label">权限级别 *</label>
                                <select asp-for="Account.PermissionLevel" class="form-select">
                                    <option value="">请选择权限级别</option>
                                    <option value="Admin">管理员</option>
                                    <option value="Operator">操作员</option>
                                    <option value="Viewer">查看者</option>
                                </select>
                                <span asp-validation-for="Account.PermissionLevel" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">账户选项</label>
                                <div class="form-check">
                                    <input asp-for="Account.IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="Account.IsActive" class="form-check-label">
                                        启用账户
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input asp-for="Account.IsExemptFromPasswordChange" class="form-check-input" type="checkbox" />
                                    <label asp-for="Account.IsExemptFromPasswordChange" class="form-check-label">
                                        免于定期密码更改
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Account.Description" class="form-label">账户描述</label>
                        <textarea asp-for="Account.Description" class="form-control" rows="3" placeholder="账户的用途和说明..."></textarea>
                        <span asp-validation-for="Account.Description" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 创建账户
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">创建说明</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 账户命名规范</h6>
                    <ul class="mb-0">
                        <li>建议格式：设备代码_角色</li>
                        <li>例如：DEV001_admin</li>
                        <li>避免使用特殊字符</li>
                        <li>长度建议6-20个字符</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="bi bi-shield-check"></i> 密码安全</h6>
                    <ul class="mb-0">
                        <li>最少8个字符</li>
                        <li>包含大小写字母</li>
                        <li>包含数字和特殊字符</li>
                        <li>避免使用常见密码</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6><i class="bi bi-gear"></i> 权限说明</h6>
                    <ul class="mb-0">
                        <li><strong>管理员</strong>：完全控制权限</li>
                        <li><strong>操作员</strong>：操作和查看权限</li>
                        <li><strong>查看者</strong>：仅查看权限</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function generatePassword() {
            const length = 12;
            const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@@#$%^&*";
            let password = "";
            
            // 确保包含各种字符类型
            password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)]; // 大写字母
            password += "abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random() * 26)]; // 小写字母
            password += "0123456789"[Math.floor(Math.random() * 10)]; // 数字
            password += "!@@#$%^&*"[Math.floor(Math.random() * 9)]; // 特殊字符
            
            // 填充剩余长度
            for (let i = password.length; i < length; i++) {
                password += charset[Math.floor(Math.random() * charset.length)];
            }
            
            // 打乱字符顺序
            password = password.split('').sort(() => 0.5 - Math.random()).join('');
            
            document.querySelector('input[name="Account.Password"]').value = password;
        }
    </script>
}
