@page
@model DeviceAccountManager.Pages.Employees.DeleteModel
@{
    ViewData["Title"] = "删除员工";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">删除员工</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a asp-page="./Details" asp-route-id="@Model.Employee.Id" class="btn btn-sm btn-outline-info">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow border-danger">
            <div class="card-header py-3 bg-danger text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-exclamation-triangle"></i> 确认删除员工
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle-fill"></i> 警告</h5>
                    <p>您即将删除以下员工，此操作<strong>不可撤销</strong>！</p>
                    @if (Model.RelatedAccountsCount > 0)
                    {
                        <p><strong>注意：</strong>此员工关联了 @Model.RelatedAccountsCount 个账户，删除员工后这些账户将失去员工绑定。</p>
                    }
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">员工姓名：</dt>
                            <dd class="col-sm-8">
                                <strong>@Model.Employee.Name</strong>
                                @if (Model.Employee.IsActive)
                                {
                                    <span class="badge bg-success ms-2">在职</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger ms-2">离职</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-4">员工工号：</dt>
                            <dd class="col-sm-8"><strong>@Model.Employee.EmployeeCode</strong></dd>

                            <dt class="col-sm-4">所属部门：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Department))
                                {
                                    <span>@Model.Employee.Department</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">职位：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Position))
                                {
                                    <span>@Model.Employee.Position</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">邮箱地址：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Email))
                                {
                                    <span>@Model.Employee.Email</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">联系电话：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Phone))
                                {
                                    <span>@Model.Employee.Phone</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">入职日期：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Employee.HireDate.HasValue)
                                {
                                    @Model.Employee.HireDate.Value.ToString("yyyy-MM-dd")
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">创建时间：</dt>
                            <dd class="col-sm-8">@Model.Employee.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Employee.Notes))
                {
                    <div class="mt-3">
                        <h6>备注信息：</h6>
                        <p class="text-muted">@Model.Employee.Notes</p>
                    </div>
                }

                <form method="post" class="mt-4">
                    <input type="hidden" asp-for="Employee.Id" />
                    
                    <div class="form-group mb-3">
                        <label asp-for="DeleteReason" class="form-label">删除原因 *</label>
                        <select asp-for="DeleteReason" class="form-select" required>
                            <option value="">请选择删除原因</option>
                            <option value="Employee resigned">员工离职</option>
                            <option value="Employee terminated">员工被解雇</option>
                            <option value="Data cleanup">数据清理</option>
                            <option value="Duplicate record">重复记录</option>
                            <option value="Data migration">数据迁移</option>
                            <option value="Other">其他原因</option>
                        </select>
                        <span asp-validation-for="DeleteReason" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="DeleteNotes" class="form-label">删除备注</label>
                        <textarea asp-for="DeleteNotes" class="form-control" rows="3" placeholder="请输入删除的详细说明..."></textarea>
                        <span asp-validation-for="DeleteNotes" class="text-danger"></span>
                    </div>

                    @if (Model.RelatedAccountsCount > 0)
                    {
                        <div class="form-group mb-3">
                            <label asp-for="HandleRelatedAccounts" class="form-label">关联账户处理方式 *</label>
                            <select asp-for="HandleRelatedAccounts" class="form-select" required>
                                <option value="">请选择处理方式</option>
                                <option value="Unbind">解除绑定（保留账户）</option>
                                <option value="Disable">禁用账户</option>
                                <option value="Delete">删除账户</option>
                            </select>
                            <span asp-validation-for="HandleRelatedAccounts" class="text-danger"></span>
                            <div class="form-text">
                                <strong>解除绑定：</strong>账户继续存在但不再绑定此员工<br>
                                <strong>禁用账户：</strong>将关联的账户设为禁用状态<br>
                                <strong>删除账户：</strong>同时删除所有关联的账户（谨慎选择）
                            </div>
                        </div>
                    }

                    <div class="form-check mb-3">
                        <input asp-for="ConfirmDelete" class="form-check-input" type="checkbox" required />
                        <label asp-for="ConfirmDelete" class="form-check-label">
                            我确认要删除此员工，并了解此操作不可撤销
                        </label>
                        <span asp-validation-for="ConfirmDelete" class="text-danger d-block"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('您确定要删除此员工吗？此操作不可撤销！')">
                            <i class="bi bi-trash"></i> 确认删除
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">影响分析</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> 删除影响</h6>
                    <ul class="mb-0">
                        <li><strong>关联账户：</strong>@Model.RelatedAccountsCount 个</li>
                        <li><strong>邮件日志：</strong>可能包含此员工的邮件记录</li>
                        <li><strong>操作日志：</strong>相关操作记录将保留</li>
                    </ul>
                </div>

                @if (Model.RelatedAccountsCount > 0)
                {
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-shield-exclamation"></i> 重要提醒</h6>
                        <p class="mb-0">此员工关联了 <strong>@Model.RelatedAccountsCount</strong> 个账户，请谨慎选择处理方式。建议先禁用账户，确认无影响后再删除。</p>
                    </div>
                }

                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> 建议操作</h6>
                    <ol class="mb-0">
                        <li>如果员工离职，建议先设置为"离职"状态</li>
                        <li>观察一段时间确认无影响</li>
                        <li>再考虑删除员工记录</li>
                        <li>删除前备份重要数据</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
