using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using Microsoft.EntityFrameworkCore;

namespace DeviceAccountManager.Services
{
    public interface IPasswordService
    {
        Task<PasswordPolicy> GetDefaultPolicyAsync();
        Task<List<Account>> GetAccountsNeedingPasswordChangeAsync();
        Task<bool> ChangePasswordAsync(int accountId, string newPassword, string reason, int changedByUserId);
        Task<string> GeneratePasswordAsync(int? policyId = null);
        Task<bool> ValidatePasswordAsync(string password, int? policyId = null);
        Task ScheduleQuarterlyPasswordChangesAsync();
        Task<List<Account>> GetPasswordExpiringAccountsAsync(int daysAhead = 7);
    }

    public class PasswordService : IPasswordService
    {
        private readonly ApplicationDbContext _context;

        public PasswordService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<PasswordPolicy> GetDefaultPolicyAsync()
        {
            var policy = await _context.PasswordPolicies
                .FirstOrDefaultAsync(p => p.IsDefault && p.IsActive);

            if (policy == null)
            {
                // 创建默认策略
                policy = new PasswordPolicy
                {
                    PolicyName = "默认密码策略",
                    MinLength = 8,
                    MaxLength = 50,
                    RequireUppercase = true,
                    RequireLowercase = true,
                    RequireDigits = true,
                    RequireSpecialChars = true,
                    AllowedSpecialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?",
                    PasswordExpiryDays = 90,
                    PasswordHistoryCount = 5,
                    IsDefault = true,
                    IsActive = true,
                    Description = "系统默认密码策略",
                    CreatedAt = DateTime.Now
                };

                _context.PasswordPolicies.Add(policy);
                await _context.SaveChangesAsync();
            }

            return policy;
        }

        public async Task<List<Account>> GetAccountsNeedingPasswordChangeAsync()
        {
            var today = DateTime.Now.Date;
            
            return await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .Where(a => a.IsActive && 
                           !a.IsExemptFromPasswordChange && 
                           a.NextPasswordChangeAt.HasValue && 
                           a.NextPasswordChangeAt.Value.Date <= today)
                .OrderBy(a => a.NextPasswordChangeAt)
                .ToListAsync();
        }

        public async Task<List<Account>> GetPasswordExpiringAccountsAsync(int daysAhead = 7)
        {
            var targetDate = DateTime.Now.Date.AddDays(daysAhead);
            
            return await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .Where(a => a.IsActive && 
                           !a.IsExemptFromPasswordChange && 
                           a.NextPasswordChangeAt.HasValue && 
                           a.NextPasswordChangeAt.Value.Date <= targetDate &&
                           a.NextPasswordChangeAt.Value.Date > DateTime.Now.Date)
                .OrderBy(a => a.NextPasswordChangeAt)
                .ToListAsync();
        }

        public async Task<bool> ChangePasswordAsync(int accountId, string newPassword, string reason, int changedByUserId)
        {
            var account = await _context.Accounts.FindAsync(accountId);
            if (account == null)
                return false;

            // 验证密码强度
            if (!await ValidatePasswordAsync(newPassword))
                return false;

            // 检查密码历史
            var recentPasswords = await _context.PasswordHistories
                .Where(ph => ph.AccountId == accountId)
                .OrderByDescending(ph => ph.ChangeDate)
                .Take(5)
                .Select(ph => ph.NewPassword)
                .ToListAsync();

            var hashedNewPassword = BCrypt.Net.BCrypt.HashPassword(newPassword);
            
            // 检查是否与最近的密码重复
            foreach (var recentPassword in recentPasswords)
            {
                if (BCrypt.Net.BCrypt.Verify(newPassword, recentPassword))
                    return false; // 密码重复
            }

            // 保存旧密码
            var oldPassword = account.Password;

            // 更新账户密码
            account.Password = hashedNewPassword;
            account.LastPasswordChangeAt = DateTime.Now;
            account.UpdatedAt = DateTime.Now;

            // 设置下次密码更改时间
            if (!account.IsExemptFromPasswordChange)
            {
                var policy = await GetDefaultPolicyAsync();
                account.NextPasswordChangeAt = DateTime.Now.AddDays(policy.PasswordExpiryDays);
            }

            // 记录密码历史
            var passwordHistory = new PasswordHistory
            {
                AccountId = accountId,
                OldPassword = oldPassword,
                NewPassword = hashedNewPassword,
                ChangeDate = DateTime.Now,
                ChangeReason = reason,
                ChangedBy = changedByUserId.ToString()
            };

            _context.PasswordHistories.Add(passwordHistory);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<string> GeneratePasswordAsync(int? policyId = null)
        {
            PasswordPolicy policy;
            
            if (policyId.HasValue)
            {
                policy = await _context.PasswordPolicies.FindAsync(policyId.Value) 
                        ?? await GetDefaultPolicyAsync();
            }
            else
            {
                policy = await GetDefaultPolicyAsync();
            }

            return policy.GeneratePassword();
        }

        public async Task<bool> ValidatePasswordAsync(string password, int? policyId = null)
        {
            PasswordPolicy policy;
            
            if (policyId.HasValue)
            {
                policy = await _context.PasswordPolicies.FindAsync(policyId.Value) 
                        ?? await GetDefaultPolicyAsync();
            }
            else
            {
                policy = await GetDefaultPolicyAsync();
            }

            return policy.ValidatePassword(password);
        }

        public async Task ScheduleQuarterlyPasswordChangesAsync()
        {
            var policy = await GetDefaultPolicyAsync();
            var today = DateTime.Now.Date;

            // 获取所有需要设置密码更改计划的账户
            var accounts = await _context.Accounts
                .Where(a => a.IsActive && 
                           !a.IsExemptFromPasswordChange && 
                           (!a.NextPasswordChangeAt.HasValue || a.NextPasswordChangeAt.Value < today))
                .ToListAsync();

            foreach (var account in accounts)
            {
                // 如果从未设置过密码更改时间，或者已经过期
                if (!account.NextPasswordChangeAt.HasValue)
                {
                    account.NextPasswordChangeAt = today.AddDays(policy.PasswordExpiryDays);
                }
                else if (account.NextPasswordChangeAt.Value < today)
                {
                    // 已过期的账户，设置为立即需要更改
                    account.NextPasswordChangeAt = today;
                }

                account.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();
        }
    }
}
