using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 密码历史记录表
    /// </summary>
    public class PasswordHistory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(255)]
        public string OldPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        public DateTime ChangeDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string ChangeReason { get; set; } = string.Empty;

        [StringLength(50)]
        public string ChangedBy { get; set; } = string.Empty;

        // 外键
        [Required]
        public int AccountId { get; set; }

        // 导航属性
        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; } = null!;
    }
}
