@page
@model DeviceAccountManager.Pages.Accounts.EditModel
@{
    ViewData["Title"] = "编辑账户";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑账户</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a asp-page="./Details" asp-route-id="@Model.Account?.Id" class="btn btn-sm btn-outline-info">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">账户信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="Account.Id" />
                    <input type="hidden" asp-for="Account.Password" />
                    <input type="hidden" asp-for="Account.CreatedAt" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.Username" class="form-label">账户名 *</label>
                                <input asp-for="Account.Username" class="form-control" />
                                <span asp-validation-for="Account.Username" class="text-danger"></span>
                                <div class="form-text">账户的登录用户名</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.PermissionLevel" class="form-label">权限级别 *</label>
                                <select asp-for="Account.PermissionLevel" class="form-select">
                                    <option value="">请选择权限级别</option>
                                    <option value="Operator">操作者</option>
                                    <option value="Maintainer">维护者</option>
                                    <option value="Technician">工艺员</option>
                                    <option value="SuperAdmin">超级管理员</option>
                                </select>
                                <span asp-validation-for="Account.PermissionLevel" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.DeviceId" class="form-label">关联设备</label>
                                <select asp-for="Account.DeviceId" class="form-select" asp-items="Model.DeviceSelectList">
                                    <option value="">请选择设备</option>
                                </select>
                                <span asp-validation-for="Account.DeviceId" class="text-danger"></span>
                                <div class="form-text">选择此账户关联的设备</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Account.EmployeeId" class="form-label">绑定员工</label>
                                <select asp-for="Account.EmployeeId" class="form-select" asp-items="Model.EmployeeSelectList">
                                    <option value="">请选择员工</option>
                                </select>
                                <span asp-validation-for="Account.EmployeeId" class="text-danger"></span>
                                <div class="form-text">选择此账户绑定的员工</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Account.Description" class="form-label">账户描述</label>
                        <textarea asp-for="Account.Description" class="form-control" rows="3" placeholder="账户的用途和说明..."></textarea>
                        <span asp-validation-for="Account.Description" class="text-danger"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">密码更改设置</label>
                                <div class="form-check">
                                    <input asp-for="Account.IsExemptFromPasswordChange" class="form-check-input" type="checkbox" />
                                    <label asp-for="Account.IsExemptFromPasswordChange" class="form-check-label">
                                        免于季度密码更改
                                    </label>
                                </div>
                                <div class="form-text">勾选后此账户不会强制要求季度密码更改</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">账户状态</label>
                                <div class="form-check">
                                    <input asp-for="Account.IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="Account.IsActive" class="form-check-label">
                                        账户启用
                                    </label>
                                </div>
                                <div class="form-text">取消勾选将禁用此账户</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存更改
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <a asp-page="./ChangePassword" asp-route-id="@Model.Account?.Id" class="btn btn-warning">
                            <i class="bi bi-key"></i> 更改密码
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">账户信息</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 创建信息</h6>
                    <p><strong>创建时间：</strong>@Model.Account?.CreatedAt.ToString("yyyy-MM-dd HH:mm")</p>
                    @if (Model.Account?.UpdatedAt.HasValue == true)
                    {
                        <p><strong>更新时间：</strong>@Model.Account.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")</p>
                    }
                </div>
                
                @if (Model.Account?.LastPasswordChangeAt.HasValue == true)
                {
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-key"></i> 密码信息</h6>
                        <p><strong>上次更改：</strong>@Model.Account.LastPasswordChangeAt.Value.ToString("yyyy-MM-dd")</p>
                        @if (Model.Account.NextPasswordChangeAt.HasValue)
                        {
                            <p><strong>下次更改：</strong>@Model.Account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")</p>
                            @if (Model.Account.NextPasswordChangeAt.Value < DateTime.Now)
                            {
                                <span class="badge bg-danger">密码已过期</span>
                            }
                            else if (Model.Account.NextPasswordChangeAt.Value < DateTime.Now.AddDays(7))
                            {
                                <span class="badge bg-warning">即将过期</span>
                            }
                        }
                    </div>
                }

                <div class="alert alert-success">
                    <h6><i class="bi bi-shield-check"></i> 权限说明</h6>
                    <ul class="mb-0">
                        <li><strong>操作者：</strong>基本操作权限</li>
                        <li><strong>维护者：</strong>设备维护权限</li>
                        <li><strong>工艺员：</strong>工艺调整权限</li>
                        <li><strong>超级管理员：</strong>完全管理权限</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
