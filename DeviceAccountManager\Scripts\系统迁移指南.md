# 🔄 系统迁移指南

## 概述
本指南帮助您将设备账户权限管理系统从一台电脑迁移到另一台电脑。

## 📋 迁移前准备

### 1. 环境要求检查
新电脑需要安装以下软件：

#### 必需软件
- **Windows 10/11** (64位)
- **.NET 10.0 Runtime** 
  - 下载地址：https://dotnet.microsoft.com/download/dotnet/10.0
- **SQL Server Express 2019/2022**
  - 下载地址：https://www.microsoft.com/sql-server/sql-server-downloads
- **SQL Server Management Studio (SSMS)** (可选，便于管理)

#### 端口要求
- **HTTP端口**：5000 (生产) 或 5046 (开发)
- **SQL Server端口**：1433 (默认)

### 2. 数据备份
在原电脑上运行以下命令进行完整备份：

```batch
# 运行完整备份
cd DeviceAccountManager\Scripts
.\backup.bat

# 或使用系统内置备份功能
# 登录系统 → 系统管理 → 备份管理 → 创建备份
```

## 🚀 迁移步骤

### 步骤1：准备迁移包
在原电脑上创建迁移包：

```batch
# 运行迁移包创建脚本
.\create_migration_package.bat
```

### 步骤2：传输文件
将以下文件/文件夹复制到新电脑：
- `DeviceAccountManager_Migration_Package.zip`
- 或手动复制：
  - `DeviceAccountManager/publish/` (应用程序文件)
  - `Backup/` (数据库备份文件)
  - `Scripts/` (管理脚本)

### 步骤3：在新电脑上安装
1. **解压迁移包**到目标目录 (如：`C:\Applications\DeviceAccountManager\`)

2. **安装SQL Server Express**
   ```batch
   # 下载并安装 SQL Server Express
   # 确保启用 SQL Server Browser 服务
   ```

3. **恢复数据库**
   ```batch
   cd C:\Applications\DeviceAccountManager\Scripts
   .\restore_database.bat
   ```

4. **配置应用程序**
   ```batch
   # 更新配置文件
   .\configure_new_environment.bat
   ```

5. **启动系统**
   ```batch
   # 方式1：直接运行
   cd C:\Applications\DeviceAccountManager
   dotnet DeviceAccountManager.dll
   
   # 方式2：安装为Windows服务
   .\Scripts\install_service.bat
   ```

### 步骤4：验证迁移
1. **访问系统**：http://localhost:5000
2. **登录测试**：使用 admin / Admin123!
3. **功能验证**：检查所有模块是否正常工作
4. **数据验证**：确认所有数据完整迁移

## 🔧 配置调整

### 数据库连接字符串
如果新电脑的SQL Server配置不同，需要修改 `appsettings.json`：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=DeviceAccountManagerDb;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

### 端口配置
如果需要更改端口，修改 `appsettings.json`：

```json
{
  "Urls": "http://localhost:5000"
}
```

## 🚨 常见问题

### 问题1：数据库连接失败
**解决方案**：
1. 确认SQL Server Express已安装并启动
2. 检查SQL Server Browser服务是否运行
3. 验证数据库名称和连接字符串

### 问题2：端口被占用
**解决方案**：
1. 更改应用程序端口配置
2. 或停止占用端口的其他应用程序

### 问题3：权限不足
**解决方案**：
1. 以管理员身份运行安装脚本
2. 确保应用程序目录有适当的读写权限

## 📞 技术支持

如果迁移过程中遇到问题：
1. 查看日志文件：`logs/app-*.log`
2. 检查Windows事件日志
3. 使用健康检查工具：`.\Scripts\health-check.bat`

## 🔄 回滚计划

如果迁移失败，可以：
1. 在原电脑上继续使用系统
2. 重新执行迁移步骤
3. 联系技术支持获取帮助
