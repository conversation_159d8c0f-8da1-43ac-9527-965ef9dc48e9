@page
@model DeviceAccountManager.Pages.Devices.IndexModel
@{
    ViewData["Title"] = "设备管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">设备管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Create" class="btn btn-sm btn-primary">
                <i class="bi bi-plus-circle"></i> 添加设备
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="get">
            <div class="input-group">
                <input type="text" class="form-control" name="searchString" value="@Model.SearchString" placeholder="搜索设备代码或名称...">
                <button class="btn btn-outline-secondary" type="submit">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-6">
        <form method="get">
            <div class="input-group">
                <select class="form-select" name="statusFilter" onchange="this.form.submit()">
                    <option value="">所有状态</option>
                    <option value="Active" selected="@(Model.StatusFilter == "Active")">运行中</option>
                    <option value="Maintenance" selected="@(Model.StatusFilter == "Maintenance")">维护中</option>
                    <option value="Offline" selected="@(Model.StatusFilter == "Offline")">离线</option>
                </select>
                <input type="hidden" name="searchString" value="@Model.SearchString" />
            </div>
        </form>
    </div>
</div>

<!-- 设备列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">设备列表</h6>
    </div>
    <div class="card-body">
        @if (Model.Devices.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>设备代码</th>
                            <th>设备名称</th>
                            <th>生产线</th>
                            <th>位置</th>
                            <th>状态</th>
                            <th>账户数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var device in Model.Devices)
                        {
                            <tr>
                                <td>
                                    <strong>@device.DeviceCode</strong>
                                </td>
                                <td>@device.DeviceName</td>
                                <td>@device.ProductionLine</td>
                                <td>@device.Location</td>
                                <td>
                                    @switch (device.Status)
                                    {
                                        case "Active":
                                            <span class="badge bg-success">运行中</span>
                                            break;
                                        case "Maintenance":
                                            <span class="badge bg-warning">维护中</span>
                                            break;
                                        case "Offline":
                                            <span class="badge bg-danger">离线</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@device.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@(device.Accounts?.Count ?? 0)</span>
                                </td>
                                <td>@device.CreatedAt.ToString("yyyy-MM-dd")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-page="./Details" asp-route-id="@device.Id" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="./Edit" asp-route-id="@device.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        @if (Model.IsSuperAdmin || Model.IsTechnician)
                                        {
                                            <a asp-page="./Delete" asp-route-id="@device.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-inbox" style="font-size: 3rem; color: #ccc;"></i>
                <p class="text-muted mt-2">暂无设备数据</p>
                <a asp-page="./Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加第一个设备
                </a>
            </div>
        }
    </div>
</div>

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.ActiveDevicesCount</h5>
                <p class="card-text">运行中设备</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.MaintenanceDevicesCount</h5>
                <p class="card-text">维护中设备</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">@Model.OfflineDevicesCount</h5>
                <p class="card-text">离线设备</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.TotalAccountsCount</h5>
                <p class="card-text">总账户数</p>
            </div>
        </div>
    </div>
</div>
