@page
@model DeviceAccountManager.Pages.Devices.CreateModel
@{
    ViewData["Title"] = "添加设备";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">添加设备</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">设备信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Device.DeviceCode" class="form-label">设备代码 *</label>
                                <input asp-for="Device.DeviceCode" class="form-control" placeholder="例如：DEV001" />
                                <span asp-validation-for="Device.DeviceCode" class="text-danger"></span>
                                <div class="form-text">设备的唯一标识代码</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Device.DeviceName" class="form-label">设备名称 *</label>
                                <input asp-for="Device.DeviceName" class="form-control" placeholder="例如：注塑机A1" />
                                <span asp-validation-for="Device.DeviceName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Device.ProductionLine" class="form-label">生产线</label>
                                <input asp-for="Device.ProductionLine" class="form-control" placeholder="例如：生产线1" />
                                <span asp-validation-for="Device.ProductionLine" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Device.Location" class="form-label">设备位置</label>
                                <input asp-for="Device.Location" class="form-control" placeholder="例如：车间A-区域1" />
                                <span asp-validation-for="Device.Location" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Device.Status" class="form-label">设备状态 *</label>
                        <select asp-for="Device.Status" class="form-select">
                            <option value="">请选择状态</option>
                            <option value="Active">运行中</option>
                            <option value="Maintenance">维护中</option>
                            <option value="Offline">离线</option>
                        </select>
                        <span asp-validation-for="Device.Status" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Device.Description" class="form-label">设备描述</label>
                        <textarea asp-for="Device.Description" class="form-control" rows="3" placeholder="设备的详细描述信息..."></textarea>
                        <span asp-validation-for="Device.Description" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存设备
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">操作说明</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 填写说明</h6>
                    <ul class="mb-0">
                        <li>设备代码必须唯一，建议使用规范的编码格式</li>
                        <li>设备名称应简洁明了，便于识别</li>
                        <li>生产线和位置信息有助于设备定位</li>
                        <li>设备状态影响账户管理策略</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> 注意事项</h6>
                    <ul class="mb-0">
                        <li>设备代码一旦创建不可修改</li>
                        <li>离线状态的设备账户将被暂停</li>
                        <li>维护状态的设备密码更新将被延迟</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
