is_global = true
build_property.TargetFramework = net10.0
build_property.TargetFramework = net10.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v10.0
build_property.RootNamespace = DeviceAccountManager
build_property.RootNamespace = DeviceAccountManager
build_property.ProjectDir = D:\Source\账号权限管理系统\DeviceAccountManager\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Source\账号权限管理系统\DeviceAccountManager
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 10.0
build_property.EnableCodeStyleSeverity = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/ChangePassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcQ2hhbmdlUGFzc3dvcmQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Accounts/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudHNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Devices/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGV2aWNlc1xDcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Devices/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGV2aWNlc1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/EmailManagement/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1haWxNYW5hZ2VtZW50XERldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/EmailManagement/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1haWxNYW5hZ2VtZW50XEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/EmailManagement/Settings.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1haWxNYW5hZ2VtZW50XFNldHRpbmdzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Employees/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1wbG95ZWVzXENyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Employees/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1wbG95ZWVzXERlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Employees/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1wbG95ZWVzXERldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Employees/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1wbG95ZWVzXEVkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Employees/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRW1wbG95ZWVzXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTG9naW4uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/PasswordManagement/BatchChange.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUGFzc3dvcmRNYW5hZ2VtZW50XEJhdGNoQ2hhbmdlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/PasswordManagement/BatchChangeResult.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUGFzc3dvcmRNYW5hZ2VtZW50XEJhdGNoQ2hhbmdlUmVzdWx0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/PasswordManagement/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUGFzc3dvcmRNYW5hZ2VtZW50XEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJpdmFjeS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/AuditLogs.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxBdWRpdExvZ3MuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Backup.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxCYWNrdXAuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Configuration.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxDb25maWd1cmF0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Monitor.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxNb25pdG9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Optimization.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxPcHRpbWl6YXRpb24uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/SystemManagement/Status.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3lzdGVtTWFuYWdlbWVudFxTdGF0dXMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Source/账号权限管理系统/DeviceAccountManager/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-q25inq5wfg
