@page
@model DeviceAccountManager.Pages.SystemManagement.BackupModel
@{
    ViewData["Title"] = "备份管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">备份管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回系统管理
            </a>
            <button type="button" class="btn btn-sm btn-success" onclick="createBackup()">
                <i class="bi bi-plus-circle"></i> 创建备份
            </button>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
        @Model.Message
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- 备份统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总备份数</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalBackups</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-archive fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">成功备份</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.SuccessfulBackups</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">总大小</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalSizeFormatted</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">最新备份</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            @if (Model.LatestBackup != null)
                            {
                                <small>@Model.LatestBackup.CreatedAt.ToString("MM-dd HH:mm")</small>
                            }
                            else
                            {
                                <small>无</small>
                            }
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建备份 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-success">
            <i class="bi bi-plus-circle"></i> 创建新备份
        </h6>
    </div>
    <div class="card-body">
        <form id="createBackupForm">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">备份名称</label>
                    <input type="text" class="form-control" id="backupName" name="backupName" 
                           value="<EMAIL>("yyyyMMdd_HHmmss")" required />
                </div>
                <div class="col-md-3">
                    <label class="form-label">备份类型</label>
                    <select class="form-select" id="backupType" name="backupType">
                        <option value="Full">完整备份</option>
                        <option value="Incremental">增量备份</option>
                        <option value="Differential">差异备份</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">描述</label>
                    <input type="text" class="form-control" id="description" name="description" 
                           placeholder="备份描述（可选）" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-success" onclick="createBackup()">
                            <i class="bi bi-play-circle"></i> 开始备份
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 备份设置 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-info">
            <i class="bi bi-gear"></i> 自动备份设置
        </h6>
    </div>
    <div class="card-body">
        <form id="backupSettingsForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="autoBackupEnabled" 
                               checked="@Model.AutoBackupEnabled" />
                        <label class="form-check-label" for="autoBackupEnabled">
                            启用自动备份
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">备份频率</label>
                    <select class="form-select" id="backupFrequency">
                        <option value="Daily">每日</option>
                        <option value="Weekly" selected="@(Model.BackupFrequency == "Weekly")">每周</option>
                        <option value="Monthly" selected="@(Model.BackupFrequency == "Monthly")">每月</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">保留天数</label>
                    <input type="number" class="form-control" id="retentionDays" 
                           value="@Model.BackupRetentionDays" min="1" max="365" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-info" onclick="saveBackupSettings()">
                            <i class="bi bi-check-circle"></i> 保存设置
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 备份列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">备份历史</h6>
    </div>
    <div class="card-body">
        @if (Model.Backups.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>备份名称</th>
                            <th>类型</th>
                            <th>大小</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>完成时间</th>
                            <th>创建者</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var backup in Model.Backups)
                        {
                            <tr>
                                <td>
                                    <strong>@backup.BackupName</strong>
                                    @if (!string.IsNullOrEmpty(backup.Description))
                                    {
                                        <br><small class="text-muted">@backup.Description</small>
                                    }
                                </td>
                                <td>
                                    @switch (backup.BackupType)
                                    {
                                        case "Full":
                                            <span class="badge bg-primary">完整</span>
                                            break;
                                        case "Incremental":
                                            <span class="badge bg-info">增量</span>
                                            break;
                                        case "Differential":
                                            <span class="badge bg-warning">差异</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@backup.BackupType</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @if (backup.FileSize > 0)
                                    {
                                        @FormatFileSize(backup.FileSize)
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @switch (backup.Status)
                                    {
                                        case "Completed":
                                            <span class="badge bg-success">已完成</span>
                                            break;
                                        case "InProgress":
                                            <span class="badge bg-info">进行中</span>
                                            break;
                                        case "Failed":
                                            <span class="badge bg-danger">失败</span>
                                            break;
                                        case "Pending":
                                            <span class="badge bg-warning">等待中</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@backup.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <small>@backup.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                </td>
                                <td>
                                    @if (backup.CompletedAt.HasValue)
                                    {
                                        <small>@backup.CompletedAt.Value.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <small>@backup.CreatedByUser.Username</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        @if (backup.Status == "Completed")
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="downloadBackup(@backup.Id)">
                                                <i class="bi bi-download"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="restoreBackup(@backup.Id)">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        }
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteBackup(@backup.Id)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-archive fa-3x text-muted"></i>
                <p class="text-muted mt-2">暂无备份记录</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function createBackup() {
            const form = document.getElementById('createBackupForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            fetch('/api/backup/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('备份任务已创建，正在后台执行...');
                    location.reload();
                } else {
                    alert('创建备份失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建备份失败');
            });
        }

        function saveBackupSettings() {
            const settings = {
                autoBackupEnabled: document.getElementById('autoBackupEnabled').checked,
                backupFrequency: document.getElementById('backupFrequency').value,
                retentionDays: parseInt(document.getElementById('retentionDays').value)
            };

            fetch('/api/backup/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('备份设置已保存');
                } else {
                    alert('保存设置失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存设置失败');
            });
        }

        function downloadBackup(backupId) {
            window.open(`/api/backup/download/${backupId}`, '_blank');
        }

        function restoreBackup(backupId) {
            if (confirm('确定要恢复此备份吗？这将覆盖当前数据，请确保已做好准备。')) {
                fetch(`/api/backup/restore/${backupId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('备份恢复已开始，请稍候...');
                    } else {
                        alert('恢复失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('恢复失败');
                });
            }
        }

        function deleteBackup(backupId) {
            if (confirm('确定要删除此备份吗？此操作不可恢复。')) {
                fetch(`/api/backup/delete/${backupId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('备份已删除');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }
    </script>
}

@functions {
    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}
