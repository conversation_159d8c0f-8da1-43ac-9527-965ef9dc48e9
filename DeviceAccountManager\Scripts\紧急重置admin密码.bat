@echo off
echo ================================
echo Emergency Admin Password Reset
echo ================================
echo.
echo WARNING: This tool is for emergency use only
echo.

set /p confirm="Confirm reset admin password? (Type YES to continue): "
if not "%confirm%"=="YES" (
    echo Operation cancelled
    pause
    exit /b
)

echo.
echo Resetting admin password to: Admin123!
echo.

sqlcmd -S .\SQLEXPRESS -E -d DeviceAccountManagerDb -i reset_admin_password.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: admin password has been reset to: Admin123!
    echo.
    echo Please login immediately and change to a new password
    echo Recommend setting a strong password and changing regularly
) else (
    echo.
    echo ERROR: Password reset failed, please check database connection
)

echo.
pause
