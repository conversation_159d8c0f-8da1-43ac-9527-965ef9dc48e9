using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Pages.EmailManagement
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmailService _emailService;

        public IndexModel(ApplicationDbContext context, IEmailService emailService)
        {
            _context = context;
            _emailService = emailService;
        }

        public IList<EmailLog> EmailLogs { get; set; } = default!;
        
        // 统计数据
        public int TotalEmails { get; set; }
        public int SentEmails { get; set; }
        public int PendingEmails { get; set; }
        public int FailedEmails { get; set; }

        // 筛选参数
        [BindProperty(SupportsGet = true)]
        public string StatusFilter { get; set; } = string.Empty;

        [BindProperty(SupportsGet = true)]
        public string EmailTypeFilter { get; set; } = string.Empty;

        [BindProperty(SupportsGet = true)]
        public string SearchTerm { get; set; } = string.Empty;

        // 分页参数
        [BindProperty(SupportsGet = true)]
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int PageSize { get; set; } = 20;

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限：只有工艺员及以上级别可以查看邮件管理
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            await LoadStatistics();
            await LoadEmailLogs();

            return Page();
        }

        private async Task LoadStatistics()
        {
            TotalEmails = await _context.EmailLogs.CountAsync();
            SentEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Sent");
            PendingEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Pending");
            FailedEmails = await _context.EmailLogs.CountAsync(e => e.Status == "Failed");
        }

        private async Task LoadEmailLogs()
        {
            var query = _context.EmailLogs
                .Include(e => e.Account)
                .ThenInclude(a => a.Employee)
                .AsQueryable();

            // 应用筛选
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                query = query.Where(e => e.Status == StatusFilter);
            }

            if (!string.IsNullOrEmpty(EmailTypeFilter))
            {
                query = query.Where(e => e.EmailType == EmailTypeFilter);
            }

            if (!string.IsNullOrEmpty(SearchTerm))
            {
                query = query.Where(e => 
                    e.ToName.Contains(SearchTerm) || 
                    e.ToEmail.Contains(SearchTerm) || 
                    e.Subject.Contains(SearchTerm));
            }

            // 计算总页数
            var totalCount = await query.CountAsync();
            TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize);

            // 应用分页
            EmailLogs = await query
                .OrderByDescending(e => e.CreatedAt)
                .Skip((CurrentPage - 1) * PageSize)
                .Take(PageSize)
                .ToListAsync();
        }
    }
}
