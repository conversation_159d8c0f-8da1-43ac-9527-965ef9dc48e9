# 生产设备账号权限管理系统 - API接口文档

## 📋 文档信息

- **文档版本**: v1.0
- **API版本**: v1.0
- **基础URL**: `http://localhost:5000/api`
- **认证方式**: Session-based Authentication
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证说明

### 认证机制
所有API接口都需要用户登录后才能访问。系统使用Session-based认证：

1. 用户通过登录页面进行身份验证
2. 成功登录后，服务器创建Session
3. 后续API请求自动携带Session Cookie
4. Session过期后需要重新登录

### 权限控制
API接口根据用户角色进行权限控制：

| 角色 | 权限说明 |
|------|----------|
| SuperAdmin | 所有API接口的完全访问权限 |
| Technician | 账户、员工、密码管理相关接口 |
| Maintainer | 仅限查看和修改自己的信息 |
| Operator | 仅限查看自己的基本信息 |

## 📊 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [
      // 数据项列表
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

## 👥 账户管理接口

### 1. 获取账户列表
```http
GET /api/accounts
```

**查询参数**:
- `page` (int, 可选): 页码，默认1
- `pageSize` (int, 可选): 每页数量，默认20
- `search` (string, 可选): 搜索关键词
- `role` (string, 可选): 角色筛选
- `isActive` (bool, 可选): 状态筛选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "SuperAdmin",
        "isActive": true,
        "lastLoginAt": "2025-01-01T10:30:00Z",
        "createdAt": "2024-12-01T09:00:00Z",
        "employee": {
          "id": 1,
          "name": "管理员",
          "department": "IT部门"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 1,
      "totalPages": 1
    }
  }
}
```

### 2. 获取单个账户信息
```http
GET /api/accounts/{id}
```

**路径参数**:
- `id` (int): 账户ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "SuperAdmin",
    "isActive": true,
    "lastLoginAt": "2025-01-01T10:30:00Z",
    "passwordChangedAt": "2024-12-01T09:00:00Z",
    "createdAt": "2024-12-01T09:00:00Z",
    "updatedAt": "2025-01-01T08:00:00Z",
    "employee": {
      "id": 1,
      "employeeCode": "EMP001",
      "name": "管理员",
      "department": "IT部门",
      "position": "系统管理员",
      "email": "<EMAIL>",
      "phone": "***********"
    }
  }
}
```

### 3. 创建账户
```http
POST /api/accounts
```

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "role": "Operator",
  "employeeId": 2,
  "sendWelcomeEmail": true
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 2,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "Operator",
    "isActive": true,
    "temporaryPassword": "TempPass123!",
    "createdAt": "2025-01-01T12:00:00Z"
  },
  "message": "账户创建成功，临时密码已发送到用户邮箱"
}
```

### 4. 更新账户信息
```http
PUT /api/accounts/{id}
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "role": "Technician",
  "isActive": true
}
```

### 5. 删除账户
```http
DELETE /api/accounts/{id}
```

**响应示例**:
```json
{
  "success": true,
  "message": "账户删除成功"
}
```

## 👨‍💼 员工管理接口

### 1. 获取员工列表
```http
GET /api/employees
```

**查询参数**:
- `page` (int): 页码
- `pageSize` (int): 每页数量
- `search` (string): 搜索关键词
- `department` (string): 部门筛选
- `isActive` (bool): 状态筛选

### 2. 创建员工
```http
POST /api/employees
```

**请求体**:
```json
{
  "employeeCode": "EMP002",
  "name": "张三",
  "department": "生产部",
  "position": "操作员",
  "email": "<EMAIL>",
  "phone": "***********",
  "hireDate": "2025-01-01"
}
```

### 3. 批量导入员工
```http
POST /api/employees/import
```

**请求体**: FormData with Excel file
- `file`: Excel文件

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalRows": 10,
    "successCount": 8,
    "failureCount": 2,
    "errors": [
      {
        "row": 3,
        "error": "员工编号已存在"
      },
      {
        "row": 7,
        "error": "邮箱格式不正确"
      }
    ]
  },
  "message": "批量导入完成，成功8条，失败2条"
}
```

## 🔑 密码管理接口

### 1. 生成密码
```http
POST /api/password/generate
```

**请求体**:
```json
{
  "length": 12,
  "includeUppercase": true,
  "includeLowercase": true,
  "includeNumbers": true,
  "includeSpecialChars": true,
  "excludeSimilar": true
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "password": "Kp9#mN2$vL8x",
    "strength": "Strong",
    "entropy": 78.5
  }
}
```

### 2. 验证密码强度
```http
POST /api/password/validate
```

**请求体**:
```json
{
  "password": "TestPassword123!",
  "username": "testuser"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "strength": "Strong",
    "score": 85,
    "feedback": [
      "密码长度符合要求",
      "包含大小写字母",
      "包含数字和特殊字符"
    ],
    "warnings": []
  }
}
```

### 3. 批量重置密码
```http
POST /api/password/batch-reset
```

**请求体**:
```json
{
  "userIds": [1, 2, 3],
  "passwordSettings": {
    "length": 12,
    "includeUppercase": true,
    "includeLowercase": true,
    "includeNumbers": true,
    "includeSpecialChars": true
  },
  "sendEmail": true
}
```

### 4. 获取密码历史
```http
GET /api/password/history/{userId}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "createdAt": "2025-01-01T10:00:00Z",
      "isCurrentPassword": true
    },
    {
      "id": 2,
      "createdAt": "2024-10-01T10:00:00Z",
      "isCurrentPassword": false
    }
  ]
}
```

## 📧 邮件管理接口

### 1. 发送测试邮件
```http
POST /api/email/test
```

**请求体**:
```json
{
  "toEmail": "<EMAIL>",
  "subject": "测试邮件",
  "body": "这是一封测试邮件"
}
```

### 2. 获取邮件日志
```http
GET /api/email/logs
```

**查询参数**:
- `page` (int): 页码
- `pageSize` (int): 每页数量
- `status` (string): 状态筛选 (Pending/Sent/Failed)
- `startDate` (datetime): 开始时间
- `endDate` (datetime): 结束时间

### 3. 获取邮件统计
```http
GET /api/email/statistics
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalSent": 150,
    "totalFailed": 5,
    "successRate": 96.77,
    "last24Hours": {
      "sent": 20,
      "failed": 1
    },
    "last7Days": {
      "sent": 85,
      "failed": 3
    }
  }
}
```

## ⚙️ 系统管理接口

### 1. 获取系统信息
```http
GET /api/system/info
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "systemName": "生产设备账号权限管理系统",
    "version": "1.0.0",
    "environment": "Production",
    "serverTime": "2025-01-01T12:00:00Z",
    "uptime": "5 days, 10 hours, 30 minutes",
    "database": {
      "type": "SQL Server Express",
      "version": "15.0.2000.5",
      "status": "Connected"
    }
  }
}
```

### 2. 获取系统健康状态
```http
GET /api/system/health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overall": "Healthy",
    "checks": [
      {
        "name": "Database",
        "status": "Healthy",
        "responseTime": "15ms"
      },
      {
        "name": "Email Service",
        "status": "Healthy",
        "responseTime": "120ms"
      },
      {
        "name": "Disk Space",
        "status": "Warning",
        "details": "Available: 2.5GB (15%)"
      }
    ]
  }
}
```

### 3. 获取系统配置
```http
GET /api/system/configurations
```

**查询参数**:
- `category` (string, 可选): 配置分类

### 4. 更新系统配置
```http
PUT /api/system/configurations/{key}
```

**请求体**:
```json
{
  "value": "new-value",
  "description": "配置说明"
}
```

### 5. 创建系统备份
```http
POST /api/system/backup
```

**请求体**:
```json
{
  "name": "手动备份_20250101",
  "description": "系统升级前备份",
  "type": "Full"
}
```

### 6. 获取备份列表
```http
GET /api/system/backups
```

### 7. 下载备份文件
```http
GET /api/system/backups/{id}/download
```

## 📊 审计日志接口

### 1. 获取审计日志
```http
GET /api/audit/logs
```

**查询参数**:
- `page` (int): 页码
- `pageSize` (int): 每页数量
- `action` (string): 操作类型筛选
- `userId` (int): 用户ID筛选
- `entityType` (string): 实体类型筛选
- `startDate` (datetime): 开始时间
- `endDate` (datetime): 结束时间

### 2. 导出审计日志
```http
POST /api/audit/export
```

**请求体**:
```json
{
  "format": "Excel",
  "filters": {
    "startDate": "2025-01-01",
    "endDate": "2025-01-31",
    "actions": ["Create", "Update", "Delete"]
  }
}
```

## 🔍 搜索接口

### 1. 全局搜索
```http
GET /api/search
```

**查询参数**:
- `q` (string): 搜索关键词
- `type` (string, 可选): 搜索类型 (accounts/employees/all)
- `limit` (int, 可选): 结果数量限制

**响应示例**:
```json
{
  "success": true,
  "data": {
    "accounts": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "type": "account"
      }
    ],
    "employees": [
      {
        "id": 1,
        "name": "管理员",
        "department": "IT部门",
        "type": "employee"
      }
    ],
    "totalResults": 2
  }
}
```

## ❌ 错误代码说明

| 错误代码 | HTTP状态码 | 说明 |
|----------|------------|------|
| AUTH_REQUIRED | 401 | 需要登录认证 |
| ACCESS_DENIED | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| DUPLICATE_RESOURCE | 409 | 资源已存在 |
| SERVER_ERROR | 500 | 服务器内部错误 |
| DATABASE_ERROR | 500 | 数据库操作错误 |
| EMAIL_ERROR | 500 | 邮件发送错误 |

## 📝 使用示例

### JavaScript/jQuery 示例
```javascript
// 获取账户列表
$.ajax({
  url: '/api/accounts',
  method: 'GET',
  data: {
    page: 1,
    pageSize: 20,
    search: 'admin'
  },
  success: function(response) {
    if (response.success) {
      console.log('账户列表:', response.data.items);
    }
  },
  error: function(xhr) {
    console.error('请求失败:', xhr.responseJSON);
  }
});

// 创建新账户
$.ajax({
  url: '/api/accounts',
  method: 'POST',
  contentType: 'application/json',
  data: JSON.stringify({
    username: 'newuser',
    email: '<EMAIL>',
    role: 'Operator',
    employeeId: 2
  }),
  success: function(response) {
    if (response.success) {
      alert('账户创建成功');
    }
  }
});
```

### C# HttpClient 示例
```csharp
using (var client = new HttpClient())
{
    client.BaseAddress = new Uri("http://localhost:5000/");
    
    // 获取账户列表
    var response = await client.GetAsync("api/accounts?page=1&pageSize=20");
    if (response.IsSuccessStatusCode)
    {
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ApiResponse<PagedResult<Account>>>(content);
    }
}
```

---

**文档维护**: 本文档随API版本更新，请关注接口变更通知。如有疑问，请联系技术支持。
