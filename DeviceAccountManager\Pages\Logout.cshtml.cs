using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;

namespace DeviceAccountManager.Pages
{
    public class LogoutModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public LogoutModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var userIdStr = HttpContext.Session.GetString("UserId");
            if (!string.IsNullOrEmpty(userIdStr) && int.TryParse(userIdStr, out int userId))
            {
                // 记录登出日志
                var log = new OperationLog
                {
                    UserId = userId,
                    Operation = "Logout",
                    TargetType = "User",
                    TargetId = userId.ToString(),
                    Description = "用户登出系统",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                    UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    CreatedAt = DateTime.Now
                };
                _context.OperationLogs.Add(log);
                await _context.SaveChangesAsync();
            }

            // 清除Session
            HttpContext.Session.Clear();

            return RedirectToPage("/Login");
        }
    }
}
