using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeviceAccountManager.Models
{
    /// <summary>
    /// 邮件发送日志表
    /// </summary>
    public class EmailLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string ToEmail { get; set; } = string.Empty;

        [StringLength(50)]
        public string ToName { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 发送状态：Pending=待发送, Sent=已发送, Failed=发送失败
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending";

        [StringLength(500)]
        public string ErrorMessage { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? SentAt { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 邮件类型：PasswordChange=密码更改通知, SystemNotice=系统通知
        /// </summary>
        [StringLength(50)]
        public string EmailType { get; set; } = string.Empty;

        // 关联的账户ID（如果是密码更改通知）
        public int? AccountId { get; set; }

        // 导航属性
        [ForeignKey("AccountId")]
        public virtual Account? Account { get; set; }
    }
}
