using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;
using System.IO.Compression;
using System.Text.Json;

namespace DeviceAccountManager.Controllers
{
    [ApiController]
    [Route("api/backup")]
    public class BackupApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ISystemConfigurationService _configService;
        private readonly IAuditLogService _auditService;
        private readonly ILogger<BackupApiController> _logger;
        private readonly IWebHostEnvironment _environment;

        public BackupApiController(
            ApplicationDbContext context,
            ISystemConfigurationService configService,
            IAuditLogService auditService,
            ILogger<BackupApiController> logger,
            IWebHostEnvironment environment)
        {
            _context = context;
            _configService = configService;
            _auditService = auditService;
            _logger = logger;
            _environment = environment;
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateBackup([FromBody] CreateBackupRequest request)
        {
            try
            {
                // 检查权限
                var currentUser = GetCurrentUser();
                if (currentUser == null || currentUser.Role != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                // 创建备份记录
                var backup = new SystemBackup
                {
                    BackupName = request.BackupName,
                    BackupType = request.BackupType,
                    Description = request.Description,
                    Status = "Pending",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = currentUser.Id
                };

                _context.SystemBackups.Add(backup);
                await _context.SaveChangesAsync();

                // 记录审计日志
                await _auditService.LogActionAsync("Create", "SystemBackup", backup.Id, backup.BackupName,
                    null, backup, "创建系统备份", currentUser.Id, GetClientIpAddress(), GetUserAgent());

                // 启动后台备份任务
                _ = Task.Run(async () => await PerformBackupAsync(backup.Id));

                return Ok(new { success = true, message = "备份任务已创建", backupId = backup.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建备份失败");
                return StatusCode(500, new { success = false, message = "创建备份失败：" + ex.Message });
            }
        }

        [HttpPost("settings")]
        public async Task<IActionResult> SaveBackupSettings([FromBody] BackupSettingsRequest request)
        {
            try
            {
                // 检查权限
                var currentUser = GetCurrentUser();
                if (currentUser == null || currentUser.Role != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                // 保存备份设置
                await _configService.SetConfigValueAsync("Backup.AutoEnabled", request.AutoBackupEnabled, 
                    "自动备份启用状态", "Backup", currentUser.Id);
                await _configService.SetConfigValueAsync("Backup.Frequency", request.BackupFrequency, 
                    "备份频率", "Backup", currentUser.Id);
                await _configService.SetConfigValueAsync("Backup.RetentionDays", request.RetentionDays, 
                    "备份保留天数", "Backup", currentUser.Id);

                // 记录审计日志
                await _auditService.LogActionAsync("Update", "BackupSettings", null, "备份设置",
                    null, request, "更新备份设置", currentUser.Id, GetClientIpAddress(), GetUserAgent());

                return Ok(new { success = true, message = "备份设置已保存" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存备份设置失败");
                return StatusCode(500, new { success = false, message = "保存设置失败：" + ex.Message });
            }
        }

        [HttpGet("download/{backupId}")]
        public async Task<IActionResult> DownloadBackup(int backupId)
        {
            try
            {
                // 检查权限
                var currentUser = GetCurrentUser();
                if (currentUser == null || currentUser.Role != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null || backup.Status != "Completed")
                {
                    return NotFound(new { success = false, message = "备份文件不存在或未完成" });
                }

                var filePath = backup.FilePath;
                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    return NotFound(new { success = false, message = "备份文件不存在" });
                }

                // 记录审计日志
                await _auditService.LogActionAsync("Download", "SystemBackup", backup.Id, backup.BackupName,
                    null, null, "下载备份文件", currentUser.Id, GetClientIpAddress(), GetUserAgent());

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                var fileName = $"{backup.BackupName}_{backup.CreatedAt:yyyyMMdd_HHmmss}.zip";

                return File(fileBytes, "application/zip", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载备份失败: {BackupId}", backupId);
                return StatusCode(500, new { success = false, message = "下载失败：" + ex.Message });
            }
        }

        [HttpPost("restore/{backupId}")]
        public async Task<IActionResult> RestoreBackup(int backupId)
        {
            try
            {
                // 检查权限
                var currentUser = GetCurrentUser();
                if (currentUser == null || currentUser.Role != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null || backup.Status != "Completed")
                {
                    return NotFound(new { success = false, message = "备份文件不存在或未完成" });
                }

                // 记录审计日志
                await _auditService.LogActionAsync("Restore", "SystemBackup", backup.Id, backup.BackupName,
                    null, null, "恢复备份", currentUser.Id, GetClientIpAddress(), GetUserAgent());

                // 启动后台恢复任务
                _ = Task.Run(async () => await PerformRestoreAsync(backup.Id));

                return Ok(new { success = true, message = "备份恢复已开始" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复备份失败: {BackupId}", backupId);
                return StatusCode(500, new { success = false, message = "恢复失败：" + ex.Message });
            }
        }

        [HttpDelete("delete/{backupId}")]
        public async Task<IActionResult> DeleteBackup(int backupId)
        {
            try
            {
                // 检查权限
                var currentUser = GetCurrentUser();
                if (currentUser == null || currentUser.Role != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null)
                {
                    return NotFound(new { success = false, message = "备份记录不存在" });
                }

                // 删除备份文件
                if (!string.IsNullOrEmpty(backup.FilePath) && System.IO.File.Exists(backup.FilePath))
                {
                    System.IO.File.Delete(backup.FilePath);
                }

                // 删除数据库记录
                _context.SystemBackups.Remove(backup);
                await _context.SaveChangesAsync();

                // 记录审计日志
                await _auditService.LogActionAsync("Delete", "SystemBackup", backup.Id, backup.BackupName,
                    backup, null, "删除备份", currentUser.Id, GetClientIpAddress(), GetUserAgent());

                return Ok(new { success = true, message = "备份已删除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除备份失败: {BackupId}", backupId);
                return StatusCode(500, new { success = false, message = "删除失败：" + ex.Message });
            }
        }

        private async Task PerformBackupAsync(int backupId)
        {
            try
            {
                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup == null) return;

                // 更新状态为进行中
                backup.Status = "InProgress";
                await _context.SaveChangesAsync();

                // 创建备份目录
                var backupDir = Path.Combine(_environment.ContentRootPath, "Backups");
                Directory.CreateDirectory(backupDir);

                var fileName = $"{backup.BackupName}_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                var filePath = Path.Combine(backupDir, fileName);

                // 创建备份文件
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                using (var archive = new ZipArchive(fileStream, ZipArchiveMode.Create))
                {
                    // 备份数据库数据
                    await BackupDatabaseDataAsync(archive);
                    
                    // 备份配置文件
                    await BackupConfigurationFilesAsync(archive);
                }

                // 更新备份记录
                var fileInfo = new FileInfo(filePath);
                backup.Status = "Completed";
                backup.FilePath = filePath;
                backup.FileSize = fileInfo.Length;
                backup.CompletedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                _logger.LogInformation("备份完成: {BackupName}", backup.BackupName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行备份失败: {BackupId}", backupId);
                
                var backup = await _context.SystemBackups.FindAsync(backupId);
                if (backup != null)
                {
                    backup.Status = "Failed";
                    backup.ErrorMessage = ex.Message;
                    await _context.SaveChangesAsync();
                }
            }
        }

        private async Task BackupDatabaseDataAsync(ZipArchive archive)
        {
            // 导出用户数据
            var users = await _context.Users.ToListAsync();
            var usersJson = JsonSerializer.Serialize(users, new JsonSerializerOptions { WriteIndented = true });
            var usersEntry = archive.CreateEntry("users.json");
            using var usersStream = usersEntry.Open();
            using var usersWriter = new StreamWriter(usersStream);
            await usersWriter.WriteAsync(usersJson);

            // 导出设备数据
            var devices = await _context.Devices.ToListAsync();
            var devicesJson = JsonSerializer.Serialize(devices, new JsonSerializerOptions { WriteIndented = true });
            var devicesEntry = archive.CreateEntry("devices.json");
            using var devicesStream = devicesEntry.Open();
            using var devicesWriter = new StreamWriter(devicesStream);
            await devicesWriter.WriteAsync(devicesJson);

            // 导出账户数据
            var accounts = await _context.Accounts.ToListAsync();
            var accountsJson = JsonSerializer.Serialize(accounts, new JsonSerializerOptions { WriteIndented = true });
            var accountsEntry = archive.CreateEntry("accounts.json");
            using var accountsStream = accountsEntry.Open();
            using var accountsWriter = new StreamWriter(accountsStream);
            await accountsWriter.WriteAsync(accountsJson);

            // 导出员工数据
            var employees = await _context.Employees.ToListAsync();
            var employeesJson = JsonSerializer.Serialize(employees, new JsonSerializerOptions { WriteIndented = true });
            var employeesEntry = archive.CreateEntry("employees.json");
            using var employeesStream = employeesEntry.Open();
            using var employeesWriter = new StreamWriter(employeesStream);
            await employeesWriter.WriteAsync(employeesJson);

            // 导出系统配置
            var configs = await _context.SystemConfigurations.ToListAsync();
            var configsJson = JsonSerializer.Serialize(configs, new JsonSerializerOptions { WriteIndented = true });
            var configsEntry = archive.CreateEntry("system_configurations.json");
            using var configsStream = configsEntry.Open();
            using var configsWriter = new StreamWriter(configsStream);
            await configsWriter.WriteAsync(configsJson);
        }

        private async Task BackupConfigurationFilesAsync(ZipArchive archive)
        {
            try
            {
                // 备份appsettings.json
                var appSettingsPath = Path.Combine(_environment.ContentRootPath, "appsettings.json");
                if (System.IO.File.Exists(appSettingsPath))
                {
                    var entry = archive.CreateEntry("appsettings.json");
                    using var entryStream = entry.Open();
                    using var fileStream = new FileStream(appSettingsPath, FileMode.Open, FileAccess.Read);
                    await fileStream.CopyToAsync(entryStream);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "备份配置文件失败");
            }
        }

        private async Task PerformRestoreAsync(int backupId)
        {
            // 恢复功能的实现（简化版本，实际生产环境需要更复杂的逻辑）
            _logger.LogInformation("备份恢复功能开发中: {BackupId}", backupId);
            await Task.Delay(1000); // 模拟恢复过程
        }

        private User? GetCurrentUser()
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username)) return null;

            return _context.Users.FirstOrDefault(u => u.Username == username && u.IsActive);
        }

        private string GetClientIpAddress()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private string GetUserAgent()
        {
            return HttpContext.Request.Headers["User-Agent"].ToString();
        }
    }

    public class CreateBackupRequest
    {
        public string BackupName { get; set; } = string.Empty;
        public string BackupType { get; set; } = "Full";
        public string Description { get; set; } = string.Empty;
    }

    public class BackupSettingsRequest
    {
        public bool AutoBackupEnabled { get; set; }
        public string BackupFrequency { get; set; } = "Weekly";
        public int RetentionDays { get; set; } = 30;
    }
}
