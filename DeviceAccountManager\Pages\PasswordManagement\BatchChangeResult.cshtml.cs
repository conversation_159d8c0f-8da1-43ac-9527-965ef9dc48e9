using Microsoft.AspNetCore.Mvc;
using DeviceAccountManager.Pages.Shared;
using System.Text.Json;

namespace DeviceAccountManager.Pages.PasswordManagement
{
    public class BatchChangeResultModel : AuthorizedPageModel
    {
        public class PasswordChangeResult
        {
            public int AccountId { get; set; }
            public string Username { get; set; } = string.Empty;
            public string NewPassword { get; set; } = string.Empty;
            public bool Success { get; set; }
            public string Error { get; set; } = string.Empty;
        }

        public List<PasswordChangeResult> Results { get; set; } = new();
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }

        public IActionResult OnGet()
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            // 从TempData获取结果
            if (TempData["BatchChangeResults"] is string resultsJson)
            {
                try
                {
                    var tupleResults = JsonSerializer.Deserialize<List<(int AccountId, string Username, string NewPassword, bool Success, string Error)>>(resultsJson);
                    Results = tupleResults?.Select(t => new PasswordChangeResult
                    {
                        AccountId = t.AccountId,
                        Username = t.Username,
                        NewPassword = t.NewPassword,
                        Success = t.Success,
                        Error = t.Error
                    }).ToList() ?? new List<PasswordChangeResult>();
                }
                catch
                {
                    Results = new List<PasswordChangeResult>();
                }
            }

            SuccessCount = TempData["SuccessCount"] as int? ?? 0;
            FailureCount = TempData["FailureCount"] as int? ?? 0;

            // 如果没有结果数据，重定向到批量更改页面
            if (!Results.Any())
            {
                return RedirectToPage("./BatchChange");
            }

            return Page();
        }
    }
}
