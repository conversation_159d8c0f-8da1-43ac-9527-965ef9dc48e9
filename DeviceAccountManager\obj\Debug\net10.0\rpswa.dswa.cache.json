{"GlobalPropertiesHash": "t9sT2XOt64JLhrMTeJQpbj3wVsDyqFOOyH+L0X+809E=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Pb0by8NLKUzgDfnzB6p3b/l05IhgZVcj00Xlz+/iJyg=", "JszJndW0cEtQs5AiADAz2ZD9YdAEauR4+WKR8uV3LoY=", "czlYC5Hnp6/QZ2w6Hoq1g6KqVhZ4OwluXAp8Paf+qJc=", "kN5z7m2EO/71qsIoXy5QG8dR6t+2QBECmezqBaScdVE=", "T7ON54ZFnfx7rf4wNGmBK373wQo515+YqXWgSECeHLU=", "Hhh3FHbvt0GBptp7WqfTguR3OzM2fdLKX9oS1bzIzuw=", "h8/aN/L8xTtIrTtKlGn56XS8sMjtfTwcRRB8PN3gxVY=", "P8+QpOrXTsPbS8pmNrit4YBdBTgxBiCcbhohHZ3sAAg=", "vVzfQyR4iSO3Y+0C+fabJDktQPbO2tEJdQPib0P1pY8=", "Vbsd8HQxPRIQyAvBUrHps80qS6sgT3N4AOpx+thA5EM=", "9ux2pRIxbKzV45td6O4So2IdFjUAHJ1cxvN/nd1MsBg=", "2nRLOsoOXbkdPThBQtC3ZTgm431pgqoqJIvO5Cwt6L8=", "lY3PG3dYkl+bsJd/aswr9M/ZCgOC8UR+jb+Wm7QyaUg=", "sVyVUXktquk1ozzWl4YqQoPuBlJ9VVrQEgals/SXJj0=", "yvWTBHnvIM29yFDI2FCdvWlvKIx8vqwvxgdoD9xR04M=", "A8//sb9SZXnYaZEQlT6FgdnbRYqu5xPPDHNBvRwUZ2w=", "KCiDPVykDAdRLdUgCz3lMXM5XgCRHZzrnDHs4IoOMiM=", "mpW3vsEyiTDtGNBF+P0oYYVGHOqdZunKWGbJkpyBwDc=", "kRfuiLjcg5EpwWkAj0LPLksHDRa4bOBE8z3Gxi94L9c=", "K2dYyrCj1XDJGSUKwWxnRpvjxw98/QK1QwgwOXc6bxM=", "9RvfTfC8GPWNrKa2RUQzLeKtRLK8N8tOeaGLzLliCkc=", "7ofFDFtF2RVUGSkOStzhxqDtoVFJzIV6pgx8J+JgAtM=", "d1rcJTfdSm9dr0rYhEULz9fW1QZ8Z2AlTcpP8wu6RfQ=", "l4dNN1eHNLLKCfQzGqPOC++4FozenZvy/KxfLuVv/q8=", "bgRyX+B0dr9LpPX+SVOQLjSuXAEJrZN5rVEpIgAp8n0=", "Y8Ix43LYksx6gs5fVjsUn1hN2Z07pxrcdHxL+2d+5V8=", "7hWnHtgZ2julCiI8PUdS0in9tsKLAiLByFT81BwuSTs=", "8v94V6DLRyTRRt7LcczVoLTIaBKksHR47lU0o5oh90c=", "Ku7pICmqN4VROVmIJ4dNlY1ACFFpdVHZ3MD+4L3cPc8=", "JwwGbOqks2Stm7J1+bMI5zLae8cBupo2unvCGGERTbo=", "E/Tyu8AnsdV5nZN24fPLBmRSIG+DkWO2ymPehbWdJXM=", "sZFA8n8PiomdZbG6oeuVvfgzT43Bs8ANl9XAfDvZBCk=", "BMN3k5GIvmWLDE6IwSOtI4Id7infQsL74sJKHCsZx2A=", "WvcO3JGSOrE3s6Azu/PHlrpwA1filgo4QXbsP6KuX3k=", "HNRKgnQFT1tiAyO4YU5CjAMQWTWDeCFudT59UUGrs10=", "3X2lZfYi8K+BtDpuFp7OHSi/IcLvbYUOHnwyfD/4qUA=", "8J8ZnYt2aXiTOUVx7DHJRuQFWUDiNtM7vPhFhPzNAuY=", "NOx5VOotiZXwHU4j/n26oeDQrcflOcbuZoDuy/bLaYc=", "RxRj2I0lfSvyBYY5MDMduCkXtEdEfWkEUBn4J71z354=", "hNakbFUZYArkUOnEpp2EIKsdYSfOswFWNYCiKcBP5os=", "iqQDmzbg2Gh/kSwIITRgUQOcsS6p/SH3JsryeEdb64k=", "zyxPhDTQplfl2UYsnoeaHqo35lK+bbl3mXsTLvIKVvk=", "4uFh5RgjCHM4+KoMtIF4o0RmtBAiqMln+UfelSgANBU=", "wDKUIKqHcjNVnopyXwiLb7JIwPGwF/nKYcR8YEW+53I=", "wscx4C7Jx5MRqZmHWqZFJxQ1P8FwDUEzuYMwTC64YRA=", "pkp4/T2U541ZUBuwWxhXF8sce0roiKJeDlTMtX5tFAY=", "3u+mnC9Z5eWLsM3pX4C7FQqSn6qXQwI/LQkiNNKq3aA=", "JFzFQI/vhbFolDoR8JOEudIhaUvWPsy/8E7JAprJ6f4=", "On91zI67G2NTtRL30jf0KIN0p9Q/5GRkxIeuS4jTqC8=", "PfylfrFDD8pRx8Wxav3FLKAMRhcIelMFVKj/SQsk59c=", "P2VakRZC8tFj7Z4/T32YWkV2cpqN5kD1JK+0ObEukeg=", "sSCEoNuR+OOpan2UTp+cYEL3OXX3bXLTpqiyX/Gvc3Q=", "P+rqPvTn+62klurSVx8tlr6ZCPj8ci4PsqOE9BTC+5o=", "ZJ98cVGckckXOgqKM+a3uluF1UMcvaUetN8DT3zGBmA=", "hAuLvrJHlW29EtUB/gpHpK/ApnKb2hJW/TVg+Vc5RRI=", "FJwxUCx8K3QycjiPjHbXXJlzoJSo0f6UYi5pWAAT4zw=", "06vy8F2hJEEF1Gq1HP0/Xm9/PDrJF7qfNMLPmshfUpc=", "eMKtPY6O2SFe7coe2icT5XgyUtTeWdJInU6iiUdKIY8=", "LBNsPMtQw1yVy4ZGLvSjlcK35PV0MLXkaeQF3B8B5bA=", "6ia0zNjmbMe/y3xJPu2CTosYtmZApb5Yw4U09oGZAHs=", "JcyQVU68JFJ97KKoMJwMY2A+5wIYIK2Hw85xHLSfvh0=", "6yDffsDILEPup7lEy+WZA7oLsLW5XzZbEILfevRDMNA=", "Fk9qj+54p3i00A9jGWmMq33Qi+4LV6MhtWAMY2aYsSI=", "WeI4E/K5sNtcTHF4ICN4QjBh1c2brbUAL5s3iAKN+4U=", "lhKmO3YpOFFy8zPA9SN9paDvyZ3WXUpJfwfOnXpH+k0=", "XUJLBLn6yApInTFQi9MqD6H02Bf72fIJNH/ep148joA=", "kZ8T1l5WZitHiGhSVMbj5HK05CQxhbG9uHLRbFCDniA=", "fpDCdggUbJSaM8kZYN+Z0ljJXjlPGDBDJaUb4LS2sS4=", "04ZzZdmfEaSm89AjCN5iWG/rxaw0Nz8wrdA7pHQGXiU=", "7PPtcjoRQB1nNrnVxzn7lTHc7h926uoiGjKMP2i3Fog=", "p43/43Q2RH0tligkQLrhhVHAc7b0eXmo67muKgkDsMc=", "14b9xhiyw006rWw6qXyZ1iNbe/rkRJgOXCdi05pA9L0=", "U5B34VZd90aV+RcHjKdp4l5qaBCcgWrQtfKKwGGJ95o=", "8hHRYqguJ3x0xKursULuAR0bRS4eym8JBo9hclD7TGs=", "miBLmmmdZGtx4F/iMUSu/UmLZrfHHjvJ+vOJ0aVzEF8=", "NBW/gR8YrEPEfJyHySgui8/lKUVl4mrNy1xvZqxKiw8=", "4RFzDTYZWC/0C+in8ZURRE4G98WVdmYF49YXu2SYpwQ=", "7u+t4jFslQyIiXqlHtwbEqT+y3ilJhbAoH7GfdeRQS4=", "4YeyRJ4aHlr2Nr2HZHzMaGTkzFI9ybTi4GA1jczSXBo=", "/uNbavwaFD6vFBBr3Wq5dhNnl0CULzbBaL1469TLOPE=", "iKeq4+4dQIOd+uZ4bJehPVLv6i2zkILq5Qlif5TfK5Q=", "RMKICjUNyW8c0T/of3Q4SHS5BwCfyC+jPztS7l9Uotw=", "yN0zFXB/WwLX+uM48SAvEtdmeqoc7H6U5vlLB68/oI4=", "TJNcBUO3XZX6na9xHbYS70BEYrJFk0tFZjgodzrsG78=", "GC8PEv2Z1kPgG3dNJHeTWB5wFU5L8YyR5yHrHKUmUzY=", "c1CDOZ0z7mUBMzgSdQaIL4bvDL80g+dn/7ya3yVoXuQ=", "WI0yWThDvCEsPaCPw1khx0gETX05/tBw4qcsaplUgbE=", "X0QCaIDWjvIqwH+hp2KQ54QmwjAp5A7WY8dVx3Cbxh4=", "zRp45gkm1Glvn9cXoB9ezg9kD4zcBJ4OhJhDBOeorwk=", "DaJ82nVBzdvNKdusMOjHURU8HbUScTdRsPPwdiNldqw=", "eZE8nSJQs8are53g80yhSpaXKr/Jej30HP7f3ozAnUs=", "JdNMYSdP6ZyvS3qRPkYdoYFnoy4nEt/APkZO27NCygU=", "bPZFhnbWOQHutB8aMcHaJYOWzf6XpfMYlXmS6nyb0gQ=", "iyEBEAcq3UdX4kTTKjwPijV9Adcnz+3+ocaXCGj+cSg=", "WknCONaP6pTORa0+QhuAW+s1KnVHuM+Hh4CozPzQ3b4=", "V+eaSGQyuXSNs4LHAwF1ckccZfCSe2gpLz2reSXq8DY=", "dYREcrriFL+8JVBJp+6QFM1Ch7heklSMkVCO2Gzd7cs=", "mhG+QrXYq9T2sCzaqRXkEIIBQuiuIA92XQeeVs6kmc4=", "oKVP1KWOLO7nDwioZXycelZSB4pRi8TNwoTIcTVom1Q=", "XE5UmkKzMFaNmMARKyP3DNfCTwjcrPn2IcqmfCkip+Y=", "hTCuySrgJYll+uQYBM6uoNzXDNNdMLXDjGExulp/JwU=", "kFtfN4oEBw0u0UZKTcSUOFzMqBsSMxDphAG0Ck/vz/U=", "+pyIB+2rtPh68/ZhJ+B3TmweFugV/WkJxabwQ54ld6g=", "wQ2xirzJxqjcCUM2bR+/7u+6PZhNDYi8/+w80LBE7zI=", "lc7mnV/EEDL6HDpJK/AwcG7Ik8eblHNiYlUubJsAk3Y=", "6KFDs3dOlvHbl6Q8GuHvFgZC82dRqGhPH4CHxnffnho="], "CachedAssets": {"Pb0by8NLKUzgDfnzB6p3b/l05IhgZVcj00Xlz+/iJyg=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\css\\site.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-31T17:09:44.8147325+00:00"}, "JszJndW0cEtQs5AiADAz2ZD9YdAEauR4+WKR8uV3LoY=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\favicon.ico", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-31T17:09:44.8134853+00:00"}, "czlYC5Hnp6/QZ2w6Hoq1g6KqVhZ4OwluXAp8Paf+qJc=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\js\\site.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-31T17:09:44.8154507+00:00"}, "kN5z7m2EO/71qsIoXy5QG8dR6t+2QBECmezqBaScdVE=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-31T17:09:44.8172542+00:00"}, "T7ON54ZFnfx7rf4wNGmBK373wQo515+YqXWgSECeHLU=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-31T17:09:44.8182292+00:00"}, "Hhh3FHbvt0GBptp7WqfTguR3OzM2fdLKX9oS1bzIzuw=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-31T17:09:44.8182292+00:00"}, "h8/aN/L8xTtIrTtKlGn56XS8sMjtfTwcRRB8PN3gxVY=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-31T17:09:44.8192323+00:00"}, "P8+QpOrXTsPbS8pmNrit4YBdBTgxBiCcbhohHZ3sAAg=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-31T17:09:44.8192323+00:00"}, "vVzfQyR4iSO3Y+0C+fabJDktQPbO2tEJdQPib0P1pY8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-31T17:09:44.8202288+00:00"}, "Vbsd8HQxPRIQyAvBUrHps80qS6sgT3N4AOpx+thA5EM=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-31T17:09:44.8212288+00:00"}, "9ux2pRIxbKzV45td6O4So2IdFjUAHJ1cxvN/nd1MsBg=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-31T17:09:44.8212288+00:00"}, "2nRLOsoOXbkdPThBQtC3ZTgm431pgqoqJIvO5Cwt6L8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-31T17:09:44.8222292+00:00"}, "lY3PG3dYkl+bsJd/aswr9M/ZCgOC8UR+jb+Wm7QyaUg=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-31T17:09:44.8222292+00:00"}, "sVyVUXktquk1ozzWl4YqQoPuBlJ9VVrQEgals/SXJj0=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-31T17:09:44.8232313+00:00"}, "yvWTBHnvIM29yFDI2FCdvWlvKIx8vqwvxgdoD9xR04M=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-31T17:09:44.8244539+00:00"}, "A8//sb9SZXnYaZEQlT6FgdnbRYqu5xPPDHNBvRwUZ2w=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-31T17:09:44.8244539+00:00"}, "KCiDPVykDAdRLdUgCz3lMXM5XgCRHZzrnDHs4IoOMiM=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-31T17:09:44.8255829+00:00"}, "mpW3vsEyiTDtGNBF+P0oYYVGHOqdZunKWGbJkpyBwDc=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-31T17:09:44.8255829+00:00"}, "kRfuiLjcg5EpwWkAj0LPLksHDRa4bOBE8z3Gxi94L9c=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-31T17:09:44.8266948+00:00"}, "K2dYyrCj1XDJGSUKwWxnRpvjxw98/QK1QwgwOXc6bxM=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-31T17:09:44.8266948+00:00"}, "9RvfTfC8GPWNrKa2RUQzLeKtRLK8N8tOeaGLzLliCkc=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-31T17:09:44.8276976+00:00"}, "7ofFDFtF2RVUGSkOStzhxqDtoVFJzIV6pgx8J+JgAtM=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-31T17:09:44.8286996+00:00"}, "d1rcJTfdSm9dr0rYhEULz9fW1QZ8Z2AlTcpP8wu6RfQ=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-31T17:09:44.8286996+00:00"}, "l4dNN1eHNLLKCfQzGqPOC++4FozenZvy/KxfLuVv/q8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-31T17:09:44.8301209+00:00"}, "bgRyX+B0dr9LpPX+SVOQLjSuXAEJrZN5rVEpIgAp8n0=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-31T17:09:44.8311248+00:00"}, "Y8Ix43LYksx6gs5fVjsUn1hN2Z07pxrcdHxL+2d+5V8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-31T17:09:44.8311248+00:00"}, "7hWnHtgZ2julCiI8PUdS0in9tsKLAiLByFT81BwuSTs=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-31T17:09:44.8321247+00:00"}, "8v94V6DLRyTRRt7LcczVoLTIaBKksHR47lU0o5oh90c=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-31T17:09:44.8331248+00:00"}, "Ku7pICmqN4VROVmIJ4dNlY1ACFFpdVHZ3MD+4L3cPc8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-31T17:09:44.8341209+00:00"}, "JwwGbOqks2Stm7J1+bMI5zLae8cBupo2unvCGGERTbo=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-31T17:09:44.8349432+00:00"}, "E/Tyu8AnsdV5nZN24fPLBmRSIG+DkWO2ymPehbWdJXM=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-31T17:09:44.8357811+00:00"}, "sZFA8n8PiomdZbG6oeuVvfgzT43Bs8ANl9XAfDvZBCk=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-31T17:09:44.8370068+00:00"}, "BMN3k5GIvmWLDE6IwSOtI4Id7infQsL74sJKHCsZx2A=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-31T17:09:44.8388241+00:00"}, "WvcO3JGSOrE3s6Azu/PHlrpwA1filgo4QXbsP6KuX3k=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-31T17:09:44.8398335+00:00"}, "HNRKgnQFT1tiAyO4YU5CjAMQWTWDeCFudT59UUGrs10=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-31T17:09:44.8411649+00:00"}, "3X2lZfYi8K+BtDpuFp7OHSi/IcLvbYUOHnwyfD/4qUA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-31T17:09:44.842169+00:00"}, "8J8ZnYt2aXiTOUVx7DHJRuQFWUDiNtM7vPhFhPzNAuY=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-31T17:09:44.8432861+00:00"}, "NOx5VOotiZXwHU4j/n26oeDQrcflOcbuZoDuy/bLaYc=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-31T17:09:44.844286+00:00"}, "RxRj2I0lfSvyBYY5MDMduCkXtEdEfWkEUBn4J71z354=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-31T17:09:44.8457899+00:00"}, "hNakbFUZYArkUOnEpp2EIKsdYSfOswFWNYCiKcBP5os=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-31T17:09:44.8457899+00:00"}, "iqQDmzbg2Gh/kSwIITRgUQOcsS6p/SH3JsryeEdb64k=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-31T17:09:44.8469017+00:00"}, "zyxPhDTQplfl2UYsnoeaHqo35lK+bbl3mXsTLvIKVvk=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-31T17:09:44.8480822+00:00"}, "4uFh5RgjCHM4+KoMtIF4o0RmtBAiqMln+UfelSgANBU=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-31T17:09:44.8490816+00:00"}, "wDKUIKqHcjNVnopyXwiLb7JIwPGwF/nKYcR8YEW+53I=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-31T17:09:44.8490816+00:00"}, "wscx4C7Jx5MRqZmHWqZFJxQ1P8FwDUEzuYMwTC64YRA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-31T17:09:44.85138+00:00"}, "pkp4/T2U541ZUBuwWxhXF8sce0roiKJeDlTMtX5tFAY=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-31T17:09:44.8586165+00:00"}, "3u+mnC9Z5eWLsM3pX4C7FQqSn6qXQwI/LQkiNNKq3aA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-31T17:09:44.8692521+00:00"}, "JFzFQI/vhbFolDoR8JOEudIhaUvWPsy/8E7JAprJ6f4=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-31T17:09:44.8163157+00:00"}, "On91zI67G2NTtRL30jf0KIN0p9Q/5GRkxIeuS4jTqC8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-31T17:09:44.9058958+00:00"}, "PfylfrFDD8pRx8Wxav3FLKAMRhcIelMFVKj/SQsk59c=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-31T17:09:44.906895+00:00"}, "P2VakRZC8tFj7Z4/T32YWkV2cpqN5kD1JK+0ObEukeg=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-31T17:09:44.9058958+00:00"}, "sSCEoNuR+OOpan2UTp+cYEL3OXX3bXLTpqiyX/Gvc3Q=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-31T17:09:44.9031551+00:00"}, "P+rqPvTn+62klurSVx8tlr6ZCPj8ci4PsqOE9BTC+5o=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-31T17:09:44.9031551+00:00"}, "ZJ98cVGckckXOgqKM+a3uluF1UMcvaUetN8DT3zGBmA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-31T17:09:44.9044041+00:00"}, "hAuLvrJHlW29EtUB/gpHpK/ApnKb2hJW/TVg+Vc5RRI=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-31T17:09:44.9044041+00:00"}, "FJwxUCx8K3QycjiPjHbXXJlzoJSo0f6UYi5pWAAT4zw=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-31T17:09:44.902158+00:00"}, "06vy8F2hJEEF1Gq1HP0/Xm9/PDrJF7qfNMLPmshfUpc=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-31T17:09:44.8981388+00:00"}, "eMKtPY6O2SFe7coe2icT5XgyUtTeWdJInU6iiUdKIY8=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-31T17:09:44.8981388+00:00"}, "LBNsPMtQw1yVy4ZGLvSjlcK35PV0MLXkaeQF3B8B5bA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-31T17:09:44.899145+00:00"}, "6ia0zNjmbMe/y3xJPu2CTosYtmZApb5Yw4U09oGZAHs=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-31T17:09:44.9001534+00:00"}, "JcyQVU68JFJ97KKoMJwMY2A+5wIYIK2Hw85xHLSfvh0=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-31T17:09:44.9011582+00:00"}, "6yDffsDILEPup7lEy+WZA7oLsLW5XzZbEILfevRDMNA=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-31T17:09:44.902158+00:00"}, "Fk9qj+54p3i00A9jGWmMq33Qi+4LV6MhtWAMY2aYsSI=": {"Identity": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "DeviceAccountManager", "SourceType": "Discovered", "ContentRoot": "D:\\Source\\账号权限管理系统\\DeviceAccountManager\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-31T17:09:44.8961377+00:00"}}, "CachedCopyCandidates": {}}