using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Accounts
{
    public class DetailsModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public DetailsModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public Account Account { get; set; } = default!;
        public IList<PasswordHistory> PasswordHistories { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (account == null)
            {
                return NotFound();
            }

            Account = account;

            // 获取密码历史记录
            PasswordHistories = await _context.PasswordHistories
                .Where(ph => ph.AccountId == id)
                .OrderByDescending(ph => ph.ChangeDate)
                .ToListAsync();

            return Page();
        }
    }
}
