using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Models
{
    public class SystemConfiguration
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string ConfigKey { get; set; } = string.Empty;

        [Required]
        public string ConfigValue { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        [StringLength(20)]
        public string DataType { get; set; } = "String"; // String, Integer, Boolean, Decimal

        public bool IsReadOnly { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        public int? CreatedByUserId { get; set; }
        public int? UpdatedByUserId { get; set; }

        // 导航属性
        public Account? CreatedByUser { get; set; }
        public Account? UpdatedByUser { get; set; }
    }

    public class AuditLog
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // Create, Update, Delete, Login, Logout, etc.

        [Required]
        [StringLength(100)]
        public string EntityType { get; set; } = string.Empty; // Account, Device, Employee, etc.

        public int? EntityId { get; set; }

        [StringLength(100)]
        public string EntityName { get; set; } = string.Empty;

        public string? OldValues { get; set; } // JSON格式的旧值
        public string? NewValues { get; set; } // JSON格式的新值

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(45)]
        public string IpAddress { get; set; } = string.Empty;

        [StringLength(500)]
        public string UserAgent { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public int UserId { get; set; }

        // 导航属性
        public Account User { get; set; } = null!;
    }

    public class SystemBackup
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string BackupName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        public long FileSize { get; set; }

        [StringLength(20)]
        public string BackupType { get; set; } = "Full"; // Full, Incremental, Differential

        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, InProgress, Completed, Failed

        [StringLength(1000)]
        public string ErrorMessage { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? CompletedAt { get; set; }

        public int CreatedByUserId { get; set; }

        // 导航属性
        public Account CreatedByUser { get; set; } = null!;
    }

    public class UserPreference
    {
        public int Id { get; set; }

        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string PreferenceKey { get; set; } = string.Empty;

        [Required]
        public string PreferenceValue { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        public Account User { get; set; } = null!;
    }
}
