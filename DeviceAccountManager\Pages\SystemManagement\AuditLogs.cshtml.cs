using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class AuditLogsModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuditLogService _auditService;

        public AuditLogsModel(ApplicationDbContext context, IAuditLogService auditService)
        {
            _context = context;
            _auditService = auditService;
        }

        public List<AuditLog> AuditLogs { get; set; } = new();
        public List<User> Users { get; set; } = new();

        // 筛选条件
        public string? FilterAction { get; set; }
        public string? FilterEntityType { get; set; }
        public int? FilterUserId { get; set; }
        public DateTime? FilterStartDate { get; set; }
        public DateTime? FilterEndDate { get; set; }

        // 分页
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages { get; set; }
        public int TotalLogs { get; set; }

        // 统计信息
        public int TodayLogs { get; set; }
        public int ActiveUsers { get; set; }
        public int FailedLogins { get; set; }

        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }

        public async Task<IActionResult> OnGetAsync(
            string? action = null,
            string? entityType = null,
            int? userId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1)
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            // 设置筛选条件
            FilterAction = action;
            FilterEntityType = entityType;
            FilterUserId = userId;
            FilterStartDate = startDate;
            FilterEndDate = endDate;
            CurrentPage = page;

            await LoadUsersAsync();
            await LoadAuditLogsAsync();
            await LoadStatisticsAsync();

            return Page();
        }

        private async Task LoadUsersAsync()
        {
            Users = await _context.Users
                .Where(u => u.IsActive)
                .OrderBy(u => u.Username)
                .ToListAsync();
        }

        private async Task LoadAuditLogsAsync()
        {
            // 获取总数
            TotalLogs = await _auditService.GetAuditLogCountAsync(
                FilterAction, FilterEntityType, FilterUserId, FilterStartDate, FilterEndDate);

            // 计算总页数
            TotalPages = (int)Math.Ceiling((double)TotalLogs / PageSize);

            // 获取当前页数据
            AuditLogs = await _auditService.GetAuditLogsAsync(
                CurrentPage, PageSize, FilterAction, FilterEntityType, FilterUserId, FilterStartDate, FilterEndDate);
        }

        private async Task LoadStatisticsAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            // 今日日志数
            TodayLogs = await _context.AuditLogs
                .Where(a => a.CreatedAt >= today && a.CreatedAt < tomorrow)
                .CountAsync();

            // 活跃用户数（最近7天有操作的用户）
            var sevenDaysAgo = DateTime.Now.AddDays(-7);
            ActiveUsers = await _context.AuditLogs
                .Where(a => a.CreatedAt >= sevenDaysAgo)
                .Select(a => a.UserId)
                .Distinct()
                .CountAsync();

            // 失败登录次数（最近24小时）
            var yesterday = DateTime.Now.AddDays(-1);
            FailedLogins = await _context.AuditLogs
                .Where(a => a.Action == "LoginFailed" && a.CreatedAt >= yesterday)
                .CountAsync();
        }
    }
}
