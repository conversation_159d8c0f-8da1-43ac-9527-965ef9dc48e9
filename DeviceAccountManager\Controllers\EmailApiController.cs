using Microsoft.AspNetCore.Mvc;
using DeviceAccountManager.Services;
using DeviceAccountManager.Data;
using Microsoft.EntityFrameworkCore;

namespace DeviceAccountManager.Controllers
{
    [Route("api/email")]
    [ApiController]
    public class EmailApiController : ControllerBase
    {
        private readonly IEmailService _emailService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EmailApiController> _logger;

        public EmailApiController(IEmailService emailService, ApplicationDbContext context, ILogger<EmailApiController> logger)
        {
            _emailService = emailService;
            _context = context;
            _logger = logger;
        }

        [HttpPost("process")]
        public async Task<IActionResult> ProcessEmails()
        {
            try
            {
                // 检查用户权限（简单的会话检查）
                var userIdClaim = HttpContext.Session.GetString("UserId");
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { success = false, message = "未授权访问" });
                }

                var userId = int.Parse(userIdClaim);
                var user = await _context.Accounts.FindAsync(userId);
                if (user == null || (user.PermissionLevel != "SuperAdmin" && user.PermissionLevel != "Technician"))
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                await _emailService.ProcessPendingEmailsAsync();

                _logger.LogInformation("用户 {UserId} 触发了邮件处理", userId);

                return Ok(new { success = true, message = "邮件处理完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理邮件时发生错误");
                return StatusCode(500, new { success = false, message = "处理邮件时发生错误" });
            }
        }

        [HttpPost("resend/{id}")]
        public async Task<IActionResult> ResendEmail(int id)
        {
            try
            {
                // 检查用户权限
                var userIdClaim = HttpContext.Session.GetString("UserId");
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { success = false, message = "未授权访问" });
                }

                var userId = int.Parse(userIdClaim);
                var user = await _context.Accounts.FindAsync(userId);
                if (user == null || (user.PermissionLevel != "SuperAdmin" && user.PermissionLevel != "Technician"))
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var success = await _emailService.ResendEmailAsync(id);

                if (success)
                {
                    _logger.LogInformation("用户 {UserId} 重新发送了邮件 {EmailId}", userId, id);
                    return Ok(new { success = true, message = "邮件重新发送成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "邮件重新发送失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新发送邮件 {EmailId} 时发生错误", id);
                return StatusCode(500, new { success = false, message = "重新发送邮件时发生错误" });
            }
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetEmailStats()
        {
            try
            {
                // 检查用户权限
                var userIdClaim = HttpContext.Session.GetString("UserId");
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { success = false, message = "未授权访问" });
                }

                var userId = int.Parse(userIdClaim);
                var user = await _context.Accounts.FindAsync(userId);
                if (user == null || (user.PermissionLevel != "SuperAdmin" && user.PermissionLevel != "Technician"))
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var stats = new
                {
                    total = await _context.EmailLogs.CountAsync(),
                    sent = await _context.EmailLogs.CountAsync(e => e.Status == "Sent"),
                    pending = await _context.EmailLogs.CountAsync(e => e.Status == "Pending"),
                    failed = await _context.EmailLogs.CountAsync(e => e.Status == "Failed"),
                    todaySent = await _context.EmailLogs.CountAsync(e => e.Status == "Sent" && e.SentAt.HasValue && e.SentAt.Value.Date == DateTime.Today),
                    recentFailures = await _context.EmailLogs
                        .Where(e => e.Status == "Failed" && e.CreatedAt >= DateTime.Now.AddDays(-7))
                        .CountAsync()
                };

                return Ok(new { success = true, data = stats });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取邮件统计信息时发生错误");
                return StatusCode(500, new { success = false, message = "获取统计信息时发生错误" });
            }
        }

        [HttpPost("test")]
        public async Task<IActionResult> SendTestEmail([FromBody] TestEmailRequest request)
        {
            try
            {
                // 检查用户权限
                var userIdClaim = HttpContext.Session.GetString("UserId");
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { success = false, message = "未授权访问" });
                }

                var userId = int.Parse(userIdClaim);
                var user = await _context.Accounts.FindAsync(userId);
                if (user == null || user.PermissionLevel != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "只有超级管理员可以发送测试邮件" });
                }

                if (string.IsNullOrEmpty(request.ToEmail) || string.IsNullOrEmpty(request.ToName))
                {
                    return BadRequest(new { success = false, message = "收件人信息不完整" });
                }

                var subject = "测试邮件 - 设备账户管理系统";
                var content = $@"
这是一封测试邮件。

发送时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}
发送人：{user.Username}

如果您收到这封邮件，说明邮件系统工作正常。

此邮件由系统自动发送，请勿回复。

设备账户管理系统
";

                var success = await _emailService.SendEmailAsync(request.ToEmail, request.ToName, subject, content, "Test");

                if (success)
                {
                    _logger.LogInformation("用户 {UserId} 发送了测试邮件到 {ToEmail}", userId, request.ToEmail);
                    return Ok(new { success = true, message = "测试邮件发送成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "测试邮件发送失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送测试邮件时发生错误");
                return StatusCode(500, new { success = false, message = "发送测试邮件时发生错误" });
            }
        }
    }

    public class TestEmailRequest
    {
        public string ToEmail { get; set; } = string.Empty;
        public string ToName { get; set; } = string.Empty;
    }
}
