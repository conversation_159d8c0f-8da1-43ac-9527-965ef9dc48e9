@page
@model DeviceAccountManager.Pages.SystemManagement.AuditLogsModel
@{
    ViewData["Title"] = "审计日志";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">审计日志</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回系统管理
            </a>
            <button type="button" class="btn btn-sm btn-warning" onclick="cleanupOldLogs()">
                <i class="bi bi-trash"></i> 清理旧日志
            </button>
            <button type="button" class="btn btn-sm btn-success" onclick="exportLogs()">
                <i class="bi bi-download"></i> 导出日志
            </button>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
        @Model.Message
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- 筛选条件 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-funnel"></i> 筛选条件
        </h6>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-2">
                <label class="form-label">操作类型</label>
                <select name="action" class="form-select">
                    <option value="">全部</option>
                    <option value="Create" selected="@(Model.FilterAction == "Create")">创建</option>
                    <option value="Update" selected="@(Model.FilterAction == "Update")">更新</option>
                    <option value="Delete" selected="@(Model.FilterAction == "Delete")">删除</option>
                    <option value="Login" selected="@(Model.FilterAction == "Login")">登录</option>
                    <option value="Logout" selected="@(Model.FilterAction == "Logout")">退出</option>
                    <option value="LoginFailed" selected="@(Model.FilterAction == "LoginFailed")">登录失败</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">对象类型</label>
                <select name="entityType" class="form-select">
                    <option value="">全部</option>
                    <option value="Account" selected="@(Model.FilterEntityType == "Account")">账户</option>
                    <option value="Device" selected="@(Model.FilterEntityType == "Device")">设备</option>
                    <option value="Employee" selected="@(Model.FilterEntityType == "Employee")">员工</option>
                    <option value="User" selected="@(Model.FilterEntityType == "User")">用户</option>
                    <option value="SystemConfiguration" selected="@(Model.FilterEntityType == "SystemConfiguration")">系统配置</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">用户</label>
                <select name="userId" class="form-select">
                    <option value="">全部用户</option>
                    @foreach (var user in Model.Users)
                    {
                        <option value="@user.Id" selected="@(Model.FilterUserId == user.Id)">@user.Username</option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">开始日期</label>
                <input type="date" name="startDate" class="form-control" value="@Model.FilterStartDate?.ToString("yyyy-MM-dd")" />
            </div>
            <div class="col-md-2">
                <label class="form-label">结束日期</label>
                <input type="date" name="endDate" class="form-control" value="@Model.FilterEndDate?.ToString("yyyy-MM-dd")" />
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总日志数</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalLogs</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-journal-text fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">今日操作</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TodayLogs</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">活跃用户</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ActiveUsers</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">失败登录</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.FailedLogins</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shield-exclamation fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 审计日志列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">审计日志列表</h6>
    </div>
    <div class="card-body">
        @if (Model.AuditLogs.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>时间</th>
                            <th>用户</th>
                            <th>操作</th>
                            <th>对象类型</th>
                            <th>对象名称</th>
                            <th>描述</th>
                            <th>IP地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var log in Model.AuditLogs)
                        {
                            <tr>
                                <td>
                                    <small>@log.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">@log.User.Username</span>
                                </td>
                                <td>
                                    @switch (log.Action)
                                    {
                                        case "Create":
                                            <span class="badge bg-success">创建</span>
                                            break;
                                        case "Update":
                                            <span class="badge bg-info">更新</span>
                                            break;
                                        case "Delete":
                                            <span class="badge bg-danger">删除</span>
                                            break;
                                        case "Login":
                                            <span class="badge bg-primary">登录</span>
                                            break;
                                        case "Logout":
                                            <span class="badge bg-secondary">退出</span>
                                            break;
                                        case "LoginFailed":
                                            <span class="badge bg-warning">登录失败</span>
                                            break;
                                        default:
                                            <span class="badge bg-light text-dark">@log.Action</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <small>@log.EntityType</small>
                                </td>
                                <td>
                                    <small>@log.EntityName</small>
                                </td>
                                <td>
                                    <small>@log.Description</small>
                                </td>
                                <td>
                                    <small>@log.IpAddress</small>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewLogDetails(@log.Id)">
                                        <i class="bi bi-eye"></i> 详情
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="审计日志分页">
                    <ul class="pagination justify-content-center">
                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage - 1)&action=@Model.FilterAction&entityType=@Model.FilterEntityType&userId=@Model.FilterUserId&startDate=@Model.FilterStartDate?.ToString("yyyy-MM-dd")&endDate=@Model.FilterEndDate?.ToString("yyyy-MM-dd")">上一页</a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" href="?page=@i&action=@Model.FilterAction&entityType=@Model.FilterEntityType&userId=@Model.FilterUserId&startDate=@Model.FilterStartDate?.ToString("yyyy-MM-dd")&endDate=@Model.FilterEndDate?.ToString("yyyy-MM-dd")">@i</a>
                            </li>
                        }

                        @if (Model.CurrentPage < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?page=@(Model.CurrentPage + 1)&action=@Model.FilterAction&entityType=@Model.FilterEntityType&userId=@Model.FilterUserId&startDate=@Model.FilterStartDate?.ToString("yyyy-MM-dd")&endDate=@Model.FilterEndDate?.ToString("yyyy-MM-dd")">下一页</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-journal-x fa-3x text-muted"></i>
                <p class="text-muted mt-2">暂无审计日志</p>
            </div>
        }
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewLogDetails(logId) {
            fetch(`/api/audit/details/${logId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('logDetailsContent').innerHTML = data.html;
                        new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
                    } else {
                        alert('获取日志详情失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取日志详情失败');
                });
        }

        function cleanupOldLogs() {
            if (confirm('确定要清理90天前的审计日志吗？此操作不可恢复。')) {
                fetch('/api/system/cleanup-logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('日志清理完成：' + data.message);
                        location.reload();
                    } else {
                        alert('清理失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('清理失败');
                });
            }
        }

        function exportLogs() {
            const params = new URLSearchParams(window.location.search);
            const exportUrl = '/api/audit/export?' + params.toString();
            window.open(exportUrl, '_blank');
        }
    </script>
}
