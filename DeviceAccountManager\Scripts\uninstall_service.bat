@echo off
echo ================================
echo   Windows Service Uninstallation
echo ================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: This script requires Administrator privileges
    echo Please run as Administrator
    pause
    exit /b 1
)

set SERVICE_NAME=DeviceAccountManager

echo Uninstalling Windows Service: %SERVICE_NAME%
echo.

:: 检查服务是否存在
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service "%SERVICE_NAME%" does not exist
    echo Nothing to uninstall
    pause
    exit /b 0
)

:: 显示服务状态
echo Current service status:
sc query "%SERVICE_NAME%"
echo.

set /p confirm="Are you sure you want to uninstall the service? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled
    pause
    exit /b 0
)

:: 停止服务
echo Stopping service...
net stop "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Service stopped successfully
) else (
    echo Service was not running or failed to stop
)

:: 等待服务完全停止
timeout /t 3 /nobreak >nul

:: 删除服务
echo Removing service...
sc delete "%SERVICE_NAME%"

if %errorLevel% equ 0 (
    echo Service removed successfully
    echo.
    echo ================================
    echo Uninstallation completed!
    echo.
    echo The application can still be run manually:
    echo   cd [Application Directory]
    echo   dotnet DeviceAccountManager.dll
) else (
    echo Error: Failed to remove service
    echo Please try again or remove manually using:
    echo   sc delete "%SERVICE_NAME%"
)

echo.
pause
