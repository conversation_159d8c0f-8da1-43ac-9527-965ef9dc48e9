using BCrypt.Net;
using Microsoft.Data.SqlClient;

class AdminPasswordReset
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== 管理员密码重置工具 ===");
        Console.WriteLine();
        
        if (args.Length == 0)
        {
            Console.WriteLine("使用方法:");
            Console.WriteLine("AdminPasswordReset.exe <用户名> [新密码]");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("AdminPasswordReset.exe admin");
            Console.WriteLine("AdminPasswordReset.exe admin NewPassword123!");
            Console.WriteLine();
            Console.WriteLine("如果不提供新密码，将生成随机密码");
            return;
        }
        
        string username = args[0];
        string newPassword = args.Length > 1 ? args[1] : GenerateRandomPassword();
        
        try
        {
            ResetPassword(username, newPassword);
            Console.WriteLine($"✅ 用户 '{username}' 的密码已成功重置");
            Console.WriteLine($"🔑 新密码: {newPassword}");
            Console.WriteLine();
            Console.WriteLine("⚠️  请妥善保管新密码，建议用户首次登录后立即修改");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 密码重置失败: {ex.Message}");
        }
    }
    
    static void ResetPassword(string username, string newPassword)
    {
        string connectionString = "Server=.\\SQLEXPRESS;Database=DeviceAccountManagerDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true";
        string hashedPassword = BCrypt.HashPassword(newPassword, 11);
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            
            // 检查用户是否存在
            string checkUserSql = "SELECT COUNT(*) FROM Users WHERE Username = @username";
            using (var checkCmd = new SqlCommand(checkUserSql, connection))
            {
                checkCmd.Parameters.AddWithValue("@username", username);
                int userCount = (int)checkCmd.ExecuteScalar();
                
                if (userCount == 0)
                {
                    throw new Exception($"用户 '{username}' 不存在");
                }
            }
            
            // 更新密码
            string updateSql = "UPDATE Users SET PasswordHash = @passwordHash, PasswordChangedAt = @changeTime WHERE Username = @username";
            using (var updateCmd = new SqlCommand(updateSql, connection))
            {
                updateCmd.Parameters.AddWithValue("@passwordHash", hashedPassword);
                updateCmd.Parameters.AddWithValue("@changeTime", DateTime.Now);
                updateCmd.Parameters.AddWithValue("@username", username);
                
                int rowsAffected = updateCmd.ExecuteNonQuery();
                if (rowsAffected == 0)
                {
                    throw new Exception("密码更新失败");
                }
            }
        }
    }
    
    static string GenerateRandomPassword()
    {
        const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%";
        var random = new Random();
        var password = new char[12];
        
        // 确保包含各种字符类型
        password[0] = "ABCDEFGHJKLMNPQRSTUVWXYZ"[random.Next(23)]; // 大写字母
        password[1] = "abcdefghijkmnpqrstuvwxyz"[random.Next(23)]; // 小写字母
        password[2] = "23456789"[random.Next(8)]; // 数字
        password[3] = "!@#$%"[random.Next(5)]; // 特殊字符
        
        // 填充剩余字符
        for (int i = 4; i < 12; i++)
        {
            password[i] = chars[random.Next(chars.Length)];
        }
        
        // 打乱顺序
        for (int i = 0; i < password.Length; i++)
        {
            int j = random.Next(password.Length);
            (password[i], password[j]) = (password[j], password[i]);
        }
        
        return new string(password);
    }
}
