# 生产设备账号权限管理系统 - 部署指南

## 📋 系统要求

### 硬件要求
- **CPU**: 双核 2.0GHz 或更高
- **内存**: 4GB RAM 或更高（推荐 8GB）
- **存储**: 20GB 可用磁盘空间
- **网络**: 稳定的网络连接（用于邮件发送）

### 软件要求
- **操作系统**: Windows 10/11 或 Windows Server 2019/2022
- **.NET Runtime**: .NET 10.0 Runtime
- **数据库**: SQL Server Express 2019 或更高版本
- **Web浏览器**: Chrome 90+, Firefox 88+, Edge 90+

## 🚀 安装步骤

### 1. 环境准备

#### 1.1 安装 .NET 10.0 Runtime
```bash
# 下载并安装 .NET 10.0 Runtime
# 访问: https://dotnet.microsoft.com/download/dotnet/10.0
```

#### 1.2 安装 SQL Server Express
```bash
# 下载 SQL Server Express 2022
# 访问: https://www.microsoft.com/sql-server/sql-server-downloads
# 选择 Express 版本进行安装
```

#### 1.3 配置 SQL Server
```sql
-- 启用 SQL Server 身份验证模式
-- 创建数据库用户（可选）
CREATE LOGIN [DeviceAccountUser] WITH PASSWORD = 'YourStrongPassword123!'
CREATE DATABASE DeviceAccountManager
USE DeviceAccountManager
CREATE USER [DeviceAccountUser] FOR LOGIN [DeviceAccountUser]
ALTER ROLE db_owner ADD MEMBER [DeviceAccountUser]
```

### 2. 应用程序部署

#### 2.1 发布应用程序
```bash
# 在开发环境中执行
cd DeviceAccountManager
dotnet publish -c Release -o ./publish
```

#### 2.2 复制文件到目标服务器
```
将 publish 文件夹复制到目标服务器，例如：
C:\Applications\DeviceAccountManager\
```

#### 2.3 配置连接字符串
编辑 `appsettings.json` 文件：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=DeviceAccountManager;Trusted_Connection=true;TrustServerCertificate=true;"
  },
  "EmailSettings": {
    "SmtpServer": "your-smtp-server.com",
    "SmtpPort": 587,
    "Username": "<EMAIL>",
    "Password": "your-email-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "设备账号管理系统"
  }
}
```

### 3. 数据库初始化

#### 3.1 运行数据库迁移
```bash
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll --migrate
```

#### 3.2 验证数据库创建
使用 SQL Server Management Studio 连接数据库，确认以下表已创建：
- Users (用户表)
- Employees (员工表)
- PasswordHistories (密码历史表)
- EmailLogs (邮件日志表)
- SystemConfigurations (系统配置表)
- AuditLogs (审计日志表)
- SystemBackups (系统备份表)

### 4. 服务配置

#### 4.1 创建 Windows 服务（可选）
```bash
# 使用 NSSM 创建 Windows 服务
nssm install DeviceAccountManager
nssm set DeviceAccountManager Application "C:\Applications\DeviceAccountManager\DeviceAccountManager.exe"
nssm set DeviceAccountManager AppDirectory "C:\Applications\DeviceAccountManager"
nssm set DeviceAccountManager DisplayName "设备账号权限管理系统"
nssm set DeviceAccountManager Description "生产设备账号权限管理系统服务"
nssm start DeviceAccountManager
```

#### 4.2 配置防火墙
```bash
# 开放应用程序端口（默认 5000）
netsh advfirewall firewall add rule name="DeviceAccountManager" dir=in action=allow protocol=TCP localport=5000
```

### 5. 首次启动配置

#### 5.1 启动应用程序
```bash
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll
```

#### 5.2 访问系统
打开浏览器访问：`http://localhost:5000`

#### 5.3 默认管理员登录
- **用户名**: admin
- **密码**: Admin123!

#### 5.4 修改默认密码
首次登录后，立即修改默认管理员密码。

## ⚙️ 系统配置

### 1. 邮件服务配置
1. 登录系统管理界面
2. 进入 "系统管理" > "系统配置"
3. 配置邮件服务器设置：
   - SMTP服务器地址
   - 端口号
   - 用户名和密码
   - SSL设置

### 2. 密码策略配置
1. 进入 "密码管理" > "密码策略"
2. 配置密码规则：
   - 最小长度
   - 复杂度要求
   - 历史记录数量
   - 有效期设置

### 3. 系统备份配置
1. 进入 "系统管理" > "备份管理"
2. 配置自动备份：
   - 备份频率
   - 保留天数
   - 备份路径

## 🔧 维护操作

### 1. 日常维护
```bash
# 检查系统状态
curl http://localhost:5000/api/system/health

# 查看系统日志
tail -f logs/app.log

# 清理临时文件
dotnet DeviceAccountManager.dll --cleanup
```

### 2. 数据备份
```bash
# 手动创建备份
curl -X POST http://localhost:5000/api/backup/create \
  -H "Content-Type: application/json" \
  -d '{"backupName":"Manual_Backup","description":"手动备份"}'
```

### 3. 系统更新
```bash
# 停止服务
net stop DeviceAccountManager

# 备份当前版本
xcopy C:\Applications\DeviceAccountManager C:\Backup\DeviceAccountManager_old /E /I

# 部署新版本
xcopy NewVersion\* C:\Applications\DeviceAccountManager /E /Y

# 运行数据库迁移
cd C:\Applications\DeviceAccountManager
dotnet DeviceAccountManager.dll --migrate

# 启动服务
net start DeviceAccountManager
```

## 🚨 故障排除

### 1. 常见问题

#### 应用程序无法启动
```bash
# 检查 .NET Runtime 是否安装
dotnet --version

# 检查端口是否被占用
netstat -an | findstr :5000

# 查看详细错误信息
dotnet DeviceAccountManager.dll --verbose
```

#### 数据库连接失败
```bash
# 检查 SQL Server 服务状态
net start | findstr SQL

# 测试数据库连接
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT @@VERSION"

# 检查连接字符串配置
type appsettings.json | findstr ConnectionStrings
```

#### 邮件发送失败
1. 检查 SMTP 服务器配置
2. 验证网络连接
3. 确认邮箱账户设置
4. 查看邮件日志：进入 "系统管理" > "审计日志"

### 2. 日志文件位置
- **应用程序日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **审计日志**: 数据库 AuditLogs 表
- **邮件日志**: 数据库 EmailLogs 表

### 3. 性能优化
```bash
# 清理过期日志
dotnet DeviceAccountManager.dll --cleanup-logs

# 优化数据库
sqlcmd -S .\SQLEXPRESS -E -Q "USE DeviceAccountManager; EXEC sp_updatestats"

# 重建索引
sqlcmd -S .\SQLEXPRESS -E -Q "USE DeviceAccountManager; EXEC sp_recompile"
```

## 📞 技术支持

### 联系信息
- **技术支持**: [技术支持邮箱]
- **紧急联系**: [紧急联系电话]
- **文档更新**: [文档版本日期]

### 系统监控
- **系统状态**: http://localhost:5000/SystemManagement/Status
- **系统监控**: http://localhost:5000/SystemManagement/Monitor
- **健康检查**: http://localhost:5000/api/system/health

---

**注意**: 
1. 部署前请确保所有依赖项已正确安装
2. 定期备份数据库和配置文件
3. 监控系统性能和日志文件
4. 及时更新系统补丁和安全设置
