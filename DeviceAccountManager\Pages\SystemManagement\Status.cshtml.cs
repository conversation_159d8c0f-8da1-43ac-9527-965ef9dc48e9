using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class StatusModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuditLogService _auditService;
        private readonly ILogger<StatusModel> _logger;

        public StatusModel(ApplicationDbContext context, IAuditLogService auditService, ILogger<StatusModel> logger)
        {
            _context = context;
            _auditService = auditService;
            _logger = logger;
        }

        public string SystemUptime { get; set; } = string.Empty;
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int TotalEmployees { get; set; }
        public int TodayLogins { get; set; }
        public DateTime StartTime { get; set; }
        public List<AuditLog> RecentEvents { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            var currentUserRole = HttpContext.Session.GetString("UserRole");
            if (currentUserRole != "SuperAdmin")
            {
                return RedirectToPage("/Account/AccessDenied");
            }

            await LoadSystemStatusAsync();
            return Page();
        }

        private async Task LoadSystemStatusAsync()
        {
            try
            {
                // 计算系统运行时间
                var process = System.Diagnostics.Process.GetCurrentProcess();
                StartTime = process.StartTime;
                var uptime = DateTime.Now - StartTime;
                SystemUptime = $"{uptime.Days}天 {uptime.Hours}小时 {uptime.Minutes}分钟";

                // 获取用户统计
                TotalUsers = await _context.Users.CountAsync();
                ActiveUsers = await _context.Users.Where(u => u.IsActive).CountAsync();

                // 获取员工统计
                TotalEmployees = await _context.Employees.CountAsync();

                // 获取今日登录数
                var today = DateTime.Today;
                TodayLogins = await _context.AuditLogs
                    .Where(a => a.Action == "Login" && a.CreatedAt >= today)
                    .CountAsync();

                // 获取最近系统事件
                RecentEvents = await _context.AuditLogs
                    .Where(a => a.EntityType == "System" || a.Action.Contains("系统") || a.Action.Contains("System"))
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(5)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载系统状态失败");
                SystemUptime = "未知";
                TotalUsers = 0;
                ActiveUsers = 0;
                TotalEmployees = 0;
                TodayLogins = 0;
                StartTime = DateTime.Now;
            }
        }
    }
}
