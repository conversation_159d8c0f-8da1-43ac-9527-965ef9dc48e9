using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace DeviceAccountManager.Services
{
    public interface IAuditLogService
    {
        Task LogActionAsync(string action, string entityType, int? entityId, string entityName, 
            object? oldValues, object? newValues, string description, int userId, string ipAddress, string userAgent);
        Task LogLoginAsync(int userId, string ipAddress, string userAgent, bool success);
        Task LogLogoutAsync(int userId, string ipAddress, string userAgent);
        Task<List<AuditLog>> GetAuditLogsAsync(int pageNumber = 1, int pageSize = 50, string? action = null, 
            string? entityType = null, int? userId = null, DateTime? startDate = null, DateTime? endDate = null);
        Task<int> GetAuditLogCountAsync(string? action = null, string? entityType = null, int? userId = null, 
            DateTime? startDate = null, DateTime? endDate = null);
        Task<List<AuditLog>> GetUserActivityAsync(int userId, int days = 30);
        Task CleanupOldLogsAsync(int retentionDays = 90);
    }

    public class AuditLogService : IAuditLogService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AuditLogService> _logger;

        public AuditLogService(ApplicationDbContext context, ILogger<AuditLogService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task LogActionAsync(string action, string entityType, int? entityId, string entityName, 
            object? oldValues, object? newValues, string description, int userId, string ipAddress, string userAgent)
        {
            try
            {
                var auditLog = new AuditLog
                {
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    EntityName = entityName,
                    OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                    NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                    Description = description,
                    UserId = userId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    CreatedAt = DateTime.Now
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录审计日志失败: {Action} {EntityType} {EntityId}", action, entityType, entityId);
            }
        }

        public async Task LogLoginAsync(int userId, string ipAddress, string userAgent, bool success)
        {
            var action = success ? "Login" : "LoginFailed";
            var description = success ? "用户登录成功" : "用户登录失败";
            
            await LogActionAsync(action, "User", userId, "", null, null, description, userId, ipAddress, userAgent);
        }

        public async Task LogLogoutAsync(int userId, string ipAddress, string userAgent)
        {
            await LogActionAsync("Logout", "User", userId, "", null, null, "用户退出登录", userId, ipAddress, userAgent);
        }

        public async Task<List<AuditLog>> GetAuditLogsAsync(int pageNumber = 1, int pageSize = 50, string? action = null, 
            string? entityType = null, int? userId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.AuditLogs.Include(a => a.User).AsQueryable();

            if (!string.IsNullOrEmpty(action))
                query = query.Where(a => a.Action == action);

            if (!string.IsNullOrEmpty(entityType))
                query = query.Where(a => a.EntityType == entityType);

            if (userId.HasValue)
                query = query.Where(a => a.UserId == userId.Value);

            if (startDate.HasValue)
                query = query.Where(a => a.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.CreatedAt <= endDate.Value);

            return await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetAuditLogCountAsync(string? action = null, string? entityType = null, int? userId = null, 
            DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.AuditLogs.AsQueryable();

            if (!string.IsNullOrEmpty(action))
                query = query.Where(a => a.Action == action);

            if (!string.IsNullOrEmpty(entityType))
                query = query.Where(a => a.EntityType == entityType);

            if (userId.HasValue)
                query = query.Where(a => a.UserId == userId.Value);

            if (startDate.HasValue)
                query = query.Where(a => a.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.CreatedAt <= endDate.Value);

            return await query.CountAsync();
        }

        public async Task<List<AuditLog>> GetUserActivityAsync(int userId, int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);
            
            return await _context.AuditLogs
                .Include(a => a.User)
                .Where(a => a.UserId == userId && a.CreatedAt >= startDate)
                .OrderByDescending(a => a.CreatedAt)
                .Take(100)
                .ToListAsync();
        }

        public async Task CleanupOldLogsAsync(int retentionDays = 90)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var oldLogs = await _context.AuditLogs
                    .Where(a => a.CreatedAt < cutoffDate)
                    .ToListAsync();

                if (oldLogs.Any())
                {
                    _context.AuditLogs.RemoveRange(oldLogs);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("清理了 {Count} 条过期审计日志", oldLogs.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理审计日志失败");
            }
        }
    }

    // 审计日志扩展方法
    public static class AuditLogExtensions
    {
        public static async Task LogCreateAsync<T>(this IAuditLogService auditService, T entity, int userId, 
            string ipAddress, string userAgent) where T : class
        {
            var entityType = typeof(T).Name;
            var entityName = GetEntityName(entity);
            var entityId = GetEntityId(entity);
            
            await auditService.LogActionAsync("Create", entityType, entityId, entityName, null, entity, 
                $"创建{entityType}: {entityName}", userId, ipAddress, userAgent);
        }

        public static async Task LogUpdateAsync<T>(this IAuditLogService auditService, T oldEntity, T newEntity, 
            int userId, string ipAddress, string userAgent) where T : class
        {
            var entityType = typeof(T).Name;
            var entityName = GetEntityName(newEntity);
            var entityId = GetEntityId(newEntity);
            
            await auditService.LogActionAsync("Update", entityType, entityId, entityName, oldEntity, newEntity, 
                $"更新{entityType}: {entityName}", userId, ipAddress, userAgent);
        }

        public static async Task LogDeleteAsync<T>(this IAuditLogService auditService, T entity, int userId, 
            string ipAddress, string userAgent) where T : class
        {
            var entityType = typeof(T).Name;
            var entityName = GetEntityName(entity);
            var entityId = GetEntityId(entity);
            
            await auditService.LogActionAsync("Delete", entityType, entityId, entityName, entity, null, 
                $"删除{entityType}: {entityName}", userId, ipAddress, userAgent);
        }

        private static string GetEntityName(object entity)
        {
            var type = entity.GetType();
            
            // 尝试获取常见的名称属性
            var nameProperty = type.GetProperty("Name") ?? 
                              type.GetProperty("Username") ?? 
                              type.GetProperty("DeviceCode") ?? 
                              type.GetProperty("Title") ?? 
                              type.GetProperty("Description");
            
            if (nameProperty != null)
            {
                var value = nameProperty.GetValue(entity);
                return value?.ToString() ?? "";
            }
            
            return "";
        }

        private static int? GetEntityId(object entity)
        {
            var type = entity.GetType();
            var idProperty = type.GetProperty("Id");
            
            if (idProperty != null)
            {
                var value = idProperty.GetValue(entity);
                if (value is int intValue)
                    return intValue;
            }
            
            return null;
        }
    }
}
