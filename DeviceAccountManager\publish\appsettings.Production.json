{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "Console": {"IncludeScopes": false}, "File": {"Path": "logs/app-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": ********, "IncludeScopes": true}}, "ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=DeviceAccountManager;Trusted_Connection=true;TrustServerCertificate=true;MultipleActiveResultSets=true;"}, "EmailSettings": {"SmtpServer": "smtp.company.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "YourEmailPassword", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "设备账号管理系统", "Timeout": 30000, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5}, "SystemSettings": {"SessionTimeoutMinutes": 30, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15, "PasswordExpirationDays": 90, "PasswordHistoryCount": 5, "AutoBackupEnabled": true, "AutoBackupIntervalHours": 24, "BackupRetentionDays": 30, "LogRetentionDays": 90, "MaintenanceMode": false}, "SecuritySettings": {"RequireHttps": false, "EnableAuditLogging": true, "EnableEmailLogging": true, "MaxFileUploadSizeMB": 10, "AllowedFileExtensions": [".xlsx", ".xls", ".csv"], "EnableRateLimiting": true, "RateLimitRequests": 100, "RateLimitWindowMinutes": 1}, "PerformanceSettings": {"DatabaseCommandTimeout": 30, "HttpClientTimeout": 30, "CacheExpirationMinutes": 60, "MaxConcurrentBackups": 1, "MaxConcurrentEmailSends": 5}, "AllowedHosts": "*", "Urls": "http://localhost:5000"}