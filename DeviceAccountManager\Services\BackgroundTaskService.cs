using DeviceAccountManager.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DeviceAccountManager.Services
{
    public class BackgroundTaskService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BackgroundTaskService> _logger;
        private readonly TimeSpan _period = TimeSpan.FromHours(1); // 每小时检查一次

        public BackgroundTaskService(IServiceProvider serviceProvider, ILogger<BackgroundTaskService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("后台任务服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await DoWorkAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "后台任务执行失败");
                }

                await Task.Delay(_period, stoppingToken);
            }

            _logger.LogInformation("后台任务服务已停止");
        }

        private async Task DoWorkAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var configService = scope.ServiceProvider.GetRequiredService<ISystemConfigurationService>();
            var backupService = scope.ServiceProvider.GetRequiredService<IBackupService>();
            var auditService = scope.ServiceProvider.GetRequiredService<IAuditLogService>();

            // 检查是否需要执行自动备份
            await CheckAutoBackupAsync(configService, backupService);

            // 清理过期的审计日志
            await CleanupAuditLogsAsync(configService, auditService);

            // 清理过期的备份
            await backupService.CleanupOldBackupsAsync();
        }

        private async Task CheckAutoBackupAsync(ISystemConfigurationService configService, IBackupService backupService)
        {
            try
            {
                var autoBackupEnabled = await configService.GetConfigValueAsync("Backup.AutoEnabled", false);
                if (!autoBackupEnabled)
                {
                    return;
                }

                var frequency = await configService.GetConfigValueAsync("Backup.Frequency", "Weekly");
                var lastBackupTime = await configService.GetConfigValueAsync("Backup.LastAutoBackupTime", DateTime.MinValue);

                var shouldBackup = false;
                var now = DateTime.Now;

                switch (frequency.ToLower())
                {
                    case "daily":
                        shouldBackup = (now - lastBackupTime).TotalDays >= 1;
                        break;
                    case "weekly":
                        shouldBackup = (now - lastBackupTime).TotalDays >= 7;
                        break;
                    case "monthly":
                        shouldBackup = (now - lastBackupTime).TotalDays >= 30;
                        break;
                }

                if (shouldBackup)
                {
                    // 检查是否有备份正在进行
                    var isBackupInProgress = await backupService.IsBackupInProgressAsync();
                    if (!isBackupInProgress)
                    {
                        var backupName = $"AutoBackup_{now:yyyyMMdd_HHmmss}";
                        var success = await backupService.CreateBackupAsync(backupName, "Full", "自动备份", 0);

                        if (success)
                        {
                            await configService.SetConfigValueAsync("Backup.LastAutoBackupTime", now, 
                                "最后自动备份时间", "Backup", 0);
                            _logger.LogInformation("自动备份完成: {BackupName}", backupName);
                        }
                        else
                        {
                            _logger.LogError("自动备份失败: {BackupName}", backupName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查自动备份失败");
            }
        }

        private async Task CleanupAuditLogsAsync(ISystemConfigurationService configService, IAuditLogService auditService)
        {
            try
            {
                var retentionDays = await configService.GetConfigValueAsync("AuditLog.RetentionDays", 90);
                await auditService.CleanupOldLogsAsync(retentionDays);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理审计日志失败");
            }
        }
    }
}
