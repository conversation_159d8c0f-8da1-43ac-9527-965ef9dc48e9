using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Models
{
    public class PasswordPolicy
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string PolicyName { get; set; } = string.Empty;

        [Range(6, 50)]
        public int MinLength { get; set; } = 8;

        [Range(6, 100)]
        public int MaxLength { get; set; } = 50;

        public bool RequireUppercase { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireDigits { get; set; } = true;
        public bool RequireSpecialChars { get; set; } = true;

        [StringLength(50)]
        public string AllowedSpecialChars { get; set; } = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        [Range(1, 365)]
        public int PasswordExpiryDays { get; set; } = 90;

        [Range(1, 50)]
        public int PasswordHistoryCount { get; set; } = 5;

        public bool IsDefault { get; set; } = false;
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // 验证密码是否符合策略
        public bool ValidatePassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            if (password.Length < MinLength || password.Length > MaxLength)
                return false;

            if (RequireUppercase && !password.Any(char.IsUpper))
                return false;

            if (RequireLowercase && !password.Any(char.IsLower))
                return false;

            if (RequireDigits && !password.Any(char.IsDigit))
                return false;

            if (RequireSpecialChars && !password.Any(c => AllowedSpecialChars.Contains(c)))
                return false;

            return true;
        }

        // 生成符合策略的密码
        public string GeneratePassword()
        {
            var random = new Random();
            var password = new List<char>();

            // 确保包含必需的字符类型
            if (RequireUppercase)
                password.Add("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[random.Next(26)]);

            if (RequireLowercase)
                password.Add("abcdefghijklmnopqrstuvwxyz"[random.Next(26)]);

            if (RequireDigits)
                password.Add("0123456789"[random.Next(10)]);

            if (RequireSpecialChars)
                password.Add(AllowedSpecialChars[random.Next(AllowedSpecialChars.Length)]);

            // 填充剩余长度
            var allChars = "";
            if (RequireUppercase) allChars += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            if (RequireLowercase) allChars += "abcdefghijklmnopqrstuvwxyz";
            if (RequireDigits) allChars += "0123456789";
            if (RequireSpecialChars) allChars += AllowedSpecialChars;

            while (password.Count < MinLength)
            {
                password.Add(allChars[random.Next(allChars.Length)]);
            }

            // 打乱顺序
            return new string(password.OrderBy(x => random.Next()).ToArray());
        }

        // 获取密码强度描述
        public string GetPasswordStrengthDescription(string password)
        {
            if (string.IsNullOrEmpty(password))
                return "请输入密码";

            var issues = new List<string>();

            if (password.Length < MinLength)
                issues.Add($"长度至少{MinLength}位");
            else if (password.Length > MaxLength)
                issues.Add($"长度不能超过{MaxLength}位");

            if (RequireUppercase && !password.Any(char.IsUpper))
                issues.Add("需要大写字母");

            if (RequireLowercase && !password.Any(char.IsLower))
                issues.Add("需要小写字母");

            if (RequireDigits && !password.Any(char.IsDigit))
                issues.Add("需要数字");

            if (RequireSpecialChars && !password.Any(c => AllowedSpecialChars.Contains(c)))
                issues.Add("需要特殊字符");

            if (issues.Any())
                return "密码不符合要求：" + string.Join("、", issues);

            return "密码符合要求";
        }
    }
}
