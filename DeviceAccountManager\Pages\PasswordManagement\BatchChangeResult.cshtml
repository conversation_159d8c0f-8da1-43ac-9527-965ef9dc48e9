@page
@model DeviceAccountManager.Pages.PasswordManagement.BatchChangeResultModel
@{
    ViewData["Title"] = "批量更改密码结果";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">批量更改密码结果</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回密码管理
            </a>
            <a asp-page="./BatchChange" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-plus"></i> 再次批量更改
            </a>
            @if (Model.Results.Any(r => r.Success))
            {
                <button type="button" class="btn btn-sm btn-success" onclick="exportResults()">
                    <i class="bi bi-download"></i> 导出结果
                </button>
            }
        </div>
    </div>
</div>

<!-- 结果统计 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            成功更改
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.SuccessCount</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle-fill fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            更改失败
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.FailureCount</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-x-circle-fill fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总计处理
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@(Model.SuccessCount + Model.FailureCount)</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-list-check fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if (Model.SuccessCount > 0)
{
    <!-- 成功更改的账户 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="bi bi-check-circle-fill"></i> 成功更改密码的账户 (@Model.SuccessCount)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-info-circle"></i>
                <strong>重要提醒：</strong>请妥善保管以下新密码信息，建议立即打印或安全存储。
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="successTable">
                    <thead class="table-light">
                        <tr>
                            <th>账户名</th>
                            <th>新密码</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var result in Model.Results.Where(r => r.Success))
                        {
                            <tr class="table-success">
                                <td><strong>@result.Username</strong></td>
                                <td>
                                    <div class="input-group">
                                        <input type="password" class="form-control password-field" value="@result.NewPassword" readonly>
                                        <button type="button" class="btn btn-outline-secondary" onclick="togglePassword(this)">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="copyPassword('@result.NewPassword')">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle"></i> 成功
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

@if (Model.FailureCount > 0)
{
    <!-- 失败的账户 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="bi bi-x-circle-fill"></i> 更改失败的账户 (@Model.FailureCount)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>注意：</strong>以下账户密码更改失败，请检查错误信息并手动处理。
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>账户名</th>
                            <th>失败原因</th>
                            <th>建议操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var result in Model.Results.Where(r => !r.Success))
                        {
                            <tr class="table-danger">
                                <td><strong>@result.Username</strong></td>
                                <td>@result.Error</td>
                                <td>
                                    <a href="/Accounts/ChangePassword/@result.AccountId" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-key"></i> 手动更改
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<!-- 操作建议 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-info">
            <i class="bi bi-lightbulb"></i> 后续操作建议
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>成功更改的账户：</h6>
                <ul>
                    <li>将新密码安全地传达给相关员工</li>
                    <li>提醒员工首次登录后修改为个人密码</li>
                    <li>确认邮件通知已正确发送</li>
                    <li>定期检查密码使用情况</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>失败的账户：</h6>
                <ul>
                    <li>检查失败原因并解决问题</li>
                    <li>手动为失败的账户更改密码</li>
                    <li>确认账户状态和权限设置</li>
                    <li>必要时联系系统管理员</li>
                </ul>
            </div>
        </div>
        
        @if (Model.SuccessCount > 0)
        {
            <hr>
            <div class="alert alert-warning">
                <i class="bi bi-shield-exclamation"></i>
                <strong>安全提醒：</strong>
                <ul class="mb-0">
                    <li>请在安全的环境中查看和传递密码信息</li>
                    <li>建议使用加密方式传递密码给员工</li>
                    <li>及时清理浏览器历史记录和临时文件</li>
                    <li>定期检查账户的密码更改记录</li>
                </ul>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function togglePassword(button) {
            const input = button.parentElement.querySelector('.password-field');
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'bi bi-eye';
            }
        }

        function copyPassword(password) {
            navigator.clipboard.writeText(password).then(function() {
                // 显示复制成功提示
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0';
                toast.style.position = 'fixed';
                toast.style.top = '20px';
                toast.style.right = '20px';
                toast.style.zIndex = '9999';
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle"></i> 密码已复制到剪贴板
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
                    </div>
                `;
                document.body.appendChild(toast);
                
                // 3秒后自动移除
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 3000);
            }).catch(function(err) {
                alert('复制失败，请手动选择密码');
            });
        }

        function exportResults() {
            // 创建CSV内容
            let csvContent = "账户名,新密码,状态\n";
            
            @foreach (var result in Model.Results.Where(r => r.Success))
            {
                <text>csvContent += "@result.Username,@result.NewPassword,成功\n";</text>
            }
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', '批量密码更改结果_' + new Date().toISOString().slice(0, 10) + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 页面加载完成后显示成功提示
        document.addEventListener('DOMContentLoaded', function() {
            @if (Model.SuccessCount > 0)
            {
                <text>
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success alert-dismissible fade show';
                successAlert.style.position = 'fixed';
                successAlert.style.top = '20px';
                successAlert.style.left = '50%';
                successAlert.style.transform = 'translateX(-50%)';
                successAlert.style.zIndex = '9999';
                successAlert.style.minWidth = '400px';
                successAlert.innerHTML = `
                    <i class="bi bi-check-circle-fill"></i>
                    <strong>批量更改完成！</strong> 成功更改 @Model.SuccessCount 个账户密码。
                    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                `;
                document.body.appendChild(successAlert);
                
                // 5秒后自动移除
                setTimeout(() => {
                    if (successAlert.parentElement) {
                        successAlert.remove();
                    }
                }, 5000);
                </text>
            }
        });
    </script>
}
