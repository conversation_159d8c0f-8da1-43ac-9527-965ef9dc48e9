using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Accounts
{
    public class EditModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Account Account { get; set; } = default!;

        public SelectList DeviceSelectList { get; set; } = default!;
        public SelectList EmployeeSelectList { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限：只有维护者及以上级别可以编辑账户
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (account == null)
            {
                return NotFound();
            }

            Account = account;
            LoadSelectLists();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                LoadSelectLists();
                return Page();
            }

            // 检查账户名是否已被其他账户使用
            var existingAccount = await _context.Accounts
                .FirstOrDefaultAsync(a => a.Username == Account.Username && a.Id != Account.Id);
            
            if (existingAccount != null)
            {
                ModelState.AddModelError("Account.Username", "账户名已存在，请使用其他账户名");
                LoadSelectLists();
                return Page();
            }

            // 获取原始账户信息
            var originalAccount = await _context.Accounts
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == Account.Id);

            if (originalAccount == null)
            {
                return NotFound();
            }

            // 更新时间
            Account.UpdatedAt = DateTime.Now;

            // 如果密码更改设置发生变化，更新下次密码更改时间
            if (Account.IsExemptFromPasswordChange != originalAccount.IsExemptFromPasswordChange)
            {
                if (Account.IsExemptFromPasswordChange)
                {
                    Account.NextPasswordChangeAt = null;
                }
                else
                {
                    Account.NextPasswordChangeAt = DateTime.Now.AddDays(90);
                }
            }

            _context.Attach(Account).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();

                // 记录操作日志
                var log = new OperationLog
                {
                    UserId = CurrentUserId,
                    Operation = "Update",
                    TargetType = "Account",
                    TargetId = Account.Id.ToString(),
                    Description = $"编辑账户：{Account.Username}",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                    UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    CreatedAt = DateTime.Now
                };
                _context.OperationLogs.Add(log);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AccountExists(Account.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return RedirectToPage("./Index");
        }

        private bool AccountExists(int id)
        {
            return _context.Accounts.Any(e => e.Id == id);
        }

        private void LoadSelectLists()
        {
            DeviceSelectList = new SelectList(_context.Devices
                .Where(d => d.Status == "Active")
                .OrderBy(d => d.DeviceCode), "Id", "DeviceCode", Account?.DeviceId);
                
            EmployeeSelectList = new SelectList(_context.Employees
                .Where(e => e.IsActive)
                .OrderBy(e => e.Name), "Id", "Name", Account?.EmployeeId);
        }
    }
}
