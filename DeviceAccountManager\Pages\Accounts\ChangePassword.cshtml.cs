using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages.Accounts
{
    public class ChangePasswordModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public ChangePasswordModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public int AccountId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "请输入新密码")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "密码长度必须在8-100个字符之间")]
        public string NewPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请确认密码")]
        [Compare("NewPassword", ErrorMessage = "两次输入的密码不一致")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请选择更改原因")]
        public string ChangeReason { get; set; } = "Manual change";

        public string Username { get; set; } = string.Empty;
        public DateTime? LastPasswordChange { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限：只有维护者及以上级别可以更改密码
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .FirstOrDefaultAsync(a => a.Id == id);

            if (account == null)
            {
                return NotFound();
            }

            AccountId = account.Id;
            Username = account.Username;
            LastPasswordChange = account.LastPasswordChangeAt;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                // 重新加载页面数据
                var accountForReload = await _context.Accounts
                    .FirstOrDefaultAsync(a => a.Id == AccountId);
                if (accountForReload != null)
                {
                    Username = accountForReload.Username;
                    LastPasswordChange = accountForReload.LastPasswordChangeAt;
                }
                return Page();
            }

            // 验证密码强度
            if (!IsPasswordStrong(NewPassword))
            {
                ModelState.AddModelError("NewPassword", "密码强度不足，请确保包含大小写字母、数字和特殊字符");
                var accountForReload = await _context.Accounts
                    .FirstOrDefaultAsync(a => a.Id == AccountId);
                if (accountForReload != null)
                {
                    Username = accountForReload.Username;
                    LastPasswordChange = accountForReload.LastPasswordChangeAt;
                }
                return Page();
            }

            var account = await _context.Accounts
                .FirstOrDefaultAsync(a => a.Id == AccountId);

            if (account == null)
            {
                return NotFound();
            }

            // 保存旧密码用于历史记录
            var oldPassword = account.Password;

            // 更新密码
            account.Password = BCrypt.Net.BCrypt.HashPassword(NewPassword);
            account.LastPasswordChangeAt = DateTime.Now;
            account.UpdatedAt = DateTime.Now;

            // 如果账户不免于密码更改，设置下次更改时间
            if (!account.IsExemptFromPasswordChange)
            {
                account.NextPasswordChangeAt = DateTime.Now.AddDays(90);
            }

            // 记录密码历史
            var passwordHistory = new PasswordHistory
            {
                AccountId = account.Id,
                OldPassword = oldPassword,
                NewPassword = account.Password,
                ChangeDate = DateTime.Now,
                ChangeReason = ChangeReason,
                ChangedBy = CurrentUserId.ToString()
            };
            _context.PasswordHistories.Add(passwordHistory);

            // 记录操作日志
            var log = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "ChangePassword",
                TargetType = "Account",
                TargetId = account.Id.ToString(),
                Description = $"更改账户密码：{account.Username}，原因：{ChangeReason}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(log);

            await _context.SaveChangesAsync();

            // 如果账户绑定了员工且有邮箱，发送密码更改通知邮件
            if (account.EmployeeId.HasValue)
            {
                var employee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.Id == account.EmployeeId);

                if (employee != null && !string.IsNullOrEmpty(employee.Email))
                {
                    var emailLog = new EmailLog
                    {
                        ToEmail = employee.Email,
                        ToName = employee.Name,
                        Subject = "账户密码已更改",
                        Content = $"您好 {employee.Name}，\n\n您的账户 {account.Username} 的密码已于 {DateTime.Now:yyyy-MM-dd HH:mm} 更改。\n\n如果这不是您本人操作，请立即联系系统管理员。\n\n此邮件由系统自动发送，请勿回复。",
                        Status = "Pending",
                        CreatedAt = DateTime.Now,
                        EmailType = "PasswordChange",
                        AccountId = account.Id
                    };
                    _context.EmailLogs.Add(emailLog);
                    await _context.SaveChangesAsync();
                }
            }

            return RedirectToPage("./Details", new { id = AccountId });
        }

        private bool IsPasswordStrong(string password)
        {
            if (string.IsNullOrEmpty(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }
    }
}
