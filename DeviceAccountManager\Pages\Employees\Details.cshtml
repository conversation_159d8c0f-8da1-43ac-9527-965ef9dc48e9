@page
@model DeviceAccountManager.Pages.Employees.DetailsModel
@{
    ViewData["Title"] = "员工详情";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">员工详情</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Index" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            @if (Model.IsMaintainer)
            {
                <a asp-page="./Edit" asp-route-id="@Model.Employee.Id" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-pencil"></i> 编辑
                </a>
            }
            @if (Model.IsTechnician)
            {
                <a asp-page="./Delete" asp-route-id="@Model.Employee.Id" class="btn btn-sm btn-outline-danger">
                    <i class="bi bi-trash"></i> 删除
                </a>
            }
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">基本信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">员工姓名：</dt>
                            <dd class="col-sm-8">
                                <strong>@Model.Employee.Name</strong>
                                @if (Model.Employee.IsActive)
                                {
                                    <span class="badge bg-success ms-2">在职</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger ms-2">离职</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-4">员工工号：</dt>
                            <dd class="col-sm-8"><strong>@Model.Employee.EmployeeCode</strong></dd>

                            <dt class="col-sm-4">所属部门：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Department))
                                {
                                    <span>@Model.Employee.Department</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">职位：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Position))
                                {
                                    <span>@Model.Employee.Position</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">邮箱地址：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Email))
                                {
                                    <a href="mailto:@Model.Employee.Email">@Model.Employee.Email</a>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">联系电话：</dt>
                            <dd class="col-sm-8">
                                @if (!string.IsNullOrEmpty(Model.Employee.Phone))
                                {
                                    <span>@Model.Employee.Phone</span>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">入职日期：</dt>
                            <dd class="col-sm-8">
                                @if (Model.Employee.HireDate.HasValue)
                                {
                                    @Model.Employee.HireDate.Value.ToString("yyyy-MM-dd")
                                    <br><small class="text-muted">入职 @((DateTime.Now - Model.Employee.HireDate.Value).Days) 天</small>
                                }
                                else
                                {
                                    <span class="text-muted">未设置</span>
                                }
                            </dd>

                            <dt class="col-sm-4">创建时间：</dt>
                            <dd class="col-sm-8">@Model.Employee.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Employee.Notes))
                {
                    <div class="mt-3">
                        <h6>备注信息：</h6>
                        <p class="text-muted">@Model.Employee.Notes</p>
                    </div>
                }
            </div>
        </div>

        <!-- 关联账户 -->
        @if (Model.RelatedAccounts.Any())
        {
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">关联账户 (@Model.RelatedAccounts.Count)</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>账户名</th>
                                    <th>权限级别</th>
                                    <th>关联设备</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var account in Model.RelatedAccounts)
                                {
                                    <tr>
                                        <td><strong>@account.Username</strong></td>
                                        <td>
                                            @switch (account.PermissionLevel)
                                            {
                                                case "SuperAdmin":
                                                    <span class="badge bg-danger">超级管理员</span>
                                                    break;
                                                case "Technician":
                                                    <span class="badge bg-warning">工艺员</span>
                                                    break;
                                                case "Maintainer":
                                                    <span class="badge bg-info">维护者</span>
                                                    break;
                                                case "Operator":
                                                    <span class="badge bg-secondary">操作者</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-light text-dark">@account.PermissionLevel</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (account.Device != null)
                                            {
                                                <span>@account.Device.DeviceCode</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">未关联</span>
                                            }
                                        </td>
                                        <td>
                                            @if (account.IsActive)
                                            {
                                                <span class="badge bg-success">启用</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">禁用</span>
                                            }
                                        </td>
                                        <td>
                                            <a asp-page="/Accounts/Details" asp-route-id="@account.Id" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i> 查看
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">快速操作</h6>
            </div>
            <div class="card-body">
                @if (Model.IsMaintainer)
                {
                    <div class="d-grid gap-2">
                        <a asp-page="./Edit" asp-route-id="@Model.Employee.Id" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> 编辑员工
                        </a>
                        @if (!string.IsNullOrEmpty(Model.Employee.Email))
                        {
                            <a href="mailto:@Model.Employee.Email" class="btn btn-success">
                                <i class="bi bi-envelope"></i> 发送邮件
                            </a>
                        }
                    </div>
                    <hr>
                }

                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> 统计信息</h6>
                    <p><strong>关联账户：</strong>@Model.RelatedAccounts.Count 个</p>
                    @if (Model.Employee.HireDate.HasValue)
                    {
                        <p><strong>工作天数：</strong>@((DateTime.Now - Model.Employee.HireDate.Value).Days) 天</p>
                    }
                    <p class="mb-0"><strong>员工状态：</strong>
                        @if (Model.Employee.IsActive)
                        {
                            <span class="badge bg-success">在职</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">离职</span>
                        }
                    </p>
                </div>

                @if (!string.IsNullOrEmpty(Model.Employee.Department))
                {
                    <div class="alert alert-success">
                        <h6><i class="bi bi-building"></i> 部门信息</h6>
                        <p><strong>所属部门：</strong>@Model.Employee.Department</p>
                        @if (!string.IsNullOrEmpty(Model.Employee.Position))
                        {
                            <p class="mb-0"><strong>职位：</strong>@Model.Employee.Position</p>
                        }
                    </div>
                }

                @if (!Model.Employee.IsActive)
                {
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> 注意</h6>
                        <p class="mb-0">此员工已离职，相关账户可能需要处理。</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
