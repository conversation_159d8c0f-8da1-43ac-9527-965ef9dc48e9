@page
@model DeviceAccountManager.Pages.SystemManagement.OptimizationModel
@{
    ViewData["Title"] = "系统优化";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-rocket me-2"></i>系统优化</h2>
                <button type="button" class="btn btn-primary" onclick="runFullOptimization()">
                    <i class="fas fa-magic me-1"></i>一键优化
                </button>
            </div>
        </div>
    </div>

    <!-- 优化建议 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>优化建议</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-database me-2"></i>数据库优化</h6>
                                <p class="mb-2">建议清理过期的审计日志和临时数据</p>
                                <button class="btn btn-sm btn-outline-info" onclick="optimizeDatabase()">
                                    <i class="fas fa-broom me-1"></i>清理数据库
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-memory me-2"></i>内存优化</h6>
                                <p class="mb-2">建议释放未使用的内存缓存</p>
                                <button class="btn btn-sm btn-outline-warning" onclick="optimizeMemory()">
                                    <i class="fas fa-sync me-1"></i>释放内存
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-file-archive me-2"></i>文件优化</h6>
                                <p class="mb-2">建议清理临时文件和日志文件</p>
                                <button class="btn btn-sm btn-outline-success" onclick="optimizeFiles()">
                                    <i class="fas fa-trash me-1"></i>清理文件
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-primary">
                                <h6><i class="fas fa-cog me-2"></i>配置优化</h6>
                                <p class="mb-2">建议检查和优化系统配置</p>
                                <button class="btn btn-sm btn-outline-primary" onclick="optimizeConfig()">
                                    <i class="fas fa-wrench me-1"></i>优化配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能分析 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>性能分析</h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>性能指标</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>响应时间</span>
                            <span class="text-success">@Model.ResponseTime ms</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 85%"></div>
                        </div>
                        <small class="text-muted">优秀</small>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>吞吐量</span>
                            <span class="text-info">@Model.Throughput req/s</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 70%"></div>
                        </div>
                        <small class="text-muted">良好</small>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>错误率</span>
                            <span class="text-warning">@Model.ErrorRate%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 15%"></div>
                        </div>
                        <small class="text-muted">正常</small>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>可用性</span>
                            <span class="text-success">@Model.Availability%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 99%"></div>
                        </div>
                        <small class="text-muted">优秀</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 优化历史 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>优化历史</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>优化类型</th>
                                    <th>描述</th>
                                    <th>效果</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="optimizationHistory">
                                @foreach (var history in Model.OptimizationHistory)
                                {
                                    <tr>
                                        <td>@history.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                        <td>@history.Action</td>
                                        <td>@history.Description</td>
                                        <td>
                                            <span class="badge bg-success">提升 15%</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">成功</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 优化进度模态框 -->
<div class="modal fade" id="optimizationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">系统优化进行中</h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">优化中...</span>
                    </div>
                    <p id="optimizationStatus">正在进行系统优化，请稍候...</p>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="optimizationProgress"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let performanceChart;

        $(document).ready(function() {
            initializePerformanceChart();
        });

        function initializePerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1小时前', '50分钟前', '40分钟前', '30分钟前', '20分钟前', '10分钟前', '现在'],
                    datasets: [{
                        label: '响应时间 (ms)',
                        data: [120, 110, 95, 85, 90, 75, 80],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }, {
                        label: 'CPU使用率 (%)',
                        data: [45, 50, 40, 35, 42, 30, 25],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }, {
                        label: '内存使用率 (%)',
                        data: [70, 68, 65, 62, 60, 58, 55],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function runFullOptimization() {
            showOptimizationModal();
            simulateOptimization([
                { step: '清理数据库', progress: 25 },
                { step: '释放内存', progress: 50 },
                { step: '清理文件', progress: 75 },
                { step: '优化配置', progress: 100 }
            ]);
        }

        function optimizeDatabase() {
            showOptimizationModal();
            simulateOptimization([
                { step: '清理过期日志', progress: 50 },
                { step: '优化数据库索引', progress: 100 }
            ]);
        }

        function optimizeMemory() {
            showOptimizationModal();
            simulateOptimization([
                { step: '释放缓存', progress: 100 }
            ]);
        }

        function optimizeFiles() {
            showOptimizationModal();
            simulateOptimization([
                { step: '清理临时文件', progress: 50 },
                { step: '压缩日志文件', progress: 100 }
            ]);
        }

        function optimizeConfig() {
            showOptimizationModal();
            simulateOptimization([
                { step: '检查配置', progress: 50 },
                { step: '应用优化设置', progress: 100 }
            ]);
        }

        function showOptimizationModal() {
            $('#optimizationModal').modal('show');
        }

        function simulateOptimization(steps) {
            let currentStep = 0;
            
            function executeStep() {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    $('#optimizationStatus').text(step.step + '...');
                    $('#optimizationProgress').css('width', step.progress + '%');
                    
                    currentStep++;
                    setTimeout(executeStep, 1500);
                } else {
                    $('#optimizationStatus').text('优化完成！');
                    setTimeout(() => {
                        $('#optimizationModal').modal('hide');
                        location.reload();
                    }, 1000);
                }
            }
            
            executeStep();
        }
    </script>
}
