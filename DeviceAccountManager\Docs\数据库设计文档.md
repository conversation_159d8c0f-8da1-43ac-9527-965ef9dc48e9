# 生产设备账号权限管理系统 - 数据库设计文档

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-01-01
- **最后更新**: 2025-01-01
- **数据库版本**: SQL Server Express 2022
- **字符集**: UTF-8

## 🗄️ 数据库概览

### 数据库基本信息
- **数据库名称**: DeviceAccountManager
- **数据库引擎**: SQL Server Express
- **排序规则**: Chinese_PRC_CI_AS
- **初始大小**: 100MB
- **增长方式**: 自动增长 10MB

### 表结构概览
系统共包含8个核心数据表：

| 表名 | 中文名称 | 记录数预估 | 主要用途 |
|------|----------|------------|----------|
| Users | 用户账户表 | 100-500 | 存储用户账户信息 |
| Employees | 员工信息表 | 100-500 | 存储员工详细信息 |
| PasswordHistories | 密码历史表 | 500-2500 | 追踪密码变更历史 |
| EmailLogs | 邮件日志表 | 1000-10000 | 记录邮件发送日志 |
| SystemConfigurations | 系统配置表 | 50-100 | 存储系统配置参数 |
| AuditLogs | 审计日志表 | 5000-50000 | 记录系统操作审计 |
| SystemBackups | 系统备份表 | 100-1000 | 记录备份操作历史 |
| UserPreferences | 用户偏好表 | 100-500 | 存储用户个性化设置 |

## 📊 详细表结构设计

### 1. Users (用户账户表)

**表用途**: 存储系统用户的基本账户信息和权限设置

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| Username | nvarchar | 50 | 否 | - | 用户名，唯一索引 |
| PasswordHash | nvarchar | 255 | 否 | - | BCrypt加密密码 |
| Email | nvarchar | 100 | 是 | - | 邮箱地址 |
| Role | nvarchar | 20 | 否 | 'Operator' | 用户角色 |
| IsActive | bit | - | 否 | 1 | 账户状态 |
| LastLoginAt | datetime2 | - | 是 | - | 最后登录时间 |
| PasswordChangedAt | datetime2 | - | 是 | - | 密码修改时间 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |
| UpdatedAt | datetime2 | - | 是 | - | 更新时间 |
| EmployeeId | int | - | 是 | - | 关联员工ID |

**索引设计**:
- PRIMARY KEY: Id
- UNIQUE INDEX: Username
- INDEX: Email, Role, IsActive
- FOREIGN KEY: EmployeeId → Employees(Id)

**约束条件**:
- Role 值限制: 'SuperAdmin', 'Technician', 'Maintainer', 'Operator'
- Username 长度 >= 3
- Email 格式验证

### 2. Employees (员工信息表)

**表用途**: 存储员工的详细个人信息和组织架构信息

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| EmployeeCode | nvarchar | 20 | 否 | - | 员工编号，唯一 |
| Name | nvarchar | 50 | 否 | - | 员工姓名 |
| Department | nvarchar | 50 | 是 | - | 所属部门 |
| Position | nvarchar | 50 | 是 | - | 职位 |
| Email | nvarchar | 100 | 是 | - | 邮箱地址 |
| Phone | nvarchar | 20 | 是 | - | 联系电话 |
| HireDate | datetime2 | - | 是 | - | 入职日期 |
| IsActive | bit | - | 否 | 1 | 员工状态 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |
| UpdatedAt | datetime2 | - | 是 | - | 更新时间 |

**索引设计**:
- PRIMARY KEY: Id
- UNIQUE INDEX: EmployeeCode
- INDEX: Name, Department, Email, IsActive

### 3. PasswordHistories (密码历史表)

**表用途**: 记录用户密码变更历史，防止重复使用旧密码

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| UserId | int | - | 否 | - | 用户ID |
| PasswordHash | nvarchar | 255 | 否 | - | 历史密码哈希 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |

**索引设计**:
- PRIMARY KEY: Id
- INDEX: UserId, CreatedAt
- FOREIGN KEY: UserId → Users(Id) ON DELETE CASCADE

### 4. EmailLogs (邮件日志表)

**表用途**: 记录系统发送的所有邮件信息和状态

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| ToEmail | nvarchar | 100 | 否 | - | 收件人邮箱 |
| Subject | nvarchar | 200 | 否 | - | 邮件主题 |
| Body | ntext | - | 是 | - | 邮件内容 |
| Status | nvarchar | 20 | 否 | 'Pending' | 发送状态 |
| ErrorMessage | nvarchar | 500 | 是 | - | 错误信息 |
| SentAt | datetime2 | - | 否 | GETDATE() | 发送时间 |
| RetryCount | int | - | 否 | 0 | 重试次数 |

**索引设计**:
- PRIMARY KEY: Id
- INDEX: ToEmail, Status, SentAt

**约束条件**:
- Status 值限制: 'Pending', 'Sent', 'Failed'

### 5. SystemConfigurations (系统配置表)

**表用途**: 存储系统的各种配置参数，支持动态配置

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| ConfigKey | nvarchar | 100 | 否 | - | 配置键名 |
| ConfigValue | nvarchar | 1000 | 是 | - | 配置值 |
| DataType | nvarchar | 20 | 否 | 'String' | 数据类型 |
| Category | nvarchar | 50 | 否 | 'General' | 配置分类 |
| Description | nvarchar | 200 | 是 | - | 配置说明 |
| IsReadOnly | bit | - | 否 | 0 | 是否只读 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |
| UpdatedAt | datetime2 | - | 是 | - | 更新时间 |
| CreatedByUserId | int | - | 是 | - | 创建用户ID |
| UpdatedByUserId | int | - | 是 | - | 更新用户ID |

**索引设计**:
- PRIMARY KEY: Id
- UNIQUE INDEX: ConfigKey
- INDEX: Category, DataType

**约束条件**:
- DataType 值限制: 'String', 'Integer', 'Boolean', 'Decimal', 'DateTime'

### 6. AuditLogs (审计日志表)

**表用途**: 记录系统中所有重要操作的审计信息

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| Action | nvarchar | 50 | 否 | - | 操作类型 |
| EntityType | nvarchar | 50 | 是 | - | 实体类型 |
| EntityId | int | - | 是 | - | 实体ID |
| EntityName | nvarchar | 100 | 是 | - | 实体名称 |
| OldValues | ntext | - | 是 | - | 修改前值 |
| NewValues | ntext | - | 是 | - | 修改后值 |
| Description | nvarchar | 500 | 是 | - | 操作描述 |
| UserId | int | - | 是 | - | 操作用户ID |
| IpAddress | nvarchar | 45 | 是 | - | IP地址 |
| UserAgent | nvarchar | 500 | 是 | - | 用户代理 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |

**索引设计**:
- PRIMARY KEY: Id
- INDEX: Action, EntityType, UserId, CreatedAt
- FOREIGN KEY: UserId → Users(Id)

### 7. SystemBackups (系统备份表)

**表用途**: 记录系统备份操作的历史和状态信息

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| BackupName | nvarchar | 200 | 否 | - | 备份名称 |
| Description | nvarchar | 500 | 是 | - | 备份描述 |
| FilePath | nvarchar | 500 | 否 | - | 备份文件路径 |
| FileSize | bigint | - | 否 | 0 | 文件大小(字节) |
| BackupType | nvarchar | 20 | 否 | 'Full' | 备份类型 |
| Status | nvarchar | 20 | 否 | 'Pending' | 备份状态 |
| ErrorMessage | nvarchar | 1000 | 是 | - | 错误信息 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |
| CompletedAt | datetime2 | - | 是 | - | 完成时间 |
| CreatedByUserId | int | - | 否 | - | 创建用户ID |

**索引设计**:
- PRIMARY KEY: Id
- INDEX: Status, BackupType, CreatedAt
- FOREIGN KEY: CreatedByUserId → Users(Id)

**约束条件**:
- BackupType 值限制: 'Full', 'Incremental', 'Differential'
- Status 值限制: 'Pending', 'InProgress', 'Completed', 'Failed'

### 8. UserPreferences (用户偏好表)

**表用途**: 存储用户的个性化设置和偏好配置

| 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|----------|------|--------|--------|------|
| Id | int | - | 否 | IDENTITY | 主键，自增ID |
| UserId | int | - | 否 | - | 用户ID |
| PreferenceKey | nvarchar | 100 | 否 | - | 偏好键名 |
| PreferenceValue | nvarchar | 1000 | 是 | - | 偏好值 |
| CreatedAt | datetime2 | - | 否 | GETDATE() | 创建时间 |
| UpdatedAt | datetime2 | - | 是 | - | 更新时间 |

**索引设计**:
- PRIMARY KEY: Id
- UNIQUE INDEX: UserId, PreferenceKey
- FOREIGN KEY: UserId → Users(Id) ON DELETE CASCADE

## 🔗 表关系设计

### 实体关系图 (ERD)
```
Users (1) ←→ (0..1) Employees
Users (1) ←→ (0..*) PasswordHistories
Users (1) ←→ (0..*) AuditLogs
Users (1) ←→ (0..*) SystemBackups
Users (1) ←→ (0..*) UserPreferences
```

### 外键约束
1. **Users.EmployeeId** → Employees.Id (可空，一对一)
2. **PasswordHistories.UserId** → Users.Id (级联删除)
3. **AuditLogs.UserId** → Users.Id (可空，保留历史)
4. **SystemBackups.CreatedByUserId** → Users.Id (不可空)
5. **UserPreferences.UserId** → Users.Id (级联删除)

## 📈 数据库性能优化

### 索引策略
1. **主键索引**: 所有表的Id字段自动创建聚集索引
2. **唯一索引**: Username, EmployeeCode等唯一字段
3. **复合索引**: 常用查询条件组合
4. **覆盖索引**: 包含查询所需的所有列

### 查询优化
1. **分页查询**: 使用OFFSET/FETCH进行分页
2. **条件过滤**: 在WHERE子句中使用索引字段
3. **连接优化**: 合理使用INNER/LEFT JOIN
4. **统计信息**: 定期更新表统计信息

### 存储优化
1. **数据类型**: 选择合适的数据类型和长度
2. **压缩**: 对历史数据表启用数据压缩
3. **分区**: 对大表考虑按时间分区
4. **归档**: 定期归档历史数据

## 🔒 数据安全设计

### 数据加密
1. **密码字段**: 使用BCrypt单向哈希
2. **敏感配置**: 系统配置中的敏感信息加密
3. **传输加密**: 数据库连接使用TLS
4. **备份加密**: 备份文件加密存储

### 访问控制
1. **数据库用户**: 创建专用数据库用户
2. **权限最小化**: 仅授予必要的数据库权限
3. **连接限制**: 限制数据库连接来源
4. **审计启用**: 启用数据库级别的审计

### 数据完整性
1. **主键约束**: 确保记录唯一性
2. **外键约束**: 维护引用完整性
3. **检查约束**: 验证数据有效性
4. **触发器**: 复杂业务规则验证

## 🔄 数据库维护

### 备份策略
1. **完整备份**: 每日凌晨执行完整备份
2. **差异备份**: 每4小时执行差异备份
3. **事务日志备份**: 每15分钟备份事务日志
4. **备份验证**: 定期验证备份文件完整性

### 维护任务
1. **索引重建**: 每周重建碎片化索引
2. **统计更新**: 每日更新表统计信息
3. **数据清理**: 定期清理过期日志数据
4. **性能监控**: 监控查询性能和资源使用

### 版本管理
1. **迁移脚本**: 使用Entity Framework Migrations
2. **版本控制**: 数据库架构版本控制
3. **回滚计划**: 准备数据库回滚方案
4. **测试验证**: 在测试环境验证迁移

---

**文档维护**: 本文档随数据库架构变更而更新，所有DDL变更需同步更新此文档。
