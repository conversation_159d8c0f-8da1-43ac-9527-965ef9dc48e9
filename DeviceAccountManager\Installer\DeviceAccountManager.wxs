<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  
  <!-- 产品定义 -->
  <Product Id="*" 
           Name="设备账户权限管理系统" 
           Language="2052" 
           Version="*******" 
           Manufacturer="您的公司名称" 
           UpgradeCode="12345678-1234-1234-1234-123456789012">
    
    <!-- 安装包信息 -->
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="设备账户权限管理系统安装包"
             Comments="生产设备账户权限管理解决方案" />

    <!-- 媒体定义 -->
    <MediaTemplate EmbedCab="yes" />

    <!-- 功能定义 -->
    <Feature Id="ProductFeature" Title="主程序" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentGroupRef Id="ServiceComponents" />
      <ComponentGroupRef Id="ShortcutComponents" />
    </Feature>

    <!-- 安装目录结构 -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFiles64Folder">
        <Directory Id="INSTALLFOLDER" Name="DeviceAccountManager">
          <Directory Id="LogsFolder" Name="logs" />
          <Directory Id="BackupFolder" Name="backup" />
          <Directory Id="ScriptsFolder" Name="scripts" />
        </Directory>
      </Directory>
      
      <!-- 开始菜单 -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="设备账户权限管理系统" />
      </Directory>
      
      <!-- 桌面 -->
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>

    <!-- 主程序组件 -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable" Guid="*">
        <File Id="DeviceAccountManagerDll" 
              Source="$(var.SourceDir)\DeviceAccountManager.dll" 
              KeyPath="yes" />
        <File Id="DeviceAccountManagerExe" 
              Source="$(var.SourceDir)\DeviceAccountManager.exe" />
        <File Id="AppSettingsJson" 
              Source="$(var.SourceDir)\appsettings.json" />
        <File Id="AppSettingsProdJson" 
              Source="$(var.SourceDir)\appsettings.Production.json" />
      </Component>
      
      <Component Id="Dependencies" Guid="*">
        <File Id="BCryptDll" Source="$(var.SourceDir)\BCrypt.Net-Next.dll" />
        <File Id="EntityFrameworkDll" Source="$(var.SourceDir)\Microsoft.EntityFrameworkCore.dll" />
        <File Id="SqlServerDll" Source="$(var.SourceDir)\Microsoft.EntityFrameworkCore.SqlServer.dll" />
      </Component>
      
      <Component Id="WebAssets" Guid="*">
        <File Id="WebConfigFile" Source="$(var.SourceDir)\web.config" />
        <!-- 添加更多Web资源文件 -->
      </Component>
    </ComponentGroup>

    <!-- Windows服务组件 -->
    <ComponentGroup Id="ServiceComponents" Directory="INSTALLFOLDER">
      <Component Id="WindowsService" Guid="*">
        <File Id="ServiceExecutable" 
              Source="$(var.SourceDir)\DeviceAccountManager.exe" 
              KeyPath="yes" />
        
        <!-- 安装Windows服务 -->
        <ServiceInstall Id="DeviceAccountManagerService"
                        Type="ownProcess"
                        Name="DeviceAccountManager"
                        DisplayName="设备账户权限管理系统"
                        Description="生产设备账户权限管理系统服务"
                        Start="auto"
                        Account="LocalSystem"
                        ErrorControl="normal"
                        Interactive="no" />
        
        <!-- 服务控制 -->
        <ServiceControl Id="StartService"
                        Start="install"
                        Stop="both"
                        Remove="uninstall"
                        Name="DeviceAccountManager"
                        Wait="yes" />
      </Component>
    </ComponentGroup>

    <!-- 快捷方式组件 -->
    <ComponentGroup Id="ShortcutComponents">
      <!-- 桌面快捷方式 -->
      <Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="*">
        <Shortcut Id="DesktopShortcut"
                  Name="设备账户权限管理系统"
                  Description="设备账户权限管理系统"
                  Target="http://localhost:5000"
                  Icon="AppIcon.ico" />
        <RemoveFolder Id="DesktopFolder" On="uninstall" />
        <RegistryValue Root="HKCU" 
                       Key="Software\DeviceAccountManager" 
                       Name="installed" 
                       Type="integer" 
                       Value="1" 
                       KeyPath="yes" />
      </Component>
      
      <!-- 开始菜单快捷方式 -->
      <Component Id="StartMenuShortcuts" Directory="ApplicationProgramsFolder" Guid="*">
        <Shortcut Id="StartMenuShortcut"
                  Name="设备账户权限管理系统"
                  Description="设备账户权限管理系统"
                  Target="http://localhost:5000"
                  Icon="AppIcon.ico" />
        
        <Shortcut Id="UninstallShortcut"
                  Name="卸载设备账户权限管理系统"
                  Description="卸载设备账户权限管理系统"
                  Target="[System64Folder]msiexec.exe"
                  Arguments="/x [ProductCode]" />
        
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
        <RegistryValue Root="HKCU" 
                       Key="Software\DeviceAccountManager" 
                       Name="startmenu" 
                       Type="integer" 
                       Value="1" 
                       KeyPath="yes" />
      </Component>
    </ComponentGroup>

    <!-- 图标定义 -->
    <Icon Id="AppIcon.ico" SourceFile="$(var.SourceDir)\icon.ico" />
    <Property Id="ARPPRODUCTICON" Value="AppIcon.ico" />

    <!-- 自定义操作 -->
    <CustomAction Id="OpenBrowser" 
                  ExeCommand="cmd.exe /c start http://localhost:5000" 
                  Directory="TARGETDIR" 
                  Execute="deferred" 
                  Return="ignore" />

    <CustomAction Id="ConfigureDatabase" 
                  ExeCommand="[INSTALLFOLDER]scripts\configure_database.bat" 
                  Directory="INSTALLFOLDER" 
                  Execute="deferred" 
                  Return="check" />

    <CustomAction Id="ConfigureFirewall" 
                  ExeCommand="netsh advfirewall firewall add rule name=&quot;DeviceAccountManager&quot; dir=in action=allow protocol=TCP localport=5000" 
                  Execute="deferred" 
                  Return="ignore" />

    <!-- 安装序列 -->
    <InstallExecuteSequence>
      <Custom Action="ConfigureDatabase" After="InstallFiles">NOT Installed</Custom>
      <Custom Action="ConfigureFirewall" After="ConfigureDatabase">NOT Installed</Custom>
      <Custom Action="OpenBrowser" After="InstallFinalize">NOT Installed</Custom>
    </InstallExecuteSequence>

    <!-- 升级逻辑 -->
    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的设备账户权限管理系统。" />

    <!-- 安装条件 -->
    <Condition Message="此应用程序需要Windows 10或更高版本。">
      <![CDATA[Installed OR (VersionNT >= 1000)]]>
    </Condition>

    <!-- 用户界面 -->
    <UIRef Id="WixUI_InstallDir" />
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    
    <!-- 许可协议 -->
    <WixVariable Id="WixUILicenseRtf" Value="$(var.SourceDir)\License.rtf" />
    
    <!-- 安装完成对话框 -->
    <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="启动设备账户权限管理系统" />
    <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1" />

  </Product>
</Wix>
