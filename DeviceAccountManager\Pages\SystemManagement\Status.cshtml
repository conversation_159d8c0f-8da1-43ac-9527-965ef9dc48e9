@page
@model DeviceAccountManager.Pages.SystemManagement.StatusModel
@{
    ViewData["Title"] = "系统状态";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-heartbeat me-2"></i>系统状态</h2>
                <div>
                    <span class="badge bg-success fs-6">系统正常运行</span>
                    <button type="button" class="btn btn-outline-primary ms-2" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt me-1"></i>刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统健康状态 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-server fa-3x text-success mb-3"></i>
                    <h5 class="card-title">应用服务</h5>
                    <span class="badge bg-success">正常</span>
                    <p class="card-text mt-2">运行时间: @Model.SystemUptime</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-database fa-3x text-success mb-3"></i>
                    <h5 class="card-title">数据库</h5>
                    <span class="badge bg-success">正常</span>
                    <p class="card-text mt-2">连接正常</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-envelope fa-3x text-success mb-3"></i>
                    <h5 class="card-title">邮件服务</h5>
                    <span class="badge bg-success">正常</span>
                    <p class="card-text mt-2">SMTP连接正常</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h5 class="card-title">安全服务</h5>
                    <span class="badge bg-success">正常</span>
                    <p class="card-text mt-2">认证正常</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统统计 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>系统统计</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-primary">@Model.TotalUsers</h3>
                                <p class="text-muted">总用户数</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success">@Model.ActiveUsers</h3>
                                <p class="text-muted">活跃用户</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-info">@Model.TotalEmployees</h3>
                                <p class="text-muted">员工总数</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-warning">@Model.TodayLogins</h3>
                                <p class="text-muted">今日登录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>系统任务</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>自动备份</span>
                        <span class="badge bg-success">已启用</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>日志清理</span>
                        <span class="badge bg-success">已启用</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>密码策略检查</span>
                        <span class="badge bg-success">已启用</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>邮件队列处理</span>
                        <span class="badge bg-success">运行中</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>系统监控</span>
                        <span class="badge bg-success">运行中</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近事件 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>最近系统事件</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @foreach (var evt in Model.RecentEvents)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">@evt.Action</h6>
                                    <p class="timeline-text">@evt.Description</p>
                                    <small class="text-muted">@evt.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>系统版本:</strong></td>
                            <td>v1.0.0</td>
                        </tr>
                        <tr>
                            <td><strong>.NET版本:</strong></td>
                            <td>10.0</td>
                        </tr>
                        <tr>
                            <td><strong>数据库:</strong></td>
                            <td>SQL Server Express</td>
                        </tr>
                        <tr>
                            <td><strong>启动时间:</strong></td>
                            <td>@Model.StartTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                        </tr>
                        <tr>
                            <td><strong>服务器时间:</strong></td>
                            <td id="serverTime">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="location.href='/SystemManagement/Monitor'">
                            <i class="fas fa-desktop me-2"></i>系统监控
                        </button>
                        <button class="btn btn-outline-success" onclick="location.href='/SystemManagement/Backup'">
                            <i class="fas fa-database me-2"></i>创建备份
                        </button>
                        <button class="btn btn-outline-info" onclick="location.href='/SystemManagement/Configuration'">
                            <i class="fas fa-cog me-2"></i>系统配置
                        </button>
                        <button class="btn btn-outline-warning" onclick="location.href='/SystemManagement/AuditLogs'">
                            <i class="fas fa-list-alt me-2"></i>审计日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #28a745;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}
</style>

@section Scripts {
    <script>
        function refreshStatus() {
            location.reload();
        }

        // 更新服务器时间
        setInterval(function() {
            const now = new Date();
            document.getElementById('serverTime').textContent = now.toLocaleString('zh-CN');
        }, 1000);
    </script>
}
