using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace DeviceAccountManager.Services
{
    public interface ISystemConfigurationService
    {
        Task<string> GetConfigValueAsync(string key, string defaultValue = "");
        Task<T> GetConfigValueAsync<T>(string key, T defaultValue = default!);
        Task<bool> SetConfigValueAsync(string key, string value, string description = "", string category = "General", int userId = 0);
        Task<bool> SetConfigValueAsync<T>(string key, T value, string description = "", string category = "General", int userId = 0);
        Task<List<SystemConfiguration>> GetConfigurationsByCategoryAsync(string category);
        Task<List<SystemConfiguration>> GetAllConfigurationsAsync();
        Task<bool> DeleteConfigurationAsync(string key, int userId);
        Task InitializeDefaultConfigurationsAsync();
    }

    public class SystemConfigurationService : ISystemConfigurationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SystemConfigurationService> _logger;

        public SystemConfigurationService(ApplicationDbContext context, ILogger<SystemConfigurationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<string> GetConfigValueAsync(string key, string defaultValue = "")
        {
            try
            {
                var config = await _context.SystemConfigurations
                    .FirstOrDefaultAsync(c => c.ConfigKey == key);

                return config?.ConfigValue ?? defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置值失败: {Key}", key);
                return defaultValue;
            }
        }

        public async Task<T> GetConfigValueAsync<T>(string key, T defaultValue = default!)
        {
            try
            {
                var stringValue = await GetConfigValueAsync(key);
                if (string.IsNullOrEmpty(stringValue))
                    return defaultValue;

                if (typeof(T) == typeof(string))
                    return (T)(object)stringValue;

                if (typeof(T) == typeof(int))
                    return int.TryParse(stringValue, out var intValue) ? (T)(object)intValue : defaultValue;

                if (typeof(T) == typeof(bool))
                    return bool.TryParse(stringValue, out var boolValue) ? (T)(object)boolValue : defaultValue;

                if (typeof(T) == typeof(decimal))
                    return decimal.TryParse(stringValue, out var decimalValue) ? (T)(object)decimalValue : defaultValue;

                if (typeof(T) == typeof(DateTime))
                    return DateTime.TryParse(stringValue, out var dateValue) ? (T)(object)dateValue : defaultValue;

                // 尝试JSON反序列化
                return JsonSerializer.Deserialize<T>(stringValue) ?? defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置值失败: {Key}", key);
                return defaultValue;
            }
        }

        public async Task<bool> SetConfigValueAsync(string key, string value, string description = "", string category = "General", int userId = 0)
        {
            try
            {
                var config = await _context.SystemConfigurations
                    .FirstOrDefaultAsync(c => c.ConfigKey == key);

                if (config == null)
                {
                    config = new SystemConfiguration
                    {
                        ConfigKey = key,
                        ConfigValue = value,
                        Description = description,
                        Category = category,
                        DataType = "String",
                        CreatedByUserId = userId,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                    _context.SystemConfigurations.Add(config);
                }
                else
                {
                    if (config.IsReadOnly)
                    {
                        _logger.LogWarning("尝试修改只读配置: {Key}", key);
                        return false;
                    }

                    config.ConfigValue = value;
                    config.UpdatedAt = DateTime.Now;
                    config.UpdatedByUserId = userId;
                    if (!string.IsNullOrEmpty(description))
                        config.Description = description;
                    if (!string.IsNullOrEmpty(category))
                        config.Category = category;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置配置值失败: {Key} = {Value}", key, value);
                return false;
            }
        }

        public async Task<bool> SetConfigValueAsync<T>(string key, T value, string description = "", string category = "General", int userId = 0)
        {
            try
            {
                string stringValue;
                string dataType;

                if (value is string strValue)
                {
                    stringValue = strValue;
                    dataType = "String";
                }
                else if (value is int or long or short)
                {
                    stringValue = value.ToString()!;
                    dataType = "Integer";
                }
                else if (value is bool)
                {
                    stringValue = value.ToString()!;
                    dataType = "Boolean";
                }
                else if (value is decimal or double or float)
                {
                    stringValue = value.ToString()!;
                    dataType = "Decimal";
                }
                else
                {
                    stringValue = JsonSerializer.Serialize(value);
                    dataType = "Json";
                }

                var config = await _context.SystemConfigurations
                    .FirstOrDefaultAsync(c => c.ConfigKey == key);

                if (config == null)
                {
                    config = new SystemConfiguration
                    {
                        ConfigKey = key,
                        ConfigValue = stringValue,
                        Description = description,
                        Category = category,
                        DataType = dataType,
                        CreatedByUserId = userId,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                    _context.SystemConfigurations.Add(config);
                }
                else
                {
                    if (config.IsReadOnly)
                    {
                        _logger.LogWarning("尝试修改只读配置: {Key}", key);
                        return false;
                    }

                    config.ConfigValue = stringValue;
                    config.DataType = dataType;
                    config.UpdatedAt = DateTime.Now;
                    config.UpdatedByUserId = userId;
                    if (!string.IsNullOrEmpty(description))
                        config.Description = description;
                    if (!string.IsNullOrEmpty(category))
                        config.Category = category;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置配置值失败: {Key}", key);
                return false;
            }
        }

        public async Task<List<SystemConfiguration>> GetConfigurationsByCategoryAsync(string category)
        {
            return await _context.SystemConfigurations
                .Where(c => c.Category == category)
                .OrderBy(c => c.ConfigKey)
                .ToListAsync();
        }

        public async Task<List<SystemConfiguration>> GetAllConfigurationsAsync()
        {
            return await _context.SystemConfigurations
                .Include(c => c.CreatedByUser)
                .Include(c => c.UpdatedByUser)
                .OrderBy(c => c.Category)
                .ThenBy(c => c.ConfigKey)
                .ToListAsync();
        }

        public async Task<bool> DeleteConfigurationAsync(string key, int userId)
        {
            try
            {
                var config = await _context.SystemConfigurations
                    .FirstOrDefaultAsync(c => c.ConfigKey == key);

                if (config == null)
                    return false;

                if (config.IsReadOnly)
                {
                    _logger.LogWarning("尝试删除只读配置: {Key}", key);
                    return false;
                }

                _context.SystemConfigurations.Remove(config);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除配置失败: {Key}", key);
                return false;
            }
        }

        public async Task InitializeDefaultConfigurationsAsync()
        {
            var defaultConfigs = new[]
            {
                new { Key = "System.Name", Value = "设备账户权限管理系统", Description = "系统名称", Category = "System", DataType = "String", IsReadOnly = true },
                new { Key = "System.Version", Value = "1.0.0", Description = "系统版本", Category = "System", DataType = "String", IsReadOnly = true },
                new { Key = "System.MaintenanceMode", Value = "false", Description = "维护模式", Category = "System", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Password.DefaultExpiryDays", Value = "90", Description = "默认密码过期天数", Category = "Password", DataType = "Integer", IsReadOnly = false },
                new { Key = "Password.MinLength", Value = "8", Description = "密码最小长度", Category = "Password", DataType = "Integer", IsReadOnly = false },
                new { Key = "Password.RequireUppercase", Value = "true", Description = "要求大写字母", Category = "Password", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Password.RequireLowercase", Value = "true", Description = "要求小写字母", Category = "Password", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Password.RequireDigits", Value = "true", Description = "要求数字", Category = "Password", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Password.RequireSpecialChars", Value = "true", Description = "要求特殊字符", Category = "Password", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Email.EnableNotifications", Value = "true", Description = "启用邮件通知", Category = "Email", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Email.SmtpHost", Value = "", Description = "SMTP服务器", Category = "Email", DataType = "String", IsReadOnly = false },
                new { Key = "Email.SmtpPort", Value = "587", Description = "SMTP端口", Category = "Email", DataType = "Integer", IsReadOnly = false },
                new { Key = "Email.EnableSsl", Value = "true", Description = "启用SSL", Category = "Email", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Backup.AutoBackupEnabled", Value = "true", Description = "启用自动备份", Category = "Backup", DataType = "Boolean", IsReadOnly = false },
                new { Key = "Backup.BackupRetentionDays", Value = "30", Description = "备份保留天数", Category = "Backup", DataType = "Integer", IsReadOnly = false },
                new { Key = "Security.SessionTimeoutMinutes", Value = "30", Description = "会话超时时间(分钟)", Category = "Security", DataType = "Integer", IsReadOnly = false },
                new { Key = "Security.MaxLoginAttempts", Value = "5", Description = "最大登录尝试次数", Category = "Security", DataType = "Integer", IsReadOnly = false }
            };

            foreach (var config in defaultConfigs)
            {
                var existing = await _context.SystemConfigurations
                    .FirstOrDefaultAsync(c => c.ConfigKey == config.Key);

                if (existing == null)
                {
                    _context.SystemConfigurations.Add(new SystemConfiguration
                    {
                        ConfigKey = config.Key,
                        ConfigValue = config.Value,
                        Description = config.Description,
                        Category = config.Category,
                        DataType = config.DataType,
                        IsReadOnly = config.IsReadOnly,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    });
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
