@echo off
echo ================================
echo   Building Professional Installer
echo ================================
echo.

:: 设置变量
set CURRENT_DIR=%~dp0
set PROJECT_ROOT=%CURRENT_DIR%..
set SOURCE_DIR=%PROJECT_ROOT%\publish
set OUTPUT_DIR=%CURRENT_DIR%output
set WIX_DIR=C:\Program Files (x86)\WiX Toolset v3.11\bin

:: 检查WiX工具集
if not exist "%WIX_DIR%\candle.exe" (
    echo Error: WiX Toolset not found
    echo Please install WiX Toolset from: https://wixtoolset.org/
    echo.
    echo Alternative: Using .NET tools
    goto :use_dotnet_tools
)

:: 检查源文件
if not exist "%SOURCE_DIR%" (
    echo Error: Source directory not found: %SOURCE_DIR%
    echo Please run 'dotnet publish' first
    pause
    exit /b 1
)

:: 创建输出目录
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

echo [1/5] Preparing source files...

:: 复制必要文件
if not exist "%SOURCE_DIR%\icon.ico" (
    echo Creating default icon...
    copy "%CURRENT_DIR%\default_icon.ico" "%SOURCE_DIR%\icon.ico" >nul 2>&1
)

if not exist "%SOURCE_DIR%\License.rtf" (
    echo Creating license file...
    echo {\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} > "%SOURCE_DIR%\License.rtf"
    echo \f0\fs24 设备账户权限管理系统软件许可协议\par >> "%SOURCE_DIR%\License.rtf"
    echo \par >> "%SOURCE_DIR%\License.rtf"
    echo 本软件按"现状"提供，不提供任何明示或暗示的保证。\par >> "%SOURCE_DIR%\License.rtf"
    echo } >> "%SOURCE_DIR%\License.rtf"
)

echo Source files prepared
echo.

echo [2/5] Compiling WiX source...
"%WIX_DIR%\candle.exe" -dSourceDir="%SOURCE_DIR%" -out "%OUTPUT_DIR%\DeviceAccountManager.wixobj" "%CURRENT_DIR%\DeviceAccountManager.wxs"

if %errorLevel% neq 0 (
    echo Error: WiX compilation failed
    pause
    exit /b 1
)

echo WiX source compiled successfully
echo.

echo [3/5] Linking installer...
"%WIX_DIR%\light.exe" -ext WixUIExtension -out "%OUTPUT_DIR%\DeviceAccountManager_Setup.msi" "%OUTPUT_DIR%\DeviceAccountManager.wixobj"

if %errorLevel% neq 0 (
    echo Error: WiX linking failed
    pause
    exit /b 1
)

echo Installer linked successfully
echo.

echo [4/5] Creating additional packages...

:: 创建自解压EXE版本
if exist "%WIX_DIR%\..\..\SDK\MakeSfxCA.exe" (
    echo Creating self-extracting executable...
    "%WIX_DIR%\..\..\SDK\MakeSfxCA.exe" "%OUTPUT_DIR%\DeviceAccountManager_Setup.exe" "%WIX_DIR%\SfxCA.dll" "%OUTPUT_DIR%\DeviceAccountManager_Setup.msi"
)

:: 创建便携版
echo Creating portable version...
set PORTABLE_DIR=%OUTPUT_DIR%\DeviceAccountManager_Portable
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

xcopy "%SOURCE_DIR%\*" "%PORTABLE_DIR%\" /E /I /Y >nul

:: 创建便携版启动脚本
echo @echo off > "%PORTABLE_DIR%\start.bat"
echo echo Starting Device Account Manager... >> "%PORTABLE_DIR%\start.bat"
echo start http://localhost:5000 >> "%PORTABLE_DIR%\start.bat"
echo dotnet DeviceAccountManager.dll >> "%PORTABLE_DIR%\start.bat"

:: 打包便携版
powershell -command "Compress-Archive -Path '%PORTABLE_DIR%\*' -DestinationPath '%OUTPUT_DIR%\DeviceAccountManager_Portable.zip' -Force"

echo Portable version created
echo.

echo [5/5] Generating checksums and info...

:: 生成文件信息
echo Device Account Manager Installation Packages > "%OUTPUT_DIR%\README.txt"
echo ============================================= >> "%OUTPUT_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\README.txt"
echo Generated on: %date% %time% >> "%OUTPUT_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\README.txt"
echo Files: >> "%OUTPUT_DIR%\README.txt"

for %%f in ("%OUTPUT_DIR%\*.msi" "%OUTPUT_DIR%\*.exe" "%OUTPUT_DIR%\*.zip") do (
    if exist "%%f" (
        echo   %%~nxf - %%~zf bytes >> "%OUTPUT_DIR%\README.txt"
    )
)

echo. >> "%OUTPUT_DIR%\README.txt"
echo Installation Instructions: >> "%OUTPUT_DIR%\README.txt"
echo 1. Run DeviceAccountManager_Setup.msi as Administrator >> "%OUTPUT_DIR%\README.txt"
echo 2. Follow the installation wizard >> "%OUTPUT_DIR%\README.txt"
echo 3. Access the system at http://localhost:5000 >> "%OUTPUT_DIR%\README.txt"
echo 4. Login with admin / Admin123! >> "%OUTPUT_DIR%\README.txt"

goto :success

:use_dotnet_tools
echo.
echo Using alternative method with .NET tools...
echo.

:: 检查是否安装了dotnet工具
dotnet tool list -g | findstr "wix" >nul
if %errorLevel% neq 0 (
    echo Installing WiX .NET tool...
    dotnet tool install --global wix
)

:: 使用.NET工具构建
echo Building with .NET WiX tools...
dotnet wix build "%CURRENT_DIR%\DeviceAccountManager.wxs" -d SourceDir="%SOURCE_DIR%" -o "%OUTPUT_DIR%\DeviceAccountManager_Setup.msi"

if %errorLevel% neq 0 (
    echo Error: .NET WiX build failed
    echo.
    echo Manual steps:
    echo 1. Install WiX Toolset: https://wixtoolset.org/
    echo 2. Or use the migration package: create_migration_package.bat
    pause
    exit /b 1
)

:success
echo.
echo ================================
echo Build completed successfully!
echo ================================
echo.
echo Output directory: %OUTPUT_DIR%
echo.

if exist "%OUTPUT_DIR%\DeviceAccountManager_Setup.msi" (
    echo ✅ MSI Installer: DeviceAccountManager_Setup.msi
    for %%f in ("%OUTPUT_DIR%\DeviceAccountManager_Setup.msi") do echo    Size: %%~zf bytes
)

if exist "%OUTPUT_DIR%\DeviceAccountManager_Setup.exe" (
    echo ✅ EXE Installer: DeviceAccountManager_Setup.exe
    for %%f in ("%OUTPUT_DIR%\DeviceAccountManager_Setup.exe") do echo    Size: %%~zf bytes
)

if exist "%OUTPUT_DIR%\DeviceAccountManager_Portable.zip" (
    echo ✅ Portable Version: DeviceAccountManager_Portable.zip
    for %%f in ("%OUTPUT_DIR%\DeviceAccountManager_Portable.zip") do echo    Size: %%~zf bytes
)

echo.
echo Installation packages are ready for distribution!
echo.
echo Next steps:
echo 1. Test the installer on a clean system
echo 2. Add digital signature (optional)
echo 3. Distribute to users
echo.
pause
