using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;
using System.ComponentModel.DataAnnotations;

namespace DeviceAccountManager.Pages.Accounts
{
    public class DeleteModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public DeleteModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Account Account { get; set; } = default!;

        [BindProperty]
        [Required(ErrorMessage = "请选择删除原因")]
        public string DeleteReason { get; set; } = string.Empty;

        [BindProperty]
        public string DeleteNotes { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "请确认删除操作")]
        public bool ConfirmDelete { get; set; }

        public int PasswordHistoryCount { get; set; }
        public int EmailLogCount { get; set; }
        public int OperationLogCount { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限：只有工艺员及以上级别可以删除账户
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var account = await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (account == null)
            {
                return NotFound();
            }

            Account = account;

            // 获取相关数据统计
            PasswordHistoryCount = await _context.PasswordHistories
                .CountAsync(ph => ph.AccountId == id);
            
            EmailLogCount = await _context.EmailLogs
                .CountAsync(el => el.AccountId == id);
            
            OperationLogCount = await _context.OperationLogs
                .CountAsync(ol => ol.TargetType == "Account" && ol.TargetId == id.ToString());

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            // 检查权限
            if (!IsTechnician)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            if (!ModelState.IsValid)
            {
                // 重新加载数据
                var accountForReload = await _context.Accounts
                    .Include(a => a.Device)
                    .Include(a => a.Employee)
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (accountForReload == null)
                {
                    return NotFound();
                }

                Account = accountForReload;
                
                PasswordHistoryCount = await _context.PasswordHistories
                    .CountAsync(ph => ph.AccountId == id);
                
                EmailLogCount = await _context.EmailLogs
                    .CountAsync(el => el.AccountId == id);
                
                OperationLogCount = await _context.OperationLogs
                    .CountAsync(ol => ol.TargetType == "Account" && ol.TargetId == id.ToString());

                return Page();
            }

            var account = await _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (account == null)
            {
                return NotFound();
            }

            // 防止删除最后一个超级管理员账户
            if (account.PermissionLevel == "SuperAdmin")
            {
                var superAdminCount = await _context.Accounts
                    .CountAsync(a => a.PermissionLevel == "SuperAdmin" && a.IsActive);
                
                if (superAdminCount <= 1)
                {
                    ModelState.AddModelError("", "不能删除最后一个超级管理员账户");
                    Account = account;
                    
                    PasswordHistoryCount = await _context.PasswordHistories
                        .CountAsync(ph => ph.AccountId == id);
                    
                    EmailLogCount = await _context.EmailLogs
                        .CountAsync(el => el.AccountId == id);
                    
                    OperationLogCount = await _context.OperationLogs
                        .CountAsync(ol => ol.TargetType == "Account" && ol.TargetId == id.ToString());

                    return Page();
                }
            }

            // 记录删除操作日志
            var deleteLog = new OperationLog
            {
                UserId = CurrentUserId,
                Operation = "Delete",
                TargetType = "Account",
                TargetId = account.Id.ToString(),
                Description = $"删除账户：{account.Username}，原因：{DeleteReason}，备注：{DeleteNotes}",
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                CreatedAt = DateTime.Now
            };
            _context.OperationLogs.Add(deleteLog);

            // 删除相关的密码历史记录
            var passwordHistories = await _context.PasswordHistories
                .Where(ph => ph.AccountId == account.Id)
                .ToListAsync();
            _context.PasswordHistories.RemoveRange(passwordHistories);

            // 删除相关的邮件日志
            var emailLogs = await _context.EmailLogs
                .Where(el => el.AccountId == account.Id)
                .ToListAsync();
            _context.EmailLogs.RemoveRange(emailLogs);

            // 删除账户
            _context.Accounts.Remove(account);

            await _context.SaveChangesAsync();

            // 如果账户绑定了员工且有邮箱，发送账户删除通知邮件
            if (account.Employee != null && !string.IsNullOrEmpty(account.Employee.Email))
            {
                var emailLog = new EmailLog
                {
                    ToEmail = account.Employee.Email,
                    ToName = account.Employee.Name,
                    Subject = "账户已被删除",
                    Content = $"您好 {account.Employee.Name}，\n\n您的账户 {account.Username} 已于 {DateTime.Now:yyyy-MM-dd HH:mm} 被删除。\n\n删除原因：{DeleteReason}\n\n如有疑问，请联系系统管理员。\n\n此邮件由系统自动发送，请勿回复。",
                    Status = "Pending",
                    CreatedAt = DateTime.Now,
                    EmailType = "AccountDeleted"
                };
                _context.EmailLogs.Add(emailLog);
                await _context.SaveChangesAsync();
            }

            return RedirectToPage("./Index");
        }
    }
}
