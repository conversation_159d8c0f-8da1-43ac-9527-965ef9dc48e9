@echo off
echo ========================================
echo 生产设备账号权限管理系统 - 健康检查
echo ========================================
echo.

:: 设置变量
set APP_NAME=DeviceAccountManager
set APP_URL=http://localhost:5000
set SERVICE_NAME=DeviceAccountManager
set LOG_FILE=health-check-%date:~0,4%%date:~5,2%%date:~8,2%.log

echo 开始系统健康检查...
echo 检查时间: %date% %time%
echo.

:: 创建日志文件
echo ======================================== > %LOG_FILE%
echo 系统健康检查报告 >> %LOG_FILE%
echo 检查时间: %date% %time% >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%
echo. >> %LOG_FILE%

:: 1. 检查服务状态
echo [1/8] 检查 Windows 服务状态...
sc query %SERVICE_NAME% | find "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✓ 服务运行正常
    echo [✓] Windows 服务: 运行正常 >> %LOG_FILE%
) else (
    echo ✗ 服务未运行或不存在
    echo [✗] Windows 服务: 未运行或不存在 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 2. 检查端口监听
echo [2/8] 检查端口监听状态...
netstat -an | find ":5000" | find "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✓ 端口 5000 正在监听
    echo [✓] 端口监听: 5000 端口正常 >> %LOG_FILE%
) else (
    echo ✗ 端口 5000 未监听
    echo [✗] 端口监听: 5000 端口未监听 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 3. 检查 HTTP 响应
echo [3/8] 检查 HTTP 响应...
curl -s -o nul -w "%%{http_code}" %APP_URL% | find "200" >nul
if %errorLevel% equ 0 (
    echo ✓ HTTP 响应正常
    echo [✓] HTTP 响应: 200 OK >> %LOG_FILE%
) else (
    echo ✗ HTTP 响应异常
    echo [✗] HTTP 响应: 异常 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 4. 检查数据库连接
echo [4/8] 检查数据库连接...
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT 1" -d DeviceAccountManager >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 数据库连接正常
    echo [✓] 数据库连接: 正常 >> %LOG_FILE%
) else (
    echo ✗ 数据库连接失败
    echo [✗] 数据库连接: 失败 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 5. 检查系统 API
echo [5/8] 检查系统 API...
curl -s %APP_URL%/api/system/health | find "Healthy" >nul
if %errorLevel% equ 0 (
    echo ✓ 系统 API 正常
    echo [✓] 系统 API: 正常 >> %LOG_FILE%
) else (
    echo ✗ 系统 API 异常
    echo [✗] 系统 API: 异常 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 6. 检查磁盘空间
echo [6/8] 检查磁盘空间...
for /f "tokens=3" %%a in ('dir C:\ ^| find "可用字节"') do set FREE_SPACE=%%a
set FREE_SPACE=%FREE_SPACE:,=%
if %FREE_SPACE% GTR ********** (
    echo ✓ 磁盘空间充足 ^(%FREE_SPACE% 字节可用^)
    echo [✓] 磁盘空间: 充足 ^(%FREE_SPACE% 字节^) >> %LOG_FILE%
) else (
    echo ✗ 磁盘空间不足 ^(%FREE_SPACE% 字节可用^)
    echo [✗] 磁盘空间: 不足 ^(%FREE_SPACE% 字节^) >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 7. 检查内存使用
echo [7/8] 检查内存使用...
for /f "skip=1 tokens=4" %%a in ('wmic OS get TotalVisibleMemorySize /value') do set TOTAL_MEM=%%a
for /f "skip=1 tokens=4" %%a in ('wmic OS get FreePhysicalMemory /value') do set FREE_MEM=%%a
set /a MEM_USAGE=100-(%FREE_MEM%*100/%TOTAL_MEM%)
if %MEM_USAGE% LSS 80 (
    echo ✓ 内存使用正常 ^(%MEM_USAGE%%%^)
    echo [✓] 内存使用: 正常 ^(%MEM_USAGE%%%^) >> %LOG_FILE%
) else (
    echo ✗ 内存使用过高 ^(%MEM_USAGE%%%^)
    echo [✗] 内存使用: 过高 ^(%MEM_USAGE%%%^) >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 8. 检查日志文件
echo [8/8] 检查日志文件...
if exist "logs\app*.log" (
    echo ✓ 日志文件存在
    echo [✓] 日志文件: 存在 >> %LOG_FILE%
    
    :: 检查最近的错误日志
    findstr /i "error\|exception\|fail" logs\app*.log | tail -5 > temp_errors.txt 2>nul
    if exist temp_errors.txt (
        for /f %%a in ('type temp_errors.txt ^| find /c /v ""') do set ERROR_COUNT=%%a
        if %ERROR_COUNT% GTR 0 (
            echo ⚠ 发现 %ERROR_COUNT% 个最近错误
            echo [⚠] 最近错误: %ERROR_COUNT% 个 >> %LOG_FILE%
        )
        del temp_errors.txt
    )
) else (
    echo ✗ 日志文件不存在
    echo [✗] 日志文件: 不存在 >> %LOG_FILE%
    set HEALTH_ISSUES=1
)
echo.

:: 生成详细系统信息
echo ======================================== >> %LOG_FILE%
echo 详细系统信息 >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%
echo 操作系统: >> %LOG_FILE%
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" >> %LOG_FILE%
echo. >> %LOG_FILE%
echo CPU 信息: >> %LOG_FILE%
wmic cpu get name /value | findstr "Name=" >> %LOG_FILE%
echo. >> %LOG_FILE%
echo 内存信息: >> %LOG_FILE%
echo 总内存: %TOTAL_MEM% KB >> %LOG_FILE%
echo 可用内存: %FREE_MEM% KB >> %LOG_FILE%
echo 使用率: %MEM_USAGE%%% >> %LOG_FILE%
echo. >> %LOG_FILE%
echo 网络连接: >> %LOG_FILE%
netstat -an | findstr ":5000" >> %LOG_FILE%
echo. >> %LOG_FILE%

:: 生成建议
echo ======================================== >> %LOG_FILE%
echo 维护建议 >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%
if defined HEALTH_ISSUES (
    echo - 发现系统问题，请检查上述失败项目 >> %LOG_FILE%
    echo - 查看应用程序日志获取详细错误信息 >> %LOG_FILE%
    echo - 如需要，重启相关服务 >> %LOG_FILE%
) else (
    echo - 系统运行正常 >> %LOG_FILE%
    echo - 建议定期执行健康检查 >> %LOG_FILE%
    echo - 保持系统和数据库的定期备份 >> %LOG_FILE%
)
echo - 监控磁盘空间，及时清理日志文件 >> %LOG_FILE%
echo - 定期更新系统补丁和安全设置 >> %LOG_FILE%
echo. >> %LOG_FILE%

:: 总结报告
echo ========================================
echo 健康检查完成
echo ========================================
if defined HEALTH_ISSUES (
    echo 状态: ⚠ 发现问题
    echo 建议: 请检查失败的项目并采取相应措施
    echo [总结] 状态: 发现问题 >> %LOG_FILE%
) else (
    echo 状态: ✓ 系统健康
    echo 建议: 系统运行正常，继续监控
    echo [总结] 状态: 系统健康 >> %LOG_FILE%
)
echo 报告文件: %LOG_FILE%
echo 检查时间: %date% %time%
echo ========================================
echo.

:: 可选：发送健康检查结果到系统
if not defined HEALTH_ISSUES (
    curl -s -X POST "%APP_URL%/api/system/log" ^
      -H "Content-Type: application/json" ^
      -d "{\"action\":\"HealthCheck\",\"description\":\"系统健康检查通过\",\"level\":\"Info\"}" >nul 2>&1
) else (
    curl -s -X POST "%APP_URL%/api/system/log" ^
      -H "Content-Type: application/json" ^
      -d "{\"action\":\"HealthCheck\",\"description\":\"系统健康检查发现问题\",\"level\":\"Warning\"}" >nul 2>&1
)

pause
