using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using Microsoft.EntityFrameworkCore;
using System.Net;
using System.Net.Mail;

namespace DeviceAccountManager.Services
{
    public interface IEmailService
    {
        Task<bool> SendEmailAsync(string toEmail, string toName, string subject, string content, string emailType = "General");
        Task<bool> SendPasswordChangeNotificationAsync(Account account, string newPassword, string changeReason);
        Task<bool> SendPasswordExpiryWarningAsync(Account account, int daysUntilExpiry);
        Task<bool> SendAccountCreatedNotificationAsync(Account account, string initialPassword);
        Task<bool> SendAccountDisabledNotificationAsync(Account account, string reason);
        Task ProcessPendingEmailsAsync();
        Task<List<EmailLog>> GetEmailLogsAsync(int pageSize = 50, int pageNumber = 1);
        Task<EmailLog?> GetEmailLogAsync(int id);
        Task<bool> ResendEmailAsync(int emailLogId);
    }

    public class EmailService : IEmailService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;

        public EmailService(ApplicationDbContext context, IConfiguration configuration, ILogger<EmailService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<bool> SendEmailAsync(string toEmail, string toName, string subject, string content, string emailType = "General")
        {
            try
            {
                // 记录邮件日志
                var emailLog = new EmailLog
                {
                    ToEmail = toEmail,
                    ToName = toName,
                    Subject = subject,
                    Content = content,
                    Status = "Pending",
                    CreatedAt = DateTime.Now,
                    EmailType = emailType
                };

                _context.EmailLogs.Add(emailLog);
                await _context.SaveChangesAsync();

                // 实际发送邮件
                return await SendEmailInternal(emailLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送邮件失败: {ToEmail}, {Subject}", toEmail, subject);
                return false;
            }
        }

        public async Task<bool> SendPasswordChangeNotificationAsync(Account account, string newPassword, string changeReason)
        {
            if (account.Employee == null || string.IsNullOrEmpty(account.Employee.Email))
                return false;

            var subject = "账户密码已更改 - 设备账户管理系统";
            var content = $@"
尊敬的 {account.Employee.Name}，

您好！您的设备账户密码已被更改，详情如下：

账户信息：
• 账户名：{account.Username}
• 设备编号：{account.Device?.DeviceCode ?? "未关联"}
• 权限级别：{GetPermissionLevelName(account.PermissionLevel)}
• 更改时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}

新密码：{newPassword}

更改原因：{changeReason}

安全提醒：
1. 请妥善保管您的新密码
2. 建议您首次登录后立即修改为个人密码
3. 请勿与他人分享您的账户信息
4. 如有疑问，请联系系统管理员

此邮件由系统自动发送，请勿回复。

设备账户管理系统
{DateTime.Now:yyyy-MM-dd}
";

            return await SendEmailAsync(account.Employee.Email, account.Employee.Name, subject, content, "PasswordChanged");
        }

        public async Task<bool> SendPasswordExpiryWarningAsync(Account account, int daysUntilExpiry)
        {
            if (account.Employee == null || string.IsNullOrEmpty(account.Employee.Email))
                return false;

            var subject = $"密码即将到期提醒 - 还有{daysUntilExpiry}天";
            var content = $@"
尊敬的 {account.Employee.Name}，

您好！您的设备账户密码即将到期，请及时更改：

账户信息：
• 账户名：{account.Username}
• 设备编号：{account.Device?.DeviceCode ?? "未关联"}
• 权限级别：{GetPermissionLevelName(account.PermissionLevel)}
• 到期日期：{account.NextPasswordChangeAt:yyyy-MM-dd}
• 剩余天数：{daysUntilExpiry} 天

操作建议：
1. 请尽快登录系统更改密码
2. 新密码应符合安全策略要求
3. 避免使用与历史密码相同的密码
4. 如需帮助，请联系系统管理员

登录地址：http://localhost:5046

此邮件由系统自动发送，请勿回复。

设备账户管理系统
{DateTime.Now:yyyy-MM-dd}
";

            return await SendEmailAsync(account.Employee.Email, account.Employee.Name, subject, content, "PasswordExpiring");
        }

        public async Task<bool> SendAccountCreatedNotificationAsync(Account account, string initialPassword)
        {
            if (account.Employee == null || string.IsNullOrEmpty(account.Employee.Email))
                return false;

            var subject = "新账户创建通知 - 设备账户管理系统";
            var content = $@"
尊敬的 {account.Employee.Name}，

您好！系统已为您创建新的设备账户：

账户信息：
• 账户名：{account.Username}
• 设备编号：{account.Device?.DeviceCode ?? "未关联"}
• 权限级别：{GetPermissionLevelName(account.PermissionLevel)}
• 创建时间：{account.CreatedAt:yyyy-MM-dd HH:mm:ss}

初始密码：{initialPassword}

重要提醒：
1. 请妥善保管您的账户信息
2. 首次登录后请立即修改密码
3. 请勿与他人分享您的账户信息
4. 定期更改密码以确保安全

登录地址：http://localhost:5046

如有任何问题，请联系系统管理员。

此邮件由系统自动发送，请勿回复。

设备账户管理系统
{DateTime.Now:yyyy-MM-dd}
";

            return await SendEmailAsync(account.Employee.Email, account.Employee.Name, subject, content, "AccountCreated");
        }

        public async Task<bool> SendAccountDisabledNotificationAsync(Account account, string reason)
        {
            if (account.Employee == null || string.IsNullOrEmpty(account.Employee.Email))
                return false;

            var subject = "账户已被禁用通知 - 设备账户管理系统";
            var content = $@"
尊敬的 {account.Employee.Name}，

您好！您的设备账户已被禁用：

账户信息：
• 账户名：{account.Username}
• 设备编号：{account.Device?.DeviceCode ?? "未关联"}
• 权限级别：{GetPermissionLevelName(account.PermissionLevel)}
• 禁用时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}

禁用原因：{reason}

如果您认为这是错误操作，或需要重新启用账户，请联系系统管理员。

此邮件由系统自动发送，请勿回复。

设备账户管理系统
{DateTime.Now:yyyy-MM-dd}
";

            return await SendEmailAsync(account.Employee.Email, account.Employee.Name, subject, content, "AccountDisabled");
        }

        public async Task ProcessPendingEmailsAsync()
        {
            var pendingEmails = await _context.EmailLogs
                .Where(e => e.Status == "Pending")
                .OrderBy(e => e.CreatedAt)
                .Take(10) // 每次处理10封邮件
                .ToListAsync();

            foreach (var email in pendingEmails)
            {
                await SendEmailInternal(email);
                await Task.Delay(1000); // 延迟1秒避免发送过快
            }
        }

        public async Task<List<EmailLog>> GetEmailLogsAsync(int pageSize = 50, int pageNumber = 1)
        {
            return await _context.EmailLogs
                .Include(e => e.Account)
                .ThenInclude(a => a.Employee)
                .OrderByDescending(e => e.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<EmailLog?> GetEmailLogAsync(int id)
        {
            return await _context.EmailLogs
                .Include(e => e.Account)
                .ThenInclude(a => a.Employee)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<bool> ResendEmailAsync(int emailLogId)
        {
            var emailLog = await _context.EmailLogs.FindAsync(emailLogId);
            if (emailLog == null)
                return false;

            emailLog.Status = "Pending";
            emailLog.SentAt = null;
            emailLog.ErrorMessage = null;
            await _context.SaveChangesAsync();

            return await SendEmailInternal(emailLog);
        }

        private async Task<bool> SendEmailInternal(EmailLog emailLog)
        {
            try
            {
                // 获取SMTP配置
                var smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com";
                var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
                var smtpUsername = _configuration["Email:Username"] ?? "";
                var smtpPassword = _configuration["Email:Password"] ?? "";
                var fromEmail = _configuration["Email:FromEmail"] ?? smtpUsername;
                var fromName = _configuration["Email:FromName"] ?? "设备账户管理系统";

                // 如果没有配置SMTP，模拟发送成功
                if (string.IsNullOrEmpty(smtpUsername))
                {
                    _logger.LogInformation("模拟发送邮件: {ToEmail}, {Subject}", emailLog.ToEmail, emailLog.Subject);
                    emailLog.Status = "Sent";
                    emailLog.SentAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }

                using var client = new SmtpClient(smtpHost, smtpPort);
                client.EnableSsl = true;
                client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);

                var message = new MailMessage
                {
                    From = new MailAddress(fromEmail, fromName),
                    Subject = emailLog.Subject,
                    Body = emailLog.Content,
                    IsBodyHtml = false
                };

                message.To.Add(new MailAddress(emailLog.ToEmail, emailLog.ToName));

                await client.SendMailAsync(message);

                emailLog.Status = "Sent";
                emailLog.SentAt = DateTime.Now;
                await _context.SaveChangesAsync();

                _logger.LogInformation("邮件发送成功: {ToEmail}, {Subject}", emailLog.ToEmail, emailLog.Subject);
                return true;
            }
            catch (Exception ex)
            {
                emailLog.Status = "Failed";
                emailLog.ErrorMessage = ex.Message;
                await _context.SaveChangesAsync();

                _logger.LogError(ex, "邮件发送失败: {ToEmail}, {Subject}", emailLog.ToEmail, emailLog.Subject);
                return false;
            }
        }

        private static string GetPermissionLevelName(string permissionLevel)
        {
            return permissionLevel switch
            {
                "SuperAdmin" => "超级管理员",
                "Technician" => "工艺员",
                "Maintainer" => "维护者",
                "Operator" => "操作者",
                _ => permissionLevel
            };
        }
    }
}
