﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 设备账号权限管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <style>
        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: calc(100vh - 56px);
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-shield-lock"></i>
                    设备账号权限管理系统
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            @(Context.Session.GetString("RealName") ?? "用户")
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> 系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" asp-page="/Logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Index">
                                <i class="bi bi-house-door"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Devices/Index">
                                <i class="bi bi-cpu"></i>
                                设备管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Accounts/Index">
                                <i class="bi bi-person-badge"></i>
                                账户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Employees/Index">
                                <i class="bi bi-people"></i>
                                员工管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/PasswordManagement/Index">
                                <i class="bi bi-key"></i>
                                密码管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/EmailManagement/Index">
                                <i class="bi bi-envelope"></i>
                                邮件管理
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="systemDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear"></i>
                                系统管理
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="systemDropdown">
                                <li><a class="dropdown-item" href="/SystemManagement"><i class="bi bi-speedometer2 me-2"></i>系统概览</a></li>
                                <li><a class="dropdown-item" href="/SystemManagement/Status"><i class="bi bi-heart-pulse me-2"></i>系统状态</a></li>
                                <li><a class="dropdown-item" href="/SystemManagement/Monitor"><i class="bi bi-display me-2"></i>系统监控</a></li>
                                <li><a class="dropdown-item" href="/SystemManagement/Optimization"><i class="bi bi-rocket me-2"></i>系统优化</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/SystemManagement/Configuration"><i class="bi bi-gear-fill me-2"></i>系统配置</a></li>
                                <li><a class="dropdown-item" href="/SystemManagement/AuditLogs"><i class="bi bi-list-check me-2"></i>审计日志</a></li>
                                <li><a class="dropdown-item" href="/SystemManagement/Backup"><i class="bi bi-hdd me-2"></i>备份管理</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Logs/Index">
                                <i class="bi bi-journal-text"></i>
                                操作日志
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    @RenderBody()
                </div>
            </main>
        </div>
    </div>

    <footer class="border-top footer text-muted mt-auto">
        <div class="container-fluid">
            <div class="text-center py-3">
                &copy; 2025 - 设备账号权限管理系统 - 版权所有
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
