using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Devices
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Device> Devices { get; set; } = default!;
        
        [BindProperty(SupportsGet = true)]
        public string? SearchString { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        public int ActiveDevicesCount { get; set; }
        public int MaintenanceDevicesCount { get; set; }
        public int OfflineDevicesCount { get; set; }
        public int TotalAccountsCount { get; set; }

        public async Task OnGetAsync()
        {
            var devicesQuery = _context.Devices.Include(d => d.Accounts).AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(SearchString))
            {
                devicesQuery = devicesQuery.Where(d => 
                    d.DeviceCode.Contains(SearchString) || 
                    d.DeviceName.Contains(SearchString) ||
                    d.ProductionLine.Contains(SearchString) ||
                    d.Location.Contains(SearchString));
            }

            // 状态过滤
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                devicesQuery = devicesQuery.Where(d => d.Status == StatusFilter);
            }

            Devices = await devicesQuery
                .OrderBy(d => d.DeviceCode)
                .ToListAsync();

            // 统计数据
            ActiveDevicesCount = await _context.Devices.CountAsync(d => d.Status == "Active");
            MaintenanceDevicesCount = await _context.Devices.CountAsync(d => d.Status == "Maintenance");
            OfflineDevicesCount = await _context.Devices.CountAsync(d => d.Status == "Offline");
            TotalAccountsCount = await _context.Accounts.CountAsync(a => a.IsActive);
        }
    }
}
