@page
@model DeviceAccountManager.Pages.Accounts.IndexModel
@{
    ViewData["Title"] = "账户管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">账户管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a asp-page="./Create" class="btn btn-sm btn-primary">
                <i class="bi bi-plus-circle"></i> 添加账户
            </a>
            <a asp-page="./BatchUpdate" class="btn btn-sm btn-warning">
                <i class="bi bi-arrow-repeat"></i> 批量更新密码
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-3">
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <input type="text" class="form-control" name="searchString" value="@Model.SearchString" placeholder="搜索账户名或设备...">
                <button class="btn btn-outline-secondary" type="submit">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <select class="form-select" name="deviceFilter" onchange="this.form.submit()">
                    <option value="">所有设备</option>
                    @foreach (var device in Model.Devices)
                    {
                        <option value="@device.Id" selected="@(Model.DeviceFilter == device.Id.ToString())">@device.DeviceCode - @device.DeviceName</option>
                    }
                </select>
                <input type="hidden" name="searchString" value="@Model.SearchString" />
            </div>
        </form>
    </div>
    <div class="col-md-4">
        <form method="get">
            <div class="input-group">
                <select class="form-select" name="statusFilter" onchange="this.form.submit()">
                    <option value="">所有状态</option>
                    <option value="true" selected="@(Model.StatusFilter == "true")">活跃</option>
                    <option value="false" selected="@(Model.StatusFilter == "false")">禁用</option>
                </select>
                <input type="hidden" name="searchString" value="@Model.SearchString" />
                <input type="hidden" name="deviceFilter" value="@Model.DeviceFilter" />
            </div>
        </form>
    </div>
</div>

<!-- 账户列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">账户列表</h6>
    </div>
    <div class="card-body">
        @if (Model.Accounts.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>账户名</th>
                            <th>关联设备</th>
                            <th>绑定员工</th>
                            <th>权限级别</th>
                            <th>状态</th>
                            <th>下次密码更新</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var account in Model.Accounts)
                        {
                            <tr>
                                <td>
                                    <strong>@account.Username</strong>
                                    @if (!account.IsActive)
                                    {
                                        <span class="badge bg-secondary ms-1">禁用</span>
                                    }
                                </td>
                                <td>
                                    @if (account.Device != null)
                                    {
                                        <span class="badge bg-info">@account.Device.DeviceCode</span>
                                        <br><small class="text-muted">@account.Device.DeviceName</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未关联</span>
                                    }
                                </td>
                                <td>
                                    @if (account.Employee != null)
                                    {
                                        <span>@account.Employee.Name</span>
                                        <br><small class="text-muted">@account.Employee.EmployeeCode</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未绑定</span>
                                    }
                                </td>
                                <td>
                                    @switch (account.PermissionLevel)
                                    {
                                        case "Admin":
                                            <span class="badge bg-danger">管理员</span>
                                            break;
                                        case "Operator":
                                            <span class="badge bg-primary">操作员</span>
                                            break;
                                        case "Viewer":
                                            <span class="badge bg-secondary">查看者</span>
                                            break;
                                        default:
                                            <span class="badge bg-light text-dark">@account.PermissionLevel</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @if (account.IsActive)
                                    {
                                        <span class="badge bg-success">活跃</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">禁用</span>
                                    }
                                </td>
                                <td>
                                    @if (account.NextPasswordChangeAt.HasValue)
                                    {
                                        var daysUntilChange = (account.NextPasswordChangeAt.Value - DateTime.Now).Days;
                                        if (daysUntilChange <= 0)
                                        {
                                            <span class="badge bg-danger">已过期</span>
                                        }
                                        else if (daysUntilChange <= 7)
                                        {
                                            <span class="badge bg-warning">@daysUntilChange 天后</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">@daysUntilChange 天后</span>
                                        }
                                        <br><small class="text-muted">@account.NextPasswordChangeAt.Value.ToString("yyyy-MM-dd")</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">未设置</span>
                                    }
                                </td>
                                <td>@(account.UpdatedAt?.ToString("yyyy-MM-dd HH:mm") ?? "未更新")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-page="./Details" asp-route-id="@account.Id" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="./Edit" asp-route-id="@account.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="./ChangePassword" asp-route-id="@account.Id" class="btn btn-sm btn-outline-warning">
                                            <i class="bi bi-key"></i>
                                        </a>
                                        @if (Model.IsSuperAdmin || Model.IsTechnician)
                                        {
                                            <a asp-page="./Delete" asp-route-id="@account.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-person-badge" style="font-size: 3rem; color: #ccc;"></i>
                <p class="text-muted mt-2">暂无账户数据</p>
                <a asp-page="./Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加第一个账户
                </a>
            </div>
        }
    </div>
</div>

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.ActiveAccountsCount</h5>
                <p class="card-text">活跃账户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.PendingPasswordChanges</h5>
                <p class="card-text">待更新密码</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@Model.BoundAccountsCount</h5>
                <p class="card-text">已绑定员工</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.TotalAccountsCount</h5>
                <p class="card-text">总账户数</p>
            </div>
        </div>
    </div>
</div>
