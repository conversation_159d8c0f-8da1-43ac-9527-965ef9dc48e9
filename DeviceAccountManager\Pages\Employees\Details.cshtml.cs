using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Employees
{
    public class DetailsModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public DetailsModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public Employee Employee { get; set; } = default!;
        public IList<Account> RelatedAccounts { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees.FirstOrDefaultAsync(m => m.Id == id);
            if (employee == null)
            {
                return NotFound();
            }

            Employee = employee;

            // 获取关联的账户
            RelatedAccounts = await _context.Accounts
                .Include(a => a.Device)
                .Where(a => a.EmployeeId == id)
                .OrderBy(a => a.Username)
                .ToListAsync();

            return Page();
        }
    }
}
