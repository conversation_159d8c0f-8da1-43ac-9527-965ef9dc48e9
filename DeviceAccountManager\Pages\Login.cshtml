@page
@model DeviceAccountManager.Pages.LoginModel
@{
    ViewData["Title"] = "用户登录";
    Layout = null;
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 设备账号权限管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
            letter-spacing: 1px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <i class="bi bi-shield-lock text-primary" style="font-size: 3rem;"></i>
            <h2>设备账号权限管理系统</h2>
            <p>请输入您的登录凭据</p>
        </div>

        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
        {
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i>
                @Model.ErrorMessage
            </div>
        }

        <form method="post">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" asp-for="Username" placeholder="用户名" required>
                <label for="username"><i class="bi bi-person"></i> 用户名</label>
                <span asp-validation-for="Username" class="text-danger"></span>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" asp-for="Password" placeholder="密码" required>
                <label for="password"><i class="bi bi-lock"></i> 密码</label>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </button>
            </div>
        </form>

        <div class="text-center mt-3">
            <small class="text-muted">
                <i class="bi bi-info-circle"></i>
                如有问题请联系系统管理员
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
