@echo off
echo ========================================
echo 生产设备账号权限管理系统 - 部署脚本
echo ========================================
echo.

:: 设置变量
set APP_NAME=DeviceAccountManager
set DEPLOY_PATH=C:\Applications\%APP_NAME%
set BACKUP_PATH=C:\Backup\%APP_NAME%_%date:~0,4%%date:~5,2%%date:~8,2%
set SERVICE_NAME=DeviceAccountManager

echo 开始部署 %APP_NAME%...
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    pause
    exit /b 1
)

:: 停止服务（如果存在）
echo 正在停止服务...
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    net stop %SERVICE_NAME%
    echo 服务已停止
) else (
    echo 服务不存在，跳过停止步骤
)
echo.

:: 创建备份
if exist "%DEPLOY_PATH%" (
    echo 正在备份现有版本...
    if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"
    xcopy "%DEPLOY_PATH%" "%BACKUP_PATH%" /E /I /Y >nul
    echo 备份完成: %BACKUP_PATH%
    echo.
)

:: 创建部署目录
echo 正在创建部署目录...
if not exist "%DEPLOY_PATH%" mkdir "%DEPLOY_PATH%"
if not exist "%DEPLOY_PATH%\logs" mkdir "%DEPLOY_PATH%\logs"
if not exist "%DEPLOY_PATH%\backups" mkdir "%DEPLOY_PATH%\backups"
echo 目录创建完成
echo.

:: 复制应用程序文件
echo 正在复制应用程序文件...
xcopy "publish\*" "%DEPLOY_PATH%" /E /Y >nul
if %errorLevel% neq 0 (
    echo 错误: 文件复制失败
    pause
    exit /b 1
)
echo 文件复制完成
echo.

:: 设置文件权限
echo 正在设置文件权限...
icacls "%DEPLOY_PATH%" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul
icacls "%DEPLOY_PATH%" /grant "Users:(OI)(CI)RX" /T >nul
echo 权限设置完成
echo.

:: 检查配置文件
echo 正在检查配置文件...
if not exist "%DEPLOY_PATH%\appsettings.json" (
    echo 警告: appsettings.json 不存在，请手动配置
) else (
    echo 配置文件存在
)
echo.

:: 运行数据库迁移
echo 正在运行数据库迁移...
cd /d "%DEPLOY_PATH%"
dotnet %APP_NAME%.dll --migrate
if %errorLevel% neq 0 (
    echo 警告: 数据库迁移可能失败，请检查数据库连接
) else (
    echo 数据库迁移完成
)
echo.

:: 创建或更新 Windows 服务
echo 正在配置 Windows 服务...
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo 服务已存在，正在更新配置...
    sc config %SERVICE_NAME% binPath= "\"%DEPLOY_PATH%\%APP_NAME%.exe\""
) else (
    echo 正在创建新服务...
    sc create %SERVICE_NAME% binPath= "\"%DEPLOY_PATH%\%APP_NAME%.exe\"" DisplayName= "设备账号权限管理系统" start= auto
)

if %errorLevel% neq 0 (
    echo 警告: 服务配置失败，可能需要手动配置
) else (
    echo 服务配置完成
)
echo.

:: 配置防火墙规则
echo 正在配置防火墙规则...
netsh advfirewall firewall show rule name="%APP_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    netsh advfirewall firewall add rule name="%APP_NAME%" dir=in action=allow protocol=TCP localport=5000
    echo 防火墙规则已添加
) else (
    echo 防火墙规则已存在
)
echo.

:: 启动服务
echo 正在启动服务...
net start %SERVICE_NAME%
if %errorLevel% neq 0 (
    echo 警告: 服务启动失败，请检查配置
    echo 您可以手动启动应用程序: cd "%DEPLOY_PATH%" && dotnet %APP_NAME%.dll
) else (
    echo 服务启动成功
)
echo.

:: 等待服务启动
echo 等待服务启动...
timeout /t 10 /nobreak >nul

:: 健康检查
echo 正在进行健康检查...
curl -s http://localhost:5000/api/system/health >nul 2>&1
if %errorLevel% equ 0 (
    echo 健康检查通过 - 系统运行正常
) else (
    echo 警告: 健康检查失败，请检查系统状态
)
echo.

echo ========================================
echo 部署完成！
echo ========================================
echo 应用程序路径: %DEPLOY_PATH%
echo 访问地址: http://localhost:5000
echo 默认管理员: admin / Admin123!
echo 备份位置: %BACKUP_PATH%
echo ========================================
echo.
echo 请注意:
echo 1. 首次登录后请立即修改默认密码
echo 2. 配置邮件服务器设置
echo 3. 检查系统配置是否正确
echo 4. 定期备份数据库
echo.

pause
