@echo off
echo ================================
echo Creating Migration Package
echo ================================
echo.

set PACKAGE_NAME=DeviceAccountManager_Migration_Package
set TEMP_DIR=%TEMP%\%PACKAGE_NAME%
set CURRENT_DIR=%~dp0
set APP_ROOT=%CURRENT_DIR%..

echo Creating migration package...
echo.

if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo [1/4] Copying application files...
mkdir "%TEMP_DIR%\Application"
if exist "%APP_ROOT%\publish" (
    xcopy "%APP_ROOT%\publish\*" "%TEMP_DIR%\Application\" /E /I /Y >nul
    echo Application files copied
) else (
    echo Warning: Publish folder not found
)
echo.

echo [2/4] Backing up database...
mkdir "%TEMP_DIR%\Database"

sqlcmd -S .\SQLEXPRESS -E -Q "BACKUP DATABASE [DeviceAccountManagerDb] TO DISK = '%TEMP_DIR%\Database\DeviceAccountManagerDb.bak' WITH FORMAT, INIT"

if %errorLevel% neq 0 (
    echo Warning: Database backup failed
    echo Creating data export instead...
    sqlcmd -S .\SQLEXPRESS -E -d DeviceAccountManagerDb -Q "SELECT * FROM Users" -o "%TEMP_DIR%\Database\users_export.txt"
    echo Data exported to text files
) else (
    echo Database backup completed
)
echo.

echo [3/4] Copying scripts...
mkdir "%TEMP_DIR%\Scripts"
xcopy "%CURRENT_DIR%*" "%TEMP_DIR%\Scripts\" /Y >nul
echo Scripts copied
echo.

echo [4/4] Creating installation script...
echo @echo off > "%TEMP_DIR%\INSTALL.bat"
echo echo Installing Device Account Manager... >> "%TEMP_DIR%\INSTALL.bat"
echo if not exist "C:\Applications" mkdir "C:\Applications" >> "%TEMP_DIR%\INSTALL.bat"
echo xcopy "Application\*" "C:\Applications\DeviceAccountManager\" /E /I /Y >> "%TEMP_DIR%\INSTALL.bat"
echo xcopy "Scripts\*" "C:\Applications\DeviceAccountManager\Scripts\" /E /I /Y >> "%TEMP_DIR%\INSTALL.bat"
echo echo Installation completed >> "%TEMP_DIR%\INSTALL.bat"
echo pause >> "%TEMP_DIR%\INSTALL.bat"

echo Installation script created
echo.

echo Creating ZIP package...
set ZIP_FILE=%CURRENT_DIR%DeviceAccountManager_Migration_Package.zip

powershell -command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_FILE%' -Force"

if %errorLevel% neq 0 (
    echo Error: Failed to create ZIP package
    echo Files are available in: %TEMP_DIR%
) else (
    echo ZIP package created: %ZIP_FILE%
    rmdir /s /q "%TEMP_DIR%"
)

echo.
echo Migration package creation completed!
echo Package location: %ZIP_FILE%
echo.
echo Next steps:
echo 1. Copy the ZIP file to the new computer
echo 2. Extract and run INSTALL.bat as Administrator
echo 3. Run restore_database.bat
echo 4. Start the application
echo.
pause
