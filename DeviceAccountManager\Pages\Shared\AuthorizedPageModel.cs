using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Filters;

namespace DeviceAccountManager.Pages.Shared
{
    public class AuthorizedPageModel : PageModel
    {
        public int CurrentUserId => int.Parse(HttpContext.Session.GetString("UserId") ?? "0");
        public string CurrentUsername => HttpContext.Session.GetString("Username") ?? "";
        public string CurrentRealName => HttpContext.Session.GetString("RealName") ?? "";
        public string CurrentRole => HttpContext.Session.GetString("Role") ?? "";

        public bool IsLoggedIn => !string.IsNullOrEmpty(HttpContext.Session.GetString("UserId"));
        public bool IsSuperAdmin => CurrentRole == "SuperAdmin";
        public bool IsTechnician => CurrentRole == "Technician" || IsSuperAdmin;
        public bool IsMaintainer => CurrentRole == "Maintainer" || IsTechnician;
        public bool IsOperator => CurrentRole == "Operator" || IsMaintainer;

        public override void OnPageHandlerExecuting(PageHandlerExecutingContext context)
        {
            if (!IsLoggedIn)
            {
                context.Result = RedirectToPage("/Login");
                return;
            }
            base.OnPageHandlerExecuting(context);
        }

        protected bool HasPermission(string requiredRole)
        {
            return requiredRole switch
            {
                "SuperAdmin" => IsSuperAdmin,
                "Technician" => IsTechnician,
                "Maintainer" => IsMaintainer,
                "Operator" => IsOperator,
                _ => false
            };
        }

        protected IActionResult ForbiddenResult()
        {
            return new ForbidResult();
        }
    }
}
