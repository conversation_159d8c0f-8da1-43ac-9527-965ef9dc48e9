using Microsoft.AspNetCore.Mvc;
using DeviceAccountManager.Models;
using DeviceAccountManager.Services;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.SystemManagement
{
    public class ConfigurationModel : AuthorizedPageModel
    {
        private readonly ISystemConfigurationService _configService;

        public ConfigurationModel(ISystemConfigurationService configService)
        {
            _configService = configService;
        }

        public List<SystemConfiguration> SystemConfigurations { get; set; } = new();
        public List<SystemConfiguration> PasswordConfigurations { get; set; } = new();
        public List<SystemConfiguration> EmailConfigurations { get; set; } = new();
        public List<SystemConfiguration> SecurityConfigurations { get; set; } = new();
        public List<SystemConfiguration> BackupConfigurations { get; set; } = new();

        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // 检查权限
            if (!IsSuperAdmin)
            {
                return ForbiddenResult();
            }

            await LoadConfigurationsAsync();
            return Page();
        }

        private async Task LoadConfigurationsAsync()
        {
            SystemConfigurations = await _configService.GetConfigurationsByCategoryAsync("System");
            PasswordConfigurations = await _configService.GetConfigurationsByCategoryAsync("Password");
            EmailConfigurations = await _configService.GetConfigurationsByCategoryAsync("Email");
            SecurityConfigurations = await _configService.GetConfigurationsByCategoryAsync("Security");
            BackupConfigurations = await _configService.GetConfigurationsByCategoryAsync("Backup");
        }
    }
}
