using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Accounts
{
    public class IndexModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public IndexModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public IList<Account> Accounts { get; set; } = default!;
        public IList<Device> Devices { get; set; } = default!;
        
        [BindProperty(SupportsGet = true)]
        public string? SearchString { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? DeviceFilter { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        public int ActiveAccountsCount { get; set; }
        public int PendingPasswordChanges { get; set; }
        public int BoundAccountsCount { get; set; }
        public int TotalAccountsCount { get; set; }

        public async Task OnGetAsync()
        {
            var accountsQuery = _context.Accounts
                .Include(a => a.Device)
                .Include(a => a.Employee)
                .AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(SearchString))
            {
                accountsQuery = accountsQuery.Where(a =>
                    a.Username.Contains(SearchString) ||
                    (a.Device != null && (a.Device.DeviceCode.Contains(SearchString) || a.Device.DeviceName.Contains(SearchString))) ||
                    (a.Employee != null && (a.Employee.Name.Contains(SearchString) || a.Employee.EmployeeCode.Contains(SearchString))));
            }

            // 设备过滤
            if (!string.IsNullOrEmpty(DeviceFilter) && int.TryParse(DeviceFilter, out int deviceId))
            {
                accountsQuery = accountsQuery.Where(a => a.DeviceId == deviceId);
            }

            // 状态过滤
            if (!string.IsNullOrEmpty(StatusFilter) && bool.TryParse(StatusFilter, out bool isActive))
            {
                accountsQuery = accountsQuery.Where(a => a.IsActive == isActive);
            }

            Accounts = await accountsQuery
                .OrderBy(a => a.Username)
                .ToListAsync();

            // 获取所有设备用于筛选下拉框
            Devices = await _context.Devices
                .Where(d => d.Status == "Active")
                .OrderBy(d => d.DeviceCode)
                .ToListAsync();

            // 统计数据
            ActiveAccountsCount = await _context.Accounts.CountAsync(a => a.IsActive);
            PendingPasswordChanges = await _context.Accounts
                .CountAsync(a => a.IsActive && !a.IsExemptFromPasswordChange && 
                           a.NextPasswordChangeAt.HasValue && a.NextPasswordChangeAt <= DateTime.Now);
            BoundAccountsCount = await _context.Accounts.CountAsync(a => a.IsActive && a.EmployeeId.HasValue);
            TotalAccountsCount = await _context.Accounts.CountAsync();
        }
    }
}
