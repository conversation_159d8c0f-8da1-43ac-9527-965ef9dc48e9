﻿@page
@model IndexModel
@{
    ViewData["Title"] = "仪表板";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">仪表板</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            活跃设备
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalDevices</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cpu fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            管理账户
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalAccounts</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-badge fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            员工数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalEmployees</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            待更新密码
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.PendingPasswordChanges</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-key fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="/Devices/Create" class="btn btn-primary btn-block">
                            <i class="bi bi-plus-circle"></i> 添加设备
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/Accounts/Create" class="btn btn-success btn-block">
                            <i class="bi bi-person-plus"></i> 添加账户
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/Employees/Create" class="btn btn-info btn-block">
                            <i class="bi bi-people"></i> 添加员工
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/Passwords/BatchUpdate" class="btn btn-warning btn-block">
                            <i class="bi bi-arrow-repeat"></i> 批量更新密码
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="small text-gray-500">最近7天邮件发送</div>
                    <div class="h5 mb-0">@Model.RecentEmailsSent 封</div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">系统运行状态</div>
                    <div class="h5 mb-0 text-success">
                        <i class="bi bi-check-circle"></i> 正常
                    </div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">当前用户</div>
                    <div class="h5 mb-0">@Model.CurrentRealName (@Model.CurrentRole)</div>
                </div>
            </div>
        </div>
    </div>
</div>
