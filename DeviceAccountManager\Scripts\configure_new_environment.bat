@echo off
echo ================================
echo   Environment Configuration
echo ================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Warning: Running without Administrator privileges
    echo Some configurations may fail
    echo.
)

set CURRENT_DIR=%~dp0
set APP_DIR=%CURRENT_DIR%..
set CONFIG_FILE=%APP_DIR%\appsettings.json

echo Configuring application for new environment...
echo.

:: 1. 检查.NET Runtime
echo [1/7] Checking .NET Runtime...
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: .NET Runtime not found
    echo Please install .NET 10.0 Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/10.0
    pause
    exit /b 1
) else (
    echo .NET Runtime found: 
    dotnet --version
)
echo.

:: 2. 检查SQL Server
echo [2/7] Checking SQL Server...
sqlcmd -S .\SQLEXPRESS -E -Q "SELECT @@VERSION" >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: SQL Server Express not accessible
    echo Please ensure SQL Server Express is installed and running
    pause
    exit /b 1
) else (
    echo SQL Server Express is accessible
)
echo.

:: 3. 检查端口可用性
echo [3/7] Checking port availability...
netstat -an | findstr :5000 >nul
if %errorLevel% equ 0 (
    echo Warning: Port 5000 is already in use
    echo You may need to change the port in appsettings.json
) else (
    echo Port 5000 is available
)
echo.

:: 4. 配置防火墙
echo [4/7] Configuring firewall...
netsh advfirewall firewall show rule name="DeviceAccountManager" >nul 2>&1
if %errorLevel% neq 0 (
    echo Adding firewall rule...
    netsh advfirewall firewall add rule name="DeviceAccountManager" dir=in action=allow protocol=TCP localport=5000
    if %errorLevel% equ 0 (
        echo Firewall rule added successfully
    ) else (
        echo Warning: Failed to add firewall rule
    )
) else (
    echo Firewall rule already exists
)
echo.

:: 5. 设置文件权限
echo [5/7] Setting file permissions...
if exist "%APP_DIR%" (
    icacls "%APP_DIR%" /grant "Users:(OI)(CI)RX" /T >nul 2>&1
    icacls "%APP_DIR%" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
    echo File permissions configured
) else (
    echo Warning: Application directory not found
)
echo.

:: 6. 创建日志目录
echo [6/7] Creating log directory...
if not exist "%APP_DIR%\logs" (
    mkdir "%APP_DIR%\logs"
    echo Log directory created
) else (
    echo Log directory already exists
)
echo.

:: 7. 验证配置文件
echo [7/7] Verifying configuration...
if exist "%CONFIG_FILE%" (
    echo Configuration file found: %CONFIG_FILE%
    
    :: 检查配置文件内容
    findstr "DefaultConnection" "%CONFIG_FILE%" >nul
    if %errorLevel% equ 0 (
        echo Database connection string found
    ) else (
        echo Warning: Database connection string not found
    )
    
    findstr "EmailSettings" "%CONFIG_FILE%" >nul
    if %errorLevel% equ 0 (
        echo Email settings found
    ) else (
        echo Warning: Email settings not found
    )
) else (
    echo Error: Configuration file not found
    echo Creating default configuration...
    
    echo { > "%CONFIG_FILE%"
    echo   "ConnectionStrings": { >> "%CONFIG_FILE%"
    echo     "DefaultConnection": "Server=.\\SQLEXPRESS;Database=DeviceAccountManagerDb;Trusted_Connection=true;TrustServerCertificate=true;" >> "%CONFIG_FILE%"
    echo   }, >> "%CONFIG_FILE%"
    echo   "Logging": { >> "%CONFIG_FILE%"
    echo     "LogLevel": { >> "%CONFIG_FILE%"
    echo       "Default": "Information", >> "%CONFIG_FILE%"
    echo       "Microsoft.AspNetCore": "Warning" >> "%CONFIG_FILE%"
    echo     } >> "%CONFIG_FILE%"
    echo   }, >> "%CONFIG_FILE%"
    echo   "AllowedHosts": "*", >> "%CONFIG_FILE%"
    echo   "Urls": "http://localhost:5000" >> "%CONFIG_FILE%"
    echo } >> "%CONFIG_FILE%"
    
    echo Default configuration created
)
echo.

:: 8. 测试应用程序启动
echo Testing application startup...
cd /d "%APP_DIR%"
timeout /t 2 /nobreak >nul

echo.
echo ================================
echo Configuration completed!
echo.
echo Application directory: %APP_DIR%
echo Configuration file: %CONFIG_FILE%
echo.
echo To start the application:
echo   cd "%APP_DIR%"
echo   dotnet DeviceAccountManager.dll
echo.
echo To access the system:
echo   Open browser: http://localhost:5000
echo   Login: admin / Admin123!
echo.
echo To install as Windows service:
echo   Run: %CURRENT_DIR%\install_service.bat
echo.
pause
