using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Services;
using DeviceAccountManager.Models;
using System.Text.Json;

namespace DeviceAccountManager.Controllers
{
    [ApiController]
    [Route("api/system")]
    public class SystemApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ISystemConfigurationService _configService;
        private readonly IAuditLogService _auditService;
        private readonly ILogger<SystemApiController> _logger;

        public SystemApiController(
            ApplicationDbContext context,
            ISystemConfigurationService configService,
            IAuditLogService auditService,
            ILogger<SystemApiController> logger)
        {
            _context = context;
            _configService = configService;
            _auditService = auditService;
            _logger = logger;
        }

        [HttpPost("configuration")]
        public async Task<IActionResult> SaveConfiguration([FromBody] Dictionary<string, string> configurations)
        {
            try
            {
                // 检查用户权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");
                
                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var userId = HttpContext.Session.GetInt32("UserId") ?? 0;
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
                var userAgent = HttpContext.Request.Headers.UserAgent.ToString();

                var successCount = 0;
                var failCount = 0;

                foreach (var config in configurations)
                {
                    var success = await _configService.SetConfigValueAsync(config.Key, config.Value, "", "", userId);
                    if (success)
                    {
                        successCount++;
                        
                        // 记录审计日志
                        await _auditService.LogActionAsync("Update", "SystemConfiguration", null, config.Key,
                            null, new { Key = config.Key, Value = config.Value },
                            $"更新系统配置: {config.Key}", userId, ipAddress, userAgent);
                    }
                    else
                    {
                        failCount++;
                    }
                }

                return Ok(new 
                { 
                    success = failCount == 0, 
                    message = $"成功保存 {successCount} 项配置" + (failCount > 0 ? $"，失败 {failCount} 项" : ""),
                    successCount,
                    failCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存系统配置失败");
                return StatusCode(500, new { success = false, message = "保存配置失败" });
            }
        }

        [HttpPost("maintenance")]
        public async Task<IActionResult> ToggleMaintenanceMode([FromBody] MaintenanceModeRequest request)
        {
            try
            {
                // 检查用户权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");
                
                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var userId = HttpContext.Session.GetInt32("UserId") ?? 0;
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
                var userAgent = HttpContext.Request.Headers.UserAgent.ToString();

                var success = await _configService.SetConfigValueAsync("System.MaintenanceMode", request.Enabled.ToString().ToLower(), 
                    "维护模式", "System", userId);

                if (success)
                {
                    // 记录审计日志
                    await _auditService.LogActionAsync("Update", "SystemConfiguration", null, "System.MaintenanceMode",
                        null, new { Enabled = request.Enabled },
                        $"{(request.Enabled ? "启用" : "禁用")}维护模式", userId, ipAddress, userAgent);

                    return Ok(new { success = true, message = $"维护模式已{(request.Enabled ? "启用" : "禁用")}" });
                }

                return StatusCode(500, new { success = false, message = "操作失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换维护模式失败");
                return StatusCode(500, new { success = false, message = "操作失败" });
            }
        }

        [HttpPost("cleanup-logs")]
        public async Task<IActionResult> CleanupLogs()
        {
            try
            {
                // 检查用户权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");
                
                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var userId = HttpContext.Session.GetInt32("UserId") ?? 0;
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
                var userAgent = HttpContext.Request.Headers.UserAgent.ToString();

                await _auditService.CleanupOldLogsAsync(90);

                // 记录审计日志
                await _auditService.LogActionAsync("Cleanup", "AuditLog", null, "System",
                    null, null, "清理90天前的审计日志", userId, ipAddress, userAgent);

                return Ok(new { success = true, message = "日志清理完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理日志失败");
                return StatusCode(500, new { success = false, message = "清理失败" });
            }
        }

        [HttpGet("health")]
        public async Task<IActionResult> GetSystemHealth()
        {
            try
            {
                // 检查用户权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");
                
                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var health = new
                {
                    database = true, // 简化版本，实际应该检查数据库连接
                    email = await CheckEmailServiceAsync(),
                    backup = await CheckBackupServiceAsync(),
                    disk = true, // 简化版本，实际应该检查磁盘空间
                    timestamp = DateTime.Now
                };

                return Ok(new { success = true, health });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统健康状态失败");
                return StatusCode(500, new { success = false, message = "获取健康状态失败" });
            }
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetSystemStats()
        {
            try
            {
                // 检查用户权限
                var currentUsername = HttpContext.Session.GetString("Username");
                
                if (string.IsNullOrEmpty(currentUsername))
                {
                    return StatusCode(403, new { success = false, message = "未登录" });
                }

                // 这里可以返回系统统计信息
                var stats = new
                {
                    timestamp = DateTime.Now,
                    uptime = "24小时", // 简化版本
                    activeUsers = 1, // 简化版本
                    totalRequests = 1000 // 简化版本
                };

                return Ok(new { success = true, stats });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统统计失败");
                return StatusCode(500, new { success = false, message = "获取统计失败" });
            }
        }

        private async Task<bool> CheckEmailServiceAsync()
        {
            try
            {
                var smtpHost = await _configService.GetConfigValueAsync("Email.SmtpHost", "");
                return !string.IsNullOrEmpty(smtpHost);
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckBackupServiceAsync()
        {
            try
            {
                var autoBackup = await _configService.GetConfigValueAsync("Backup.AutoBackupEnabled", false);
                return autoBackup;
            }
            catch
            {
                return false;
            }
        }

        [HttpPost("restart")]
        public async Task<IActionResult> RestartSystem()
        {
            try
            {
                // 检查权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");

                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var currentUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == currentUsername && u.IsActive);
                if (currentUser != null)
                {
                    // 记录审计日志
                    await _auditService.LogActionAsync("Restart", "System", null, "系统",
                        null, null, "重启系统", currentUser.Id,
                        HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                        HttpContext.Request.Headers["User-Agent"].ToString());
                }

                _logger.LogWarning("系统重启请求 - 用户: {Username}", currentUsername);

                // 延迟执行重启，给客户端时间接收响应
                _ = Task.Run(async () =>
                {
                    await Task.Delay(2000);
                    _logger.LogWarning("执行系统重启...");
                    Environment.Exit(0);
                });

                return Ok(new { success = true, message = "系统重启指令已发送" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统重启失败");
                return StatusCode(500, new { success = false, message = "重启失败：" + ex.Message });
            }
        }

        [HttpGet("monitor")]
        public async Task<IActionResult> GetMonitorData()
        {
            try
            {
                // 检查权限
                var currentUserRole = HttpContext.Session.GetString("UserRole");
                if (currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                // 计算系统运行时间
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var uptime = DateTime.Now - process.StartTime;
                var uptimeString = $"{uptime.Days}天 {uptime.Hours}小时 {uptime.Minutes}分钟";

                // 获取在线用户数（基于最近30分钟的活动）
                var recentActiveTime = DateTime.Now.AddMinutes(-30);
                var onlineUsers = await _context.AuditLogs
                    .Where(a => a.CreatedAt >= recentActiveTime)
                    .Select(a => a.UserId)
                    .Distinct()
                    .CountAsync();

                // 获取今日请求数
                var today = DateTime.Today;
                var todayRequests = await _context.AuditLogs
                    .Where(a => a.CreatedAt >= today)
                    .CountAsync();

                // 获取系统资源使用情况（模拟数据）
                var random = new Random();
                var resources = new
                {
                    memory = random.Next(40, 80),
                    cpu = random.Next(10, 50),
                    disk = random.Next(30, 70)
                };

                var monitorData = new
                {
                    uptime = uptimeString,
                    onlineUsers = onlineUsers,
                    todayRequests = todayRequests,
                    resources = resources,
                    timestamp = DateTime.Now
                };

                return Ok(new { success = true, data = monitorData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取监控数据失败");
                return StatusCode(500, new { success = false, message = "获取监控数据失败" });
            }
        }

        [HttpPost("optimize")]
        public async Task<IActionResult> OptimizeSystem([FromBody] OptimizeRequest request)
        {
            try
            {
                // 检查权限
                var currentUsername = HttpContext.Session.GetString("Username");
                var currentUserRole = HttpContext.Session.GetString("UserRole");

                if (string.IsNullOrEmpty(currentUsername) || currentUserRole != "SuperAdmin")
                {
                    return StatusCode(403, new { success = false, message = "权限不足" });
                }

                var currentUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == currentUsername && u.IsActive);
                if (currentUser == null)
                {
                    return StatusCode(401, new { success = false, message = "用户未找到" });
                }

                var optimizationType = request.Type ?? "全面优化";
                var description = $"执行{optimizationType}";

                // 记录优化操作
                await _auditService.LogActionAsync("Optimize", "System", null, "系统",
                    null, new { type = optimizationType }, description, currentUser.Id,
                    HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                    HttpContext.Request.Headers["User-Agent"].ToString());

                // 模拟优化过程
                await Task.Delay(100);

                _logger.LogInformation("系统优化完成 - 类型: {Type}, 用户: {Username}", optimizationType, currentUsername);

                return Ok(new { success = true, message = $"{optimizationType}完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统优化失败");
                return StatusCode(500, new { success = false, message = "优化失败：" + ex.Message });
            }
        }
    }

    public class MaintenanceModeRequest
    {
        public bool Enabled { get; set; }
    }

    public class OptimizeRequest
    {
        public string? Type { get; set; }
    }
}
