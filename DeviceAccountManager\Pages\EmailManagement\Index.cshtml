@page
@model DeviceAccountManager.Pages.EmailManagement.IndexModel
@{
    ViewData["Title"] = "邮件管理";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">邮件管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" onclick="processEmails()">
                <i class="bi bi-send"></i> 处理待发邮件
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
            <a asp-page="./Settings" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-gear"></i> 邮件设置
            </a>
        </div>
    </div>
</div>

<!-- 邮件统计 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总邮件数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalEmails</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-envelope fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            发送成功
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.SentEmails</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            待发送
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.PendingEmails</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            发送失败
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.FailedEmails</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-x-circle fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">筛选和搜索</h6>
    </div>
    <div class="card-body">
        <form method="get">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">邮件状态</label>
                    <select name="status" class="form-select" onchange="this.form.submit()">
                        <option value="">全部状态</option>
                        <option value="Pending" selected="@(Model.StatusFilter == "Pending")">待发送</option>
                        <option value="Sent" selected="@(Model.StatusFilter == "Sent")">已发送</option>
                        <option value="Failed" selected="@(Model.StatusFilter == "Failed")">发送失败</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">邮件类型</label>
                    <select name="emailType" class="form-select" onchange="this.form.submit()">
                        <option value="">全部类型</option>
                        <option value="PasswordChanged" selected="@(Model.EmailTypeFilter == "PasswordChanged")">密码更改</option>
                        <option value="PasswordExpiring" selected="@(Model.EmailTypeFilter == "PasswordExpiring")">密码到期提醒</option>
                        <option value="AccountCreated" selected="@(Model.EmailTypeFilter == "AccountCreated")">账户创建</option>
                        <option value="AccountDisabled" selected="@(Model.EmailTypeFilter == "AccountDisabled")">账户禁用</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">搜索</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="搜索收件人或主题..." value="@Model.SearchTerm">
                        <button type="submit" class="btn btn-outline-secondary">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="@Url.Page("./Index")" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-counterclockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 邮件列表 -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">邮件记录</h6>
    </div>
    <div class="card-body">
        @if (Model.EmailLogs.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>收件人</th>
                            <th>主题</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>发送时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var email in Model.EmailLogs)
                        {
                            <tr class="@(email.Status == "Failed" ? "table-danger" : email.Status == "Sent" ? "table-success" : "table-warning")">
                                <td>
                                    <div>
                                        <strong>@email.ToName</strong>
                                        <br>
                                        <small class="text-muted">@email.ToEmail</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="@email.Subject">
                                        @email.Subject
                                    </div>
                                </td>
                                <td>
                                    @switch (email.EmailType)
                                    {
                                        case "PasswordChanged":
                                            <span class="badge bg-info">密码更改</span>
                                            break;
                                        case "PasswordExpiring":
                                            <span class="badge bg-warning">密码到期</span>
                                            break;
                                        case "AccountCreated":
                                            <span class="badge bg-success">账户创建</span>
                                            break;
                                        case "AccountDisabled":
                                            <span class="badge bg-danger">账户禁用</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@email.EmailType</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    @switch (email.Status)
                                    {
                                        case "Pending":
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock"></i> 待发送
                                            </span>
                                            break;
                                        case "Sent":
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> 已发送
                                            </span>
                                            break;
                                        case "Failed":
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> 发送失败
                                            </span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@email.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <small>@email.CreatedAt.ToString("yyyy-MM-dd HH:mm")</small>
                                </td>
                                <td>
                                    @if (email.SentAt.HasValue)
                                    {
                                        <small>@email.SentAt.Value.ToString("yyyy-MM-dd HH:mm")</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-page="./Details" asp-route-id="@email.Id" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if (email.Status == "Failed" || email.Status == "Pending")
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="resendEmail(@email.Id)">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="邮件分页">
                    <ul class="pagination justify-content-center">
                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Page("./Index", new { page = Model.CurrentPage - 1, status = Model.StatusFilter, emailType = Model.EmailTypeFilter, search = Model.SearchTerm })">上一页</a>
                            </li>
                        }

                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" href="@Url.Page("./Index", new { page = i, status = Model.StatusFilter, emailType = Model.EmailTypeFilter, search = Model.SearchTerm })">@i</a>
                            </li>
                        }

                        @if (Model.CurrentPage < Model.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" href="@Url.Page("./Index", new { page = Model.CurrentPage + 1, status = Model.StatusFilter, emailType = Model.EmailTypeFilter, search = Model.SearchTerm })">下一页</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-4">
                <i class="bi bi-envelope-x fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无邮件记录</h5>
                <p class="text-muted">当系统发送邮件时，记录将显示在这里。</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function processEmails() {
            if (confirm('确定要处理所有待发送的邮件吗？')) {
                fetch('/api/email/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('邮件处理完成！');
                        location.reload();
                    } else {
                        alert('邮件处理失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('处理邮件时发生错误');
                });
            }
        }

        function resendEmail(emailId) {
            if (confirm('确定要重新发送这封邮件吗？')) {
                fetch(`/api/email/resend/${emailId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('邮件重新发送成功！');
                        location.reload();
                    } else {
                        alert('邮件重新发送失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('重新发送邮件时发生错误');
                });
            }
        }

        function refreshPage() {
            location.reload();
        }

        // 自动刷新页面（每30秒）
        setInterval(function() {
            // 只有在没有用户交互时才自动刷新
            if (document.hidden === false) {
                const lastActivity = localStorage.getItem('lastActivity');
                const now = Date.now();
                if (!lastActivity || now - parseInt(lastActivity) > 30000) {
                    location.reload();
                }
            }
        }, 30000);

        // 记录用户活动
        document.addEventListener('click', function() {
            localStorage.setItem('lastActivity', Date.now().toString());
        });
    </script>
}
