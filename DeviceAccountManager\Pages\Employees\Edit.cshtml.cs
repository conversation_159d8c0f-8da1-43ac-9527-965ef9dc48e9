using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DeviceAccountManager.Data;
using DeviceAccountManager.Models;
using DeviceAccountManager.Pages.Shared;

namespace DeviceAccountManager.Pages.Employees
{
    public class EditModel : AuthorizedPageModel
    {
        private readonly ApplicationDbContext _context;

        public EditModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        public IList<string> ExistingDepartments { get; set; } = default!;
        public IList<string> ExistingPositions { get; set; } = default!;
        public int RelatedAccountsCount { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            // 检查权限：只有维护者及以上级别可以编辑员工
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees.FirstOrDefaultAsync(m => m.Id == id);
            if (employee == null)
            {
                return NotFound();
            }

            Employee = employee;
            await LoadSelectData();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // 检查权限
            if (!IsMaintainer)
            {
                return ForbiddenResult();
            }

            if (!ModelState.IsValid)
            {
                await LoadSelectData();
                return Page();
            }

            // 检查员工工号是否已被其他员工使用
            var existingEmployee = await _context.Employees
                .FirstOrDefaultAsync(e => e.EmployeeCode == Employee.EmployeeCode && e.Id != Employee.Id);
            
            if (existingEmployee != null)
            {
                ModelState.AddModelError("Employee.EmployeeCode", "员工工号已存在，请使用其他工号");
                await LoadSelectData();
                return Page();
            }

            // 检查邮箱是否已被其他员工使用
            if (!string.IsNullOrEmpty(Employee.Email))
            {
                var existingEmailEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.Email == Employee.Email && e.Id != Employee.Id);
                
                if (existingEmailEmployee != null)
                {
                    ModelState.AddModelError("Employee.Email", "邮箱地址已被其他员工使用");
                    await LoadSelectData();
                    return Page();
                }
            }

            // 更新时间
            Employee.UpdatedAt = DateTime.Now;

            _context.Attach(Employee).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();

                // 记录操作日志
                var log = new OperationLog
                {
                    UserId = CurrentUserId,
                    Operation = "Update",
                    TargetType = "Employee",
                    TargetId = Employee.Id.ToString(),
                    Description = $"编辑员工：{Employee.Name} ({Employee.EmployeeCode})",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                    UserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    CreatedAt = DateTime.Now
                };
                _context.OperationLogs.Add(log);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmployeeExists(Employee.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return RedirectToPage("./Index");
        }

        private bool EmployeeExists(int id)
        {
            return _context.Employees.Any(e => e.Id == id);
        }

        private async Task LoadSelectData()
        {
            // 获取现有部门列表
            ExistingDepartments = await _context.Employees
                .Where(e => !string.IsNullOrEmpty(e.Department))
                .Select(e => e.Department!)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            // 获取现有职位列表
            ExistingPositions = await _context.Employees
                .Where(e => !string.IsNullOrEmpty(e.Position))
                .Select(e => e.Position!)
                .Distinct()
                .OrderBy(p => p)
                .ToListAsync();

            // 获取关联账户数量
            RelatedAccountsCount = await _context.Accounts
                .CountAsync(a => a.EmployeeId == Employee.Id);
        }
    }
}
