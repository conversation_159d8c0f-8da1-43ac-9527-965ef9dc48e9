@echo off
echo ========================================
echo 生产设备账号权限管理系统 - 备份脚本
echo ========================================
echo.

:: 设置变量
set APP_NAME=DeviceAccountManager
set APP_PATH=C:\Applications\%APP_NAME%
set BACKUP_ROOT=C:\Backup
set DATE_STAMP=%date:~0,4%%date:~5,2%%date:~8,2%
set TIME_STAMP=%time:~0,2%%time:~3,2%%time:~6,2%
set TIME_STAMP=%TIME_STAMP: =0%
set BACKUP_NAME=%APP_NAME%_Backup_%DATE_STAMP%_%TIME_STAMP%
set BACKUP_PATH=%BACKUP_ROOT%\%BACKUP_NAME%

echo 开始备份 %APP_NAME%...
echo 备份时间: %date% %time%
echo 备份路径: %BACKUP_PATH%
echo.

:: 检查应用程序路径
if not exist "%APP_PATH%" (
    echo 错误: 应用程序路径不存在 - %APP_PATH%
    pause
    exit /b 1
)

:: 创建备份目录
echo 正在创建备份目录...
if not exist "%BACKUP_ROOT%" mkdir "%BACKUP_ROOT%"
if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"
mkdir "%BACKUP_PATH%\Application"
mkdir "%BACKUP_PATH%\Database"
mkdir "%BACKUP_PATH%\Logs"
echo 备份目录创建完成
echo.

:: 备份应用程序文件
echo 正在备份应用程序文件...
xcopy "%APP_PATH%\*" "%BACKUP_PATH%\Application\" /E /I /Y >nul
if %errorLevel% neq 0 (
    echo 警告: 应用程序文件备份可能不完整
) else (
    echo 应用程序文件备份完成
)
echo.

:: 备份数据库
echo 正在备份数据库...
set DB_BACKUP_FILE=%BACKUP_PATH%\Database\DeviceAccountManager_%DATE_STAMP%_%TIME_STAMP%.bak

:: 使用 sqlcmd 备份数据库
sqlcmd -S .\SQLEXPRESS -E -Q "BACKUP DATABASE [DeviceAccountManager] TO DISK = '%DB_BACKUP_FILE%' WITH FORMAT, INIT, NAME = 'DeviceAccountManager-Full Database Backup', SKIP, NOREWIND, NOUNLOAD, STATS = 10"

if %errorLevel% neq 0 (
    echo 警告: 数据库备份失败，请检查 SQL Server 连接
) else (
    echo 数据库备份完成: %DB_BACKUP_FILE%
)
echo.

:: 备份日志文件
echo 正在备份日志文件...
if exist "%APP_PATH%\logs" (
    xcopy "%APP_PATH%\logs\*" "%BACKUP_PATH%\Logs\" /E /I /Y >nul
    echo 日志文件备份完成
) else (
    echo 日志目录不存在，跳过日志备份
)
echo.

:: 创建备份信息文件
echo 正在创建备份信息文件...
set INFO_FILE=%BACKUP_PATH%\backup_info.txt
echo 备份信息 > "%INFO_FILE%"
echo ======================================== >> "%INFO_FILE%"
echo 备份名称: %BACKUP_NAME% >> "%INFO_FILE%"
echo 备份时间: %date% %time% >> "%INFO_FILE%"
echo 应用程序版本: [需要手动填写] >> "%INFO_FILE%"
echo 数据库版本: [需要手动填写] >> "%INFO_FILE%"
echo 备份类型: 完整备份 >> "%INFO_FILE%"
echo 备份大小: [计算中...] >> "%INFO_FILE%"
echo ======================================== >> "%INFO_FILE%"
echo 备份内容: >> "%INFO_FILE%"
echo - 应用程序文件 >> "%INFO_FILE%"
echo - 数据库文件 >> "%INFO_FILE%"
echo - 日志文件 >> "%INFO_FILE%"
echo - 配置文件 >> "%INFO_FILE%"
echo ======================================== >> "%INFO_FILE%"
echo 恢复说明: >> "%INFO_FILE%"
echo 1. 停止应用程序服务 >> "%INFO_FILE%"
echo 2. 恢复应用程序文件到原路径 >> "%INFO_FILE%"
echo 3. 使用 SQL Server 恢复数据库 >> "%INFO_FILE%"
echo 4. 检查配置文件设置 >> "%INFO_FILE%"
echo 5. 启动应用程序服务 >> "%INFO_FILE%"
echo ======================================== >> "%INFO_FILE%"

echo 备份信息文件创建完成
echo.

:: 计算备份大小
echo 正在计算备份大小...
for /f "tokens=3" %%a in ('dir "%BACKUP_PATH%" /s /-c ^| find "个文件"') do set BACKUP_SIZE=%%a
echo 备份大小: %BACKUP_SIZE% 字节 >> "%INFO_FILE%"
echo.

:: 压缩备份（可选）
set /p COMPRESS="是否压缩备份文件? (Y/N): "
if /i "%COMPRESS%"=="Y" (
    echo 正在压缩备份文件...
    powershell -command "Compress-Archive -Path '%BACKUP_PATH%' -DestinationPath '%BACKUP_ROOT%\%BACKUP_NAME%.zip' -Force"
    if %errorLevel% equ 0 (
        echo 备份文件已压缩: %BACKUP_ROOT%\%BACKUP_NAME%.zip
        set /p DELETE_ORIGINAL="是否删除原始备份文件夹? (Y/N): "
        if /i "%DELETE_ORIGINAL%"=="Y" (
            rmdir /s /q "%BACKUP_PATH%"
            echo 原始备份文件夹已删除
        )
    ) else (
        echo 警告: 备份压缩失败
    )
    echo.
)

:: 清理旧备份（保留最近7天）
echo 正在清理旧备份文件...
forfiles /p "%BACKUP_ROOT%" /m "%APP_NAME%_Backup_*" /d -7 /c "cmd /c del @path" 2>nul
if %errorLevel% equ 0 (
    echo 旧备份文件清理完成
) else (
    echo 没有需要清理的旧备份文件
)
echo.

:: 验证备份完整性
echo 正在验证备份完整性...
if exist "%BACKUP_PATH%\Application\%APP_NAME%.dll" (
    echo ✓ 应用程序文件备份完整
) else (
    echo ✗ 应用程序文件备份不完整
)

if exist "%DB_BACKUP_FILE%" (
    echo ✓ 数据库备份文件存在
) else (
    echo ✗ 数据库备份文件不存在
)

if exist "%BACKUP_PATH%\backup_info.txt" (
    echo ✓ 备份信息文件存在
) else (
    echo ✗ 备份信息文件不存在
)
echo.

:: 记录备份到系统日志
echo 正在记录备份日志...
curl -s -X POST "http://localhost:5000/api/system/log" ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"Backup\",\"description\":\"系统备份完成: %BACKUP_NAME%\",\"level\":\"Info\"}" >nul 2>&1

echo ========================================
echo 备份完成！
echo ========================================
echo 备份名称: %BACKUP_NAME%
echo 备份路径: %BACKUP_PATH%
echo 备份时间: %date% %time%
echo ========================================
echo.
echo 备份验证:
echo - 检查备份文件是否完整
echo - 测试数据库备份文件
echo - 验证配置文件设置
echo.
echo 建议:
echo - 定期测试备份恢复过程
echo - 将重要备份存储到异地
echo - 保持备份文件的安全性
echo.

pause
